const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Sichere Verbindung zwischen Hauptprozess und Renderer-Prozess
contextBridge.exposeInMainWorld('electron', {
  // Empfangen von Status-Updates
  onUpdateStatus: (callback) => {
    ipcRenderer.on('update-status', (event, data) => {
      console.log('Status-Update empfangen:', data);
      callback(data);
    });
  },

  // Für kritische Fehler
  onCriticalError: (callback) => {
    ipcRenderer.on('critical-error', (event, data) => {
      callback(data);
    });
  },

  // Für das Anzeigen des "Trotzdem fortfahren"-Buttons
  onShowContinueButton: (callback) => {
    ipcRenderer.on('show-continue-button', () => {
      console.log('Zeige "Trotzdem fortfahren"-Button an');
      callback();
    });
  },

  // <PERSON><PERSON><PERSON> den "Trotzdem fortfahren"-Button
  continueAnyway: () => {
    console.log('Sende continue-anyway Event an Hauptprozess');
    ipcRenderer.send('continue-anyway');
  }
});