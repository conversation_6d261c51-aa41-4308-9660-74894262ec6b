/**
 * ZVT-Konstanten und Status-Codes
 */
class ZVTConstants {
    constructor() {
      // ZVT-Konstanten
      this.ZVT = {
        // Befehlscodes
        CMD: {
          REGISTER: 0x06,
          PAYMENT: 0x04,
          STATUS: 0x05,
          END_OF_DAY: 0x50,
          ABORT: 0x1e,
          DISPLAY: 0xff,
          AUTHORIZATION: 0x0f,
        },
        // Antwortcodes
        RESPONSE: {
          SUCCESS: 0x80,
          ERROR: 0x84,
          INTERMEDIATE: 0x86,
          ABORT: 0x1e,
          RECEIPT: 0x0f,
        },
        // TLV-Tags
        TLV: {
          RECEIPT_CUSTOMER: 0x27,
          RECEIPT_MERCHANT: 0x28,
          RECEIPT_DATA: 0xd3,
          RESULT_CODE: 0x04,
          CARD_TYPE: 0x19,
          CARD_NUMBER: 0x22,
          CARD_EXPIRY: 0x0e,
          TRACE_NUMBER: 0x88,
          AUTH_CODE: 0x89,
          RECEIPT_ID: 0x87,
          EMV_DATA: 0x3c,
          TERMINAL_ID: 0x29,
          AMOUNT: 0x04,
          VU_NUMBER: 0x17,
        },
        // Spezielle Nachrichtenformate
        MESSAGE: {
          DISPLAY: [0x04, 0xff],
          AUTHORIZATION: [0x04, 0x0f],
          RECEIPT: [0x06, 0x0f, 0x00],
          ABORT: [0x06, 0x1e],
          SIMPLE_SUCCESS: [0x80, 0x00, 0x00],
          ERROR: [0x84],
        },
      };

      // Status-Zuordnungen für bekannte Fehlercodes
      this.statusCodes = {
        "00": "Erfolg",
        "05": "Karte abgelehnt",
        "0C": "Transaktion abgebrochen durch Benutzer",
        "1A": "Terminal nicht bereit",
        "30": "Formatfehler",
        "55": "Falsche PIN",
        "64": "Ursprünglicher Betrag falsch",
        "65": "Höchstbetrag überschritten",
        "9A": "Vorgang nicht möglich",
        "A0": "Nicht verfügbar",
        "B1": "Nicht erlaubt",
        "B2": "Karte abgelaufen",
        "B3": "Karte gesperrt",
        "B4": "Karte defekt",
        "B5": "Kommunikationsfehler",
        "B6": "Autorisierungsfehler",
        "B7": "Karte nicht zugelassen",
        "B8": "Systemfehler",
        "FF": "Unbekannter Fehler",
        "Z3": "Abgebrochen durch Timeout",
      };
    }
  }

  module.exports = ZVTConstants;