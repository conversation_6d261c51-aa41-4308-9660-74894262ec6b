// TSE-Client für die Integration mit EasyTSE über HTTP-Service
const axios = require('axios');
const log = require('electron-log');
const TseDatabase = require('./tse-database');
const path = require('path');

// TSE-Operation-Queue zur sequentiellen Verarbeitung
class TseOperationQueue {
  constructor() {
    this.queue = [];
    this.processing = false;
    this.minDelay = 500; // 500ms Mindestabstand zwischen Operationen
    this.lastOperationTime = 0;
  }

  // Fügt eine TSE-Operation zur Warteschlange hinzu
  addOperation(operation, params) {
    return new Promise((resolve, reject) => {
      this.queue.push({
        operation,
        params,
        resolve,
        reject
      });

      // Starte Verarbeitung, falls noch nicht läuft
      if (!this.processing) {
        this.processNext();
      }
    });
  }

  // Verarbeitet die nächste Operation in der Warteschlange
  async processNext() {
    if (this.queue.length === 0) {
      this.processing = false;
      return;
    }

    this.processing = true;
    const item = this.queue.shift();

    try {
      // Berechne Wartezeit, um Mindestabstand zu gewährleisten
      const now = Date.now();
      const timeSinceLastOperation = now - this.lastOperationTime;
      const delay = Math.max(0, this.minDelay - timeSinceLastOperation);

      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      // Führe die Operation aus
      const result = await item.operation(...item.params);
      this.lastOperationTime = Date.now();
      item.resolve(result);
    } catch (error) {
      item.reject(error);
    } finally {
      // Verarbeite die nächste Operation
      setTimeout(() => this.processNext(), 10);
    }
  }
}

class TseClient {
  constructor(config) {
    this.config = config;
    this.tseConfig = config.tse_config || {};

    // Service-URL aus der Konfiguration oder Standard-URL verwenden
    this.serviceUrl = this.tseConfig.service_url || "http://localhost:8765";

    this.connected = false;
    this.transactions = new Map();
    
    // Initialisiere die Revisionsdatenbank direkt im Konstruktor
    try {
      log.info('Initialisiere Revisionsdatenbank bei TSE-Client Start');
      const dbPath = path.join(process.env.APPDATA || process.env.HOME, 'wizidpos', 'data', 'tse-revisions.db');
      this.revisionDb = new TseDatabase({ dbPath: dbPath });
      // Die asynchrone Initialisierung wird später in der connect-Methode aufgerufen
      log.info('Revisionsdatenbank-Objekt erstellt mit Pfad:', dbPath);
    } catch (error) {
      log.error('Fehler bei der Initialisierung der Revisionsdatenbank:', error);
      this.revisionDb = null;
    }

    // Operationswarteschlange initialisieren
    this.operationQueue = new TseOperationQueue();

    // Standardwerte für Zeitüberschreitungen
    this.requestTimeout = this.tseConfig.request_timeout || 30000;

    log.info('EPSON TSE-Client initialisiert mit Service-URL:', this.serviceUrl);
  }

  /**
   * Verbindung zur TSE herstellen
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async connect() {
    return this.operationQueue.addOperation(this._connectImpl.bind(this), []);
  }

  async _connectImpl() {
    try {
      log.info('Stelle Verbindung zur EPSON TSE her...');

      // Verbindungsparameter
      const params = {
        puk: this.tseConfig.puk || '123456',
        adminPin: this.tseConfig.admin_pin || '12345',
        timeAdminPin: this.tseConfig.time_admin_pin || '54321',
        secretKey: this.tseConfig.secret_key || 'EPSONKEY',
        ip: this.tseConfig.ip || '127.0.0.1',
        port: String(this.tseConfig.port || 8009), // Als String konvertieren
        deviceId: this.tseConfig.device_id || 'local_TSE',
        clientId: this.tseConfig.client_id || 'Kasse1'
      };

      log.info('Sende Verbindungsparameter an EPSON TSE Service:', JSON.stringify(params));

      // Anfrage an den EasyTSE-Service senden
      const response = await axios.post(`${this.serviceUrl}/connect`, params, {
        timeout: this.requestTimeout
      });

      // Überprüfen des Antwortformats
      let responseData = response.data;
      if (typeof responseData === 'string') {
        try {
          responseData = JSON.parse(responseData);
        } catch (parseError) {
          log.error('Fehler beim Parsen der EPSON TSE-Verbindungsantwort:', parseError.message);
          return false;
        }
      }

      if (responseData && responseData.success) {
        this.connected = true;
        if (responseData.serialNumber) {
          log.info('EPSON TSE-Seriennummer:', responseData.serialNumber);
        }
        log.info('EPSON TSE-Verbindung erfolgreich hergestellt');

        // Initialisiere die Revisionsdatenbank, falls sie noch nicht initialisiert wurde
        if (this.revisionDb) {
          try {
            log.info('Initialisiere TSE-Revisionsdatenbank nach erfolgreicher Verbindung...');
            await this.revisionDb.initialize();
            log.info('TSE-Revisionsdatenbank erfolgreich initialisiert');
          } catch (dbError) {
            log.error('Fehler bei der Initialisierung der TSE-Revisionsdatenbank:', dbError);
          }
        } else {
          log.warn('TSE-Revisionsdatenbank-Objekt nicht vorhanden, kann nicht initialisiert werden');
        }

        // Nach erfolgreicher Verbindung Zeitaktualisierung durchführen
        // Selbsttest wird separat aufgerufen, um Doppelausführung zu vermeiden
        try {
          // Zeitaktualisierung durchführen
          const updateTimeResult = await this._updateTimeImpl();
          log.info('EPSON TSE Zeitaktualisierung Ergebnis:', updateTimeResult);

          // Als verbunden betrachten
          this.connected = true;
          return true;
        } catch (err) {
          log.error('Fehler bei EPSON TSE Selbsttest oder Zeitaktualisierung:', err.message);
          // Bei Fehler nicht als verbunden betrachten
          this.connected = false;
          return false;
        }
      } else {
        const errorMessage = responseData?.error || 'Unbekannter Fehler';
        log.error('EPSON TSE-Verbindung fehlgeschlagen:', errorMessage);
        return false;
      }
    } catch (error) {
      log.error('Fehler bei EPSON TSE-Verbindung:', error.message);
      if (error.response) {
        log.error('Fehlerdetails:', JSON.stringify(error.response.data));
      }
      return false;
    }
  }

  /**
   * TSE-Selbsttest durchführen
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async runSelfTest() {
    return this.operationQueue.addOperation(this._runSelfTestImpl.bind(this), []);
  }

  async _runSelfTestImpl() {
    try {
      log.info('Führe EPSON TSE-Selbsttest durch...');
      const response = await axios.post(`${this.serviceUrl}/selfTest`, {}, {
        timeout: this.requestTimeout
      });

      // Überprüfen des Antwortformats
      let responseData = response.data;
      if (typeof responseData === 'string') {
        try {
          responseData = JSON.parse(responseData);
        } catch (parseError) {
          log.error('Fehler beim Parsen der EPSON TSE-Selbsttestantwort:', parseError.message);
          return false;
        }
      }

      if (responseData && responseData.success) {
        log.info('EPSON TSE-Selbsttest erfolgreich');
        return true;
      } else {
        const errorMessage = responseData?.error || 'Unbekannter Fehler';
        log.error('EPSON TSE-Selbsttest fehlgeschlagen:', errorMessage);
        return false;
      }
    } catch (error) {
      log.error('Fehler beim EPSON TSE-Selbsttest:', error.message);
      return false;
    }
  }

  /**
   * TSE-Zeit aktualisieren
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async updateTime() {
    return this.operationQueue.addOperation(this._updateTimeImpl.bind(this), []);
  }

  async _updateTimeImpl() {
    try {
      log.info('Aktualisiere EPSON TSE-Zeit...');
      const response = await axios.post(`${this.serviceUrl}/updateTime`, {
        timeAdminPin: this.tseConfig.time_admin_pin || '54321'
      }, {
        timeout: this.requestTimeout
      });

      // Überprüfen des Antwortformats
      let responseData = response.data;
      if (typeof responseData === 'string') {
        try {
          responseData = JSON.parse(responseData);
        } catch (parseError) {
          log.error('Fehler beim Parsen der EPSON TSE-Zeitaktualisierungsantwort:', parseError.message);
          return false;
        }
      }

      if (responseData && responseData.success) {
        log.info('EPSON TSE-Zeit erfolgreich aktualisiert');
        return true;
      } else {
        const errorMessage = responseData?.error || 'Unbekannter Fehler';
        log.error('EPSON TSE-Zeitaktualisierung fehlgeschlagen:', errorMessage);
        return false;
      }
    } catch (error) {
      log.error('Fehler bei EPSON TSE-Zeitaktualisierung:', error.message);
      return false;
    }
  }

  /**
   * Eine neue Transaktion in der TSE starten
   * @param {Object} transactionData Transaktionsdaten
   * @returns {Promise<Object>} Transaktionsdaten mit TSE-Informationen
   */
  async openTransaction(transactionData) {
    return this.operationQueue.addOperation(this._openTransactionImpl.bind(this), [transactionData]);
  }

  async _openTransactionImpl(transactionData) {
    try {
      if (!this.connected) {
        await this._connectImpl();
      }

      log.info('Starte neue EPSON TSE-Transaktion:', transactionData.id);

      // Ermittle den ProcessData-String für StartTransaction
      // Wichtig: Bei StartTransaction noch KEIN ProcessData übergeben, da es erst bei UpdateTransaction gebraucht wird
      const processData = "";

      const payload = {
        clientId: this.tseConfig.client_id || 'Kasse1',
        holdConnection: true // Verbindung offen halten für nachfolgende Updates
      };

      log.info('Sende startTransaction-Anfrage an EPSON TSE Service:', JSON.stringify(payload));

      const response = await axios.post(`${this.serviceUrl}/startTransaction`, payload, {
        timeout: this.requestTimeout
      });

      // Überprüfen des Antwortformats
      let responseData = response.data;
      if (typeof responseData === 'string') {
        try {
          responseData = JSON.parse(responseData);
        } catch (parseError) {
          log.error('Fehler beim Parsen der EPSON TSE-startTransaction-Antwort:', parseError.message);
          throw new Error('Fehler bei der Verarbeitung der EPSON TSE-Antwort');
        }
      }

      if (responseData && responseData.success && responseData.data) {
        const result = responseData.data;

        // Erstelle ProcessData für Update
        const fullProcessData = this._createProcessData(transactionData);

        // Sende sofort ein Update mit den vollständigen Daten
        const updateOptions = {
          processType: 1, // Standard-Prozesstyp
          vorgangsType: this._getVorgangsTypFromTseEntry(transactionData.tse_entry), // Vorgangstyp aus TSE-Entry extrahieren
          holdConnection: true // Verbindung offen halten
        };

        // Transaktion für späteren Zugriff speichern
        this.transactions.set(transactionData.id, {
          wizidId: transactionData.id,
          tseTransactionNumber: result.transactionNumber,
          tseLogTime: result.logTime,
          tseSignatureStart: result.signatureStart,
          tseSignatureCounter: result.signatureCounter,
          tseSerialNumber: result.serialNumber,
          processData: fullProcessData
        });

        log.info('EPSON TSE Transaktion erfolgreich initialisiert. Aktualisiere mit vollständigen Daten...');

        // UpdateTransaction mit den eigentlichen Daten durchführen
        const updateResult = await this._updateTransactionImpl(transactionData, updateOptions);

        log.info('EPSON TSE-Transaktion erfolgreich gestartet und aktualisiert:', result.transactionNumber);

        // Vollständige TSE-Daten für MQTT-Nachricht zurückgeben
        return {
          tenant_id: transactionData.tenant_id,
          cashbox_id: this.tseConfig.client_id || 'K001',
          wizid_transaction_id: transactionData.id,
          tse_transaction_id: parseInt(result.transactionNumber, 10) || 0,
          tse_opening_timestamp: result.logTime || new Date().toISOString(),
          process_type: 0,
          vorgangs_type: 0,
          tse_serial_number: result.serialNumber || "",
          tse_signatur_start: result.signatureStart || "SignatureInit",
          tse_signatur_counter: parseInt(result.signatureCounter, 10) || 0,
          tse_hash_algorithm: "",
          tse_public_key: "",
          tse_error: "",
          transaction_type: transactionData.transaction_type || "Beleg",
          transaction_sub_type: transactionData.transaction_sub_type || "Verkauf",
          qr_code: ""
        };
      } else {
        const errorMessage = responseData?.error || 'Unbekannter Fehler';
        log.error('EPSON TSE-Transaktion konnte nicht gestartet werden:', errorMessage);
        throw new Error(errorMessage || 'Transaktion konnte nicht gestartet werden');
      }
    } catch (error) {
      log.error('Fehler beim Starten der EPSON TSE-Transaktion:', error.message);
      throw error;
    }
  }

  /**
   * Eine TSE-Transaktion aktualisieren
   * @param {Object} transactionData Transaktionsdaten zur Aktualisierung
   * @param {Object} options Optionale Parameter für die Aktualisierung
   * @returns {Promise<Object>} Aktualisierte TSE-Transaktionsdaten
   */
  async updateTransaction(transactionData, options = {}) {
    return this.operationQueue.addOperation(this._updateTransactionImpl.bind(this), [transactionData, options]);
  }

  async _updateTransactionImpl(transactionData, options = {}) {
    try {
      if (!this.connected) {
        await this._connectImpl();
      }

      // Transaktionsdaten aus dem Cache holen
      const storedTransaction = this.transactions.get(transactionData.id);
      if (!storedTransaction) {
        log.error('EPSON TSE-Transaktion nicht gefunden:', transactionData.id);
        throw new Error('Transaktion nicht gefunden');
      }

      log.info('Aktualisiere EPSON TSE-Transaktion:', storedTransaction.tseTransactionNumber);

      // Aktualisierte ProcessData erstellen
      const fullProcessData = this._createProcessData(transactionData);

      // VorgangsTyp aus tse_entry extrahieren
      const vorgangsTyp = this._getVorgangsTypFromTseEntry(transactionData.tse_entry);

      const payload = {
        clientId: this.tseConfig.client_id || 'Kasse1',
        transactionNumber: String(storedTransaction.tseTransactionNumber),  // Als String konvertieren
        processData: fullProcessData,
        processType: options.processType || 1, // Standardwert für ProcessType
        vorgangsType: options.vorgangsType || vorgangsTyp, // VorgangsTyp aus den Optionen oder aus tse_entry
        holdConnection: options.holdConnection || false // Ob die Verbindung gehalten werden soll
      };

      log.info('Sende updateTransaction-Anfrage an EPSON TSE Service:', JSON.stringify(payload));

      const response = await axios.post(`${this.serviceUrl}/updateTransaction`, payload, {
        timeout: this.requestTimeout
      });

      // Überprüfen des Antwortformats
      let responseData = response.data;
      if (typeof responseData === 'string') {
        try {
          responseData = JSON.parse(responseData);
        } catch (parseError) {
          log.error('Fehler beim Parsen der EPSON TSE-updateTransaction-Antwort:', parseError.message);
          throw new Error('Fehler bei der Verarbeitung der EPSON TSE-Antwort');
        }
      }

      if (responseData && responseData.success && responseData.data) {
        const result = responseData.data;
        log.info('EPSON TSE-Transaktion erfolgreich aktualisiert:', storedTransaction.tseTransactionNumber);

        // Aktualisierte Transaktionsdaten im Cache speichern
        this.transactions.set(transactionData.id, {
          ...storedTransaction,
          processData: fullProcessData,
          tseLogTime: result.logTime || storedTransaction.tseLogTime,
          tseSignatureValue: result.signatureValue || storedTransaction.tseSignatureStart
        });

        // Vollständige TSE-Daten für MQTT-Nachricht zurückgeben
        return {
          tenantId: transactionData.tenant_id,
          cashboxId: transactionData.cashbox_id,
          internalWizidId: transactionData.id,
          transactionNr: storedTransaction.tseTransactionNumber,
          logTime: result.logTime || storedTransaction.tseLogTime,
          signatureValue: result.signatureValue,
          signatureCounter: result.signatureCounter
        };
      } else {
        const errorMessage = responseData?.error || 'Unbekannter Fehler';
        log.error('EPSON TSE-Transaktion konnte nicht aktualisiert werden:', errorMessage);
        throw new Error(errorMessage || 'Transaktion konnte nicht aktualisiert werden');
      }
    } catch (error) {
      log.error('Fehler beim Aktualisieren der EPSON TSE-Transaktion:', error.message);
      throw error;
    }
  }

  /**
   * TSE-Transaktion abschließen
   * @param {Object} transactionData Die Transaktionsdaten mit Zahlungsmitteln etc.
   * @returns {Promise<Object>} Das Ergebnis des Abschlusses mit QRCode usw.
   */
  async finishTransaction(transactionData = {}) {
    return this.operationQueue.addOperation(this._finishTransactionImpl.bind(this), [transactionData]);
  }

  async _finishTransactionImpl(transactionData = {}) {
    try {
      log.info('=== EPSON-TSE-CLIENT: finishTransaction für Transaktion ' + transactionData.id + ' aufgerufen ===');
      const transactionId = transactionData.id;

      // NEUE PRÜFUNG: Erkennung von Dummy-Signaturen
      const hasDummySignature =
        transactionData.tse_signatur_start === "dummy-sig-start" ||
        transactionData.tse_serial_number === "dummy-serial" ||
        transactionData.tse_public_key === "dummy-public-key";

      // Wenn bereits abgeschlossen, aber keine Dummy-Werte, in Datenbank prüfen
      if (transactionData.tse_finish === true && !hasDummySignature) {
        // Prüfe in der Datenbank, ob die Transaktion bereits erfolgreich signiert wurde
        if (this.revisionDb) {
          try {
            const existingTransaction = await this.revisionDb.findTransactionByWizidId(transactionId);
            if (existingTransaction && existingTransaction.state === 'FINISHED') {
              log.info(`Transaktion ${transactionId} bereits erfolgreich signiert, verwende vorhandene Signatur`);

              // Vorhandene TSE-Daten aus der Datenbank zurückgeben
              const processDataJson = JSON.parse(existingTransaction.process_data || '{}');
              const finalQrCode = processDataJson.finished?.formatted || '';

              // Vollständige TSE-Daten zurückgeben
              return {
                tenant_id: transactionData.tenant_id,
                cashbox_id: this.tseConfig.client_id || 'K001',
                wizid_transaction_id: transactionId,
                tse_transaction_id: existingTransaction.transaction_number,
                tse_opening_timestamp: new Date(existingTransaction.start * 1000).toISOString(),
                tse_signatur_start: existingTransaction.start_signature || "",
                tse_signatur_end: existingTransaction.end_signature || "",
                tse_signatur_counter: parseInt(existingTransaction.transaction_counter, 10) || 0,
                tse_signatur_end_counter: parseInt(existingTransaction.transaction_counter, 10) || 0,
                tse_signatur_end_timestamp: new Date(existingTransaction.end * 1000).toISOString(),
                tse_serial_number: existingTransaction.cash_register_id || this.tseConfig.client_id || "K001",
                tse_hash_algorithm: "SHA256",
                tse_public_key: "",
                qr_code: finalQrCode,
                transaction_type: transactionData.transaction_type || "Beleg",
                transaction_sub_type: transactionData.transaction_sub_type || "Verkauf",
                total_amount: this._calculateTotalAmount(transactionData),
                tax_set: this._extractTaxSet(transactionData),
                payments: this._extractPayments(transactionData)
              };
            }
          } catch (dbError) {
            log.warn(`Fehler bei der Datenbankabfrage für Transaktion ${transactionId}: ${dbError.message}`);
            // Wir setzen trotz des Fehlers fort mit normalem Ablauf
          }
        }
      }

      // Wenn Dummy-Signaturen erkannt wurden, loggen und fortfahren
      if (hasDummySignature) {
        log.info(`Transaktion ${transactionId} hat Dummy-Signaturen, führe echte TSE-Signierung durch`);
      }

      // Transaktion im Cache suchen oder neu öffnen
      if (!this.transactions.has(transactionId)) {
        log.info(`Keine offene EPSON TSE-Transaktion für ID ${transactionId} gefunden, öffne zuerst eine Transaktion.`);
        try {
          await this._openTransactionImpl(transactionData);
        } catch (openError) {
          log.error(`Fehler beim Öffnen der EPSON TSE-Transaktion: ${openError.message}`);
          throw openError;
        }
      }

      const storedTransaction = this.transactions.get(transactionId);
      log.info('Gespeicherte EPSON TSE-Transaktion gefunden:', storedTransaction);

      // Formatieren der Prozessdaten basierend auf dem tse_entry
      const fullProcessData = this._createProcessData(transactionData);

      // VorgangsTyp aus tse_entry extrahieren
      const vorgangsTyp = this._getVorgangsTypFromTseEntry(transactionData.tse_entry);

      // Umsatzsteuer-Set erstellen aus tse_entry
      const taxSet = this._extractTaxSet(transactionData);

      // Zahlungsmittel aus tse_entry extrahieren
      const payments = this._extractPayments(transactionData);

      // Wichtig: Prozessdaten für FinishTransaction korrekt formatieren
      // Das ProcessData darf nicht verändert werden - genau so an den EasyTSE-Service übergeben
      log.info('ProcessData für finishTransaction:', fullProcessData);

      const payload = {
        clientId: this.tseConfig.client_id || 'Kasse1',
        transactionNumber: String(storedTransaction.tseTransactionNumber),
        processData: fullProcessData, // Die vollständigen Prozessdaten
        processType: 1,
        totalAmount: this._calculateTotalAmount(transactionData),
        taxSet: taxSet.map(item => ({
          taxRate: Number(item.taxRate),
          amount: Number(item.amount),
          netAmount: Number(item.netAmount)
        })),
        payments: payments.map(item => ({
          type: item.type,
          amount: Number(item.amount),
          name: item.name
        }))
      };

      log.info('Sende finishTransaction-Anfrage an EPSON TSE Service:', JSON.stringify(payload));

      const response = await axios.post(`${this.serviceUrl}/finishTransaction`, payload, {
        timeout: this.requestTimeout
      });

      // Überprüfen des Antwortformats
      let responseData = response.data;
      if (typeof responseData === 'string') {
        try {
          responseData = JSON.parse(responseData);
        } catch (parseError) {
          log.error('Fehler beim Parsen der EPSON TSE-finishTransaction-Antwort:', parseError.message);
          throw new Error('Fehler bei der Verarbeitung der EPSON TSE-Antwort');
        }
      }

      if (responseData && responseData.success) {
        const result = responseData.data;

        // Log der Signature-Werte zur besseren Nachverfolgung
        log.info('EPSON TSE Signature-Werte in der Antwort:', {
          signatureFinish: result.signatureFinish ? 'vorhanden' : 'nicht verfügbar',
          signatureCounter: result.signatureCounter ? 'vorhanden' : 'nicht verfügbar',
          logTime: result.logTime ? 'vorhanden' : 'nicht verfügbar',
          publicKey: result.publicKey ? 'vorhanden' : 'nicht verfügbar',
          qrData: result.qrData ? 'vorhanden' : 'nicht verfügbar'
        });

        // Detailliertes Logging des QR-Codes aus der EasyTSE-Antwort
        if (result.qrData) {
          log.info('EasyTSE QR-Code erhalten:', result.qrData);
        }

        let finalQrCode = null;
        // Prüfen, ob ein EasyTSE QR-Code im Format V0;... erhalten wurde
        if (result.qrData && typeof result.qrData === 'string' && result.qrData.startsWith('V0;')) {
          log.info('Gültiger EasyTSE QR-Code in Standard-Format erkannt');
          finalQrCode = result.qrData;
        } else {
          // Falls kein QR-Code vorhanden, generieren wir einen eigenen
          finalQrCode = await this.createDSFinVKQRCode(transactionData, result, storedTransaction);
        }

        // Vereinfachten QR-Code für das deutsche Prüftool erstellen
        const germanFiscalQRCode = await this.createGermanFiscalQRCode(transactionData, result);

        // Speichern der Transaktion in der Revisionsdatenbank
        try {
          // Prüfen, ob die Revisionsdatenbank initialisiert ist
          if (!this.revisionDb) {
            log.info('Initialisiere Revisionsdatenbank für erste Nutzung');
            const dbPath = path.join(process.env.APPDATA || process.env.HOME, 'wizidpos', 'data', 'tse-revisions.db');
            this.revisionDb = new TseDatabase({ dbPath: dbPath });
            await this.revisionDb.initialize();
            log.info('Revisionsdatenbank initialisiert:', this.revisionDb.dbPath);
          }

          // Transaktionsdaten für die Revisionsdatenbank vorbereiten
          const tseTransactionId = storedTransaction.tseTransactionNumber || "";

          // Zeit-Konvertierung: Wenn im Format DD.MM.YYYY HH:MM:SS, dann in Unix-Timestamp umwandeln
          let startTime = storedTransaction.tseLogTime;
          if (typeof startTime === 'string' && startTime.includes('.') && !startTime.includes('T')) {
            // Deutsches Datumsformat in ISO umwandeln
            const parts = startTime.split(' ');
            if (parts.length === 2) {
              const dateParts = parts[0].split('.');
              if (dateParts.length === 3) {
                startTime = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}T${parts[1]}.000Z`;
              }
            }
          }
          startTime = Math.floor(new Date(startTime).getTime() / 1000);

          // Endzeit konvertieren
          let endTime = result.logTime;
          if (typeof endTime === 'string' && endTime.includes('.') && !endTime.includes('T')) {
            // Deutsches Datumsformat in ISO umwandeln
            const parts = endTime.split(' ');
            if (parts.length === 2) {
              const dateParts = parts[0].split('.');
              if (dateParts.length === 3) {
                endTime = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}T${parts[1]}.000Z`;
              }
            }
          }
          endTime = Math.floor(new Date(endTime).getTime() / 1000);

          // ProcessData für die Datenbank vorbereiten
          const processDataJson = JSON.stringify({
            original: storedTransaction.processData || "",
            finished: {
              raw: fullProcessData,
              formatted: finalQrCode
            }
          });

          // In Revisionsdatenbank speichern
          await this.revisionDb.saveTransaction({
            fiskaly_client: this.tseConfig.client_id || 'Kasse1',
            tse_transaction_id: tseTransactionId,
            cash_register_id: transactionData.cash_register_id || this.tseConfig.client_id || 'K001',
            transaction_id: transactionData.id,
            transaction_number: parseInt(tseTransactionId, 10) || 0,
            transaction_counter: parseInt(result.signatureCounter, 10) || 0,
            start: startTime,
            end: endTime,
            state: 'FINISHED',
            type: 'TRANSACTION',
            start_signature: storedTransaction.tseSignatureStart || "",
            end_signature: result.signatureFinish || "",
            process_data: processDataJson
          });

          log.info('Abgeschlossene Transaktion in Revisionsdatenbank gespeichert:', transactionData.id);
          log.info('Datenbankpfad:', this.revisionDb.dbPath);

          // Zusätzlich auch einen Eintrag in der payment_transactions Tabelle erstellen
          try {
            // Zahlungsmethode und Betrag aus den Transaktionsdaten extrahieren
            const payments = this._extractPayments(transactionData);
            if (payments && payments.length > 0) {
              for (const payment of payments) {
                // Zahlungsmethode standardisieren
                let paymentMethod = 'UNKNOWN';
                if (payment.type.toLowerCase().includes('bar') || payment.name.toLowerCase().includes('bargeld')) {
                  paymentMethod = 'CASH';
                } else if (payment.type.toLowerCase().includes('card') || payment.type.toLowerCase().includes('karte')) {
                  paymentMethod = 'CARD';
                } else {
                  paymentMethod = payment.type.toUpperCase();
                }

                // Zahlungstransaktion in der Datenbank speichern
                const paymentResult = await this.revisionDb.savePaymentTransaction({
                  transaction_id: transactionData.id,
                  amount: payment.amount,
                  payment_method: paymentMethod,
                  status: 'COMPLETED',
                  completed_at: Math.floor(Date.now() / 1000)
                });

                log.info(`Zahlungstransaktion für ${transactionData.id} mit Methode ${paymentMethod} und Betrag ${payment.amount} in Datenbank gespeichert`);

                // Referenz zwischen TSE-Transaktion und Zahlungstransaktion speichern
                try {
                  await this.revisionDb.saveTransactionReference({
                    tse_transaction_id: tseTransactionId,
                    payment_transaction_id: transactionData.id,
                    checkout_id: transactionData.checkout_id || transactionData.id,
                    reference_type: 'TSE_PAYMENT'
                  });

                  log.info(`Referenz zwischen TSE-Transaktion ${tseTransactionId} und Zahlungstransaktion ${transactionData.id} gespeichert`);
                } catch (refError) {
                  log.warn(`Fehler beim Speichern der Transaktionsreferenz: ${refError.message}`);
                }
              }
            } else {
              // Fallback: Wenn keine Zahlungen extrahiert werden konnten, verwende den Gesamtbetrag als Barzahlung
              const totalAmount = this._calculateTotalAmount(transactionData);
              const paymentResult = await this.revisionDb.savePaymentTransaction({
                transaction_id: transactionData.id,
                amount: totalAmount,
                payment_method: 'CASH',
                status: 'COMPLETED',
                completed_at: Math.floor(Date.now() / 1000)
              });

              log.info(`Fallback-Zahlungstransaktion für ${transactionData.id} mit Methode CASH und Betrag ${totalAmount} in Datenbank gespeichert`);

              // Referenz zwischen TSE-Transaktion und Zahlungstransaktion speichern
              try {
                await this.revisionDb.saveTransactionReference({
                  tse_transaction_id: tseTransactionId,
                  payment_transaction_id: transactionData.id,
                  checkout_id: transactionData.checkout_id || transactionData.id,
                  reference_type: 'TSE_PAYMENT_FALLBACK'
                });

                log.info(`Fallback-Referenz zwischen TSE-Transaktion ${tseTransactionId} und Zahlungstransaktion ${transactionData.id} gespeichert`);
              } catch (refError) {
                log.warn(`Fehler beim Speichern der Fallback-Transaktionsreferenz: ${refError.message}`);
              }
            }
          } catch (paymentDbError) {
            log.warn('Fehler beim Speichern der Zahlungstransaktion:', paymentDbError.message);
            // Wir werfen den Fehler nicht weiter, damit die TSE-Funktionalität trotzdem gewährleistet ist
          }
        } catch (dbError) {
          // Fehlerbehandlung - die Hauptfunktionalität nicht unterbrechen
          log.warn('Fehler beim Speichern in Revisionsdatenbank:', dbError.message);
          // Wir werfen den Fehler nicht weiter, damit die TSE-Funktionalität trotzdem gewährleistet ist
        }

        // Transaktion aus dem Cache entfernen
        this.transactions.delete(transactionId);

        log.info('EPSON TSE-Transaktion erfolgreich abgeschlossen:', storedTransaction.tseTransactionNumber);

        // Debug für Transaktion IDs
        log.info('Debug Transaktion IDs:', {
          storedTransactionNumber: storedTransaction.tseTransactionNumber,
          originalTransactionId: payload.transactionNumber,
          resultTransactionNumber: result.transactionNumber || 'nicht in Antwort vorhanden'
        });

        // Ermittle die korrekte TSE-Transaktions-ID
        const tseTransactionId = parseInt(payload.transactionNumber, 10) ||
                                parseInt(storedTransaction.tseTransactionNumber, 10) ||
                                parseInt(result.transactionNumber, 10) || 0;

        // Mapping von VorgangsTyp aus dem tse_entry
        let process_type = 0;
        let vorgangs_type = 0;

        if (transactionData.tse_entry) {
          const parts = transactionData.tse_entry.split(',');
          if (parts.length >= 2) {
            process_type = parseInt(parts[0], 10) || 0;
            vorgangs_type = parseInt(parts[1], 10) || 0;
          }
        }

        // Vollständige TSE-Daten für MQTT-Nachricht zurückgeben
        return {
          tenant_id: transactionData.tenant_id,
          cashbox_id: this.tseConfig.client_id || 'K001',
          wizid_transaction_id: transactionData.id,
          tse_transaction_id: tseTransactionId,
          tse_opening_timestamp: storedTransaction.tseLogTime || new Date().toISOString(),
          tse_signatur_start: storedTransaction.tseSignatureStart || "",
          tse_signatur_counter: parseInt(storedTransaction.tseSignatureCounter, 10) || 0,
          process_type: process_type,
          vorgangs_type: vorgangs_type,
          tse_serial_number: result.serialNumber || storedTransaction.tseSerialNumber || "",
          tse_signatur_end: result.signatureFinish || "",
          tse_signatur_end_counter: parseInt(result.signatureCounter, 10) || 0,
          tse_signatur_end_timestamp: result.logTime || new Date().toISOString(),
          tse_hash_algorithm: "SHA256",
          tse_public_key: result.publicKey || "",
          tse_error: "",
          transaction_type: transactionData.transaction_type || "Beleg",
          transaction_sub_type: transactionData.transaction_sub_type || "Verkauf",
          qr_code: finalQrCode,
          german_fiscal_qrcode: germanFiscalQRCode,
          total_amount: this._calculateTotalAmount(transactionData),
          tax_set: taxSet,
          payments: payments
        };
      } else {
        const errorMessage = responseData?.error || 'Unbekannter Fehler';
        log.error('EPSON TSE-Transaktion konnte nicht abgeschlossen werden:', errorMessage);
        throw new Error(errorMessage || 'Transaktion konnte nicht abgeschlossen werden');
      }
    } catch (error) {
      log.error('Fehler beim Abschließen der EPSON TSE-Transaktion:', error.message);
      throw error;
    }
  }

  /**
   * Eine TSE-Transaktion mit 0€ abschließen (für abgestürzte/unvollständige Transaktionen)
   * @param {Object} transactionData Transaktionsdaten mit transaction_number
   * @returns {Promise<Object>} Abschluss-Ergebnis
   */
  async finishZeroTransaction(transactionData = {}) {
    return this.operationQueue.addOperation(this._finishZeroTransactionImpl.bind(this), [transactionData]);
  }

  async _finishZeroTransactionImpl(transactionData = {}) {
    try {
      const transactionNumber = transactionData.transaction_number;
      const transactionId = transactionData.id;

      if (!transactionNumber) {
        throw new Error('Keine Transaktionsnummer für 0€-Abschluss angegeben');
      }

      log.info(`Schließe EPSON TSE-Transaktion ${transactionNumber} mit 0€ ab`);

      // Anfrage vorbereiten
      const payload = {
        clientId: this.tseConfig.client_id || 'Kasse1',
        transactionNumber: transactionNumber.toString()
      };

      log.info('Sende finishZeroTransaction-Anfrage an EPSON TSE Service:', JSON.stringify(payload));

      // Anfrage senden
      const response = await axios.post(`${this.serviceUrl}/finishZeroTransaction`, payload, {
        timeout: this.requestTimeout
      });

      // Überprüfen des Antwortformats
      let responseData = response.data;
      if (typeof responseData === 'string') {
        try {
          responseData = JSON.parse(responseData);
        } catch (parseError) {
          log.error('Fehler beim Parsen der EPSON TSE-finishZeroTransaction-Antwort:', parseError.message);
          throw new Error('Fehler bei der Verarbeitung der EPSON TSE-Antwort');
        }
      }

      if (responseData && responseData.success) {
        // Speichern in Revisionsdatenbank als abgeschlossen
        try {
          if (this.revisionDb) {
            const endTime = Math.floor(Date.now() / 1000);

            // In Revisionsdatenbank als abgeschlossen speichern
            await this.revisionDb.saveTransaction({
              fiskaly_client: this.tseConfig.client_id || 'Kasse1',
              tse_transaction_id: transactionNumber.toString(),
              cash_register_id: this.tseConfig.client_id || 'K001',
              transaction_id: transactionId || `zero_finish_${transactionNumber}`,
              transaction_number: parseInt(transactionNumber, 10) || 0,
              transaction_counter: parseInt(responseData.data?.signatureCounter, 10) || 0,
              start: endTime - 1, // Startzeit 1 Sekunde vor Ende setzen
              end: endTime,
              state: 'FINISHED',
              type: 'ZERO_TRANSACTION',
              start_signature: "",
              end_signature: responseData.data?.signatureFinish || "",
              process_data: JSON.stringify({
                type: 'zero_finish',
                original_transaction_number: transactionNumber,
                finished_at: new Date().toISOString()
              })
            });

            log.info(`0€-Transaktion ${transactionNumber} in Revisionsdatenbank gespeichert`);
          }
        } catch (dbError) {
          log.error('Fehler beim Speichern der 0€-Transaktion in Revisionsdatenbank:', dbError.message);
          // Nicht als Fehler behandeln, da die TSE-Operation erfolgreich war
        }

        log.info(`EPSON TSE-Transaktion ${transactionNumber} erfolgreich mit 0€ abgeschlossen`);

        return {
          success: true,
          message: `Transaktion ${transactionNumber} erfolgreich mit 0€ abgeschlossen`,
          data: responseData.data
        };
      } else {
        const errorMessage = responseData?.error || 'Unbekannter Fehler';
        log.error('EPSON TSE-0€-Abschluss fehlgeschlagen:', errorMessage);
        throw new Error(`TSE-0€-Abschluss fehlgeschlagen: ${errorMessage}`);
      }
    } catch (error) {
      log.error('Fehler beim 0€-Abschluss der EPSON TSE-Transaktion:', error.message);
      throw error;
    }
  }

  /**
   * Eine TSE-Transaktion abbrechen
   * @param {Object} transactionData Transaktionsdaten zum Abbrechen
   * @returns {Promise<Object>} Abbruch-Ergebnis
   */
  async cancelTransaction(transactionData = {}) {
    return this.operationQueue.addOperation(this._cancelTransactionImpl.bind(this), [transactionData]);
  }

  async _cancelTransactionImpl(transactionData = {}) {
    try {
      const transactionId = transactionData.id;

      // NEUE PRÜFUNG: Erkennung von Dummy-Signaturen
      const hasDummySignature =
        transactionData.tse_signatur_start === "dummy-sig-start" ||
        transactionData.tse_serial_number === "dummy-serial" ||
        transactionData.tse_public_key === "dummy-public-key";

      // Wenn bereits abgeschlossen, aber keine Dummy-Werte, in Datenbank prüfen
      if (transactionData.tse_finish === true && !hasDummySignature) {
        // Prüfe in der Datenbank, ob die Transaktion bereits erfolgreich abgebrochen wurde
        if (this.revisionDb) {
          try {
            const existingTransaction = await this.revisionDb.findTransactionByWizidId(transactionId);
            if (existingTransaction && existingTransaction.state === 'CANCELED') {
              log.info(`Transaktion ${transactionId} bereits erfolgreich abgebrochen, verwende vorhandene Daten`);

              // Vorhandene TSE-Daten zurückgeben
              return {
                tenant_id: transactionData.tenant_id,
                cashbox_id: this.tseConfig.client_id || 'K001',
                wizid_transaction_id: transactionId,
                tse_transaction_id: existingTransaction.transaction_number,
                tse_opening_timestamp: new Date(existingTransaction.start * 1000).toISOString(),
                tse_signatur_start: existingTransaction.start_signature || "",
                tse_signatur_counter: parseInt(existingTransaction.transaction_counter, 10) || 0,
                process_type: 0,
                vorgangs_type: 0,
                tse_serial_number: existingTransaction.cash_register_id || "",
                tse_signatur_end: "",
                tse_signatur_end_counter: 0,
                tse_signatur_end_timestamp: new Date().toISOString(),
                tse_hash_algorithm: "",
                tse_public_key: "",
                tse_error: "",
                transaction_type: transactionData.transaction_type || "Beleg",
                transaction_sub_type: transactionData.transaction_sub_type || "Verkauf",
                qr_code: "",
                status: "canceled"
              };
            }
          } catch (dbError) {
            log.warn(`Fehler bei der Datenbankabfrage für Transaktion ${transactionId}: ${dbError.message}`);
            // Wir setzen trotz des Fehlers fort mit normalem Ablauf
          }
        }
      }

      // Wenn Dummy-Signaturen erkannt wurden, loggen und fortfahren
      if (hasDummySignature) {
        log.info(`Transaktion ${transactionId} hat Dummy-Signaturen, führe echte TSE-Stornierung durch`);
      }

      // Transaktion im Cache suchen oder fehlerwerfen
      if (!this.transactions.has(transactionId)) {
        // Bei Abbruch versuchen wir nicht, eine neue Transaktion zu starten,
        // da es keinen Sinn macht, eine Transaktion zu starten, um sie sofort abzubrechen
        const errorMessage = `Keine offene EPSON TSE-Transaktion für ID ${transactionId} gefunden`;
        log.error(errorMessage);
        throw new Error(errorMessage);
      }

      const storedTransaction = this.transactions.get(transactionId);
      log.info('Gespeicherte EPSON TSE-Transaktion für Abbruch gefunden:', storedTransaction);

      // Anfrage vorbereiten
      let startTime;
      try {
        // Versuche, die Startzeit zu parsen und als ISO-String zu senden
        if (storedTransaction.tseLogTime) {
          // Prüfe, ob es bereits ein Date-Objekt ist oder ein String
          if (storedTransaction.tseLogTime instanceof Date) {
            startTime = storedTransaction.tseLogTime.toISOString();
          } else {
            // Versuche verschiedene Formate zu parsen
            let parsedDate;
            const timeStr = storedTransaction.tseLogTime.toString();

            // Deutsches Format: "30.06.2025 09:32:21"
            if (timeStr.includes('.') && timeStr.includes(' ')) {
              const [datePart, timePart] = timeStr.split(' ');
              const [day, month, year] = datePart.split('.');
              parsedDate = new Date(`${year}-${month}-${day}T${timePart}`);
            } else {
              // Fallback: Versuche direktes Parsen
              parsedDate = new Date(timeStr);
            }

            if (isNaN(parsedDate.getTime())) {
              log.warn('Konnte Startzeit nicht parsen, verwende aktuelle Zeit:', timeStr);
              startTime = new Date().toISOString();
            } else {
              startTime = parsedDate.toISOString();
            }
          }
        } else {
          log.warn('Keine Startzeit verfügbar, verwende aktuelle Zeit');
          startTime = new Date().toISOString();
        }
      } catch (error) {
        log.error('Fehler beim Verarbeiten der Startzeit:', error.message);
        startTime = new Date().toISOString();
      }

      const payload = {
        clientId: this.tseConfig.client_id || 'Kasse1',
        transactionNumber: storedTransaction.tseTransactionNumber,
        startTime: startTime // Startzeit der Transaktion im ISO 8601-Format übergeben
      };

      log.info('Sende cancelTransaction-Anfrage an EPSON TSE Service:', JSON.stringify(payload));

      // Anfrage senden
      const response = await axios.post(`${this.serviceUrl}/cancelTransaction`, payload, {
        timeout: this.requestTimeout
      });

      // Überprüfen des Antwortformats
      let responseData = response.data;
      if (typeof responseData === 'string') {
        try {
          responseData = JSON.parse(responseData);
        } catch (parseError) {
          log.error('Fehler beim Parsen der EPSON TSE-cancelTransaction-Antwort:', parseError.message);
          throw new Error('Fehler bei der Verarbeitung der EPSON TSE-Antwort');
        }
      }

      if (responseData && responseData.success) {
        // Speichern in Revisionsdatenbank als abgebrochen
        try {
          if (this.revisionDb) {
            // Daten für die Revisionsdatenbank vorbereiten
            let startTime;
            try {
              // Versuche, die Startzeit zu parsen (deutsches Format: "30.06.2025 09:32:21")
              if (storedTransaction.tseLogTime && typeof storedTransaction.tseLogTime === 'string') {
                const timeStr = storedTransaction.tseLogTime.toString();

                if (timeStr.includes('.') && timeStr.includes(' ')) {
                  // Deutsches Format: "30.06.2025 09:32:21"
                  const [datePart, timePart] = timeStr.split(' ');
                  const [day, month, year] = datePart.split('.');
                  const parsedDate = new Date(`${year}-${month}-${day}T${timePart}`);

                  if (!isNaN(parsedDate.getTime())) {
                    startTime = Math.floor(parsedDate.getTime() / 1000);
                  } else {
                    throw new Error('Ungültiges Datum nach Parsing');
                  }
                } else {
                  // Fallback: Versuche direktes Parsen
                  const parsedDate = new Date(timeStr);
                  if (!isNaN(parsedDate.getTime())) {
                    startTime = Math.floor(parsedDate.getTime() / 1000);
                  } else {
                    throw new Error('Direktes Parsing fehlgeschlagen');
                  }
                }
              } else {
                throw new Error('Keine gültige tseLogTime verfügbar');
              }
            } catch (timeParseError) {
              log.warn('Konnte Startzeit nicht parsen, verwende aktuelle Zeit:', timeParseError.message);
              startTime = Math.floor(Date.now() / 1000);
            }

            const endTime = Math.floor(Date.now() / 1000);

            // In Revisionsdatenbank als abgebrochen speichern
            await this.revisionDb.saveTransaction({
              fiskaly_client: this.tseConfig.client_id || 'Kasse1',
              tse_transaction_id: storedTransaction.tseTransactionNumber,
              cash_register_id: transactionData.cash_register_id || this.tseConfig.client_id || 'K001',
              transaction_id: transactionId,
              transaction_number: parseInt(storedTransaction.tseTransactionNumber, 10) || 0,
              transaction_counter: parseInt(storedTransaction.tseSignatureCounter, 10) || 0,
              start: startTime,
              end: endTime,
              state: 'CANCELED',
              type: 'TRANSACTION',
              start_signature: storedTransaction.tseSignatureStart || "",
              end_signature: "",
              process_data: JSON.stringify({
                original: storedTransaction.processData || "",
                canceled: true
              })
            });

            log.info('Abgebrochene Transaktion in Revisionsdatenbank gespeichert:', transactionId);
          }
        } catch (dbError) {
          log.warn('Fehler beim Speichern des Abbruchs in Revisionsdatenbank:', dbError.message);
        }

        // Transaktion aus dem Cache entfernen
        this.transactions.delete(transactionId);

        log.info('EPSON TSE-Transaktion erfolgreich abgebrochen:', storedTransaction.tseTransactionNumber);

        // Vollständige TSE-Daten für MQTT-Nachricht zurückgeben
        return {
          tenant_id: transactionData.tenant_id,
          cashbox_id: this.tseConfig.client_id || 'K001',
          wizid_transaction_id: transactionData.id,
          tse_transaction_id: parseInt(storedTransaction.tseTransactionNumber, 10) || 0,
          tse_opening_timestamp: storedTransaction.tseLogTime,
          tse_signatur_start: storedTransaction.tseSignatureStart,
          tse_signatur_counter: parseInt(storedTransaction.tseSignatureCounter, 10) || 0,
          process_type: 0,
          vorgangs_type: 0,
          tse_serial_number: storedTransaction.tseSerialNumber || "",
          tse_signatur_end: "",
          tse_signatur_end_counter: 0,
          tse_signatur_end_timestamp: new Date().toISOString(),
          tse_hash_algorithm: "",
          tse_public_key: "",
          tse_error: "",
          transaction_type: transactionData.transaction_type || "Beleg",
          transaction_sub_type: transactionData.transaction_sub_type || "Verkauf",
          qr_code: "",
          status: "canceled"
        };
      } else {
        const errorMessage = responseData?.error || 'Unbekannter Fehler';
        log.error('EPSON TSE-Transaktion konnte nicht abgebrochen werden:', errorMessage);
        throw new Error(errorMessage || 'Transaktion konnte nicht abgebrochen werden');
      }
    } catch (error) {
      log.error('Fehler beim Abbrechen der EPSON TSE-Transaktion:', error.message);
      throw error;
    }
  }

  /**
   * Ermittelt den Barbetrag einer Transaktion
   * @param {Object} transaction Transaktionsdaten
   * @returns {number} Barbetrag
   */
  _calculateTotalAmount(transaction) {
    try {
      let totalAmount = 0;

      // Prioritätsreihenfolge für den Betrag: total_amount > total
      if (transaction.total_amount !== undefined) {
        // Stelle sicher, dass der Betrag als Zahl behandelt wird
        let amount = parseFloat(transaction.total_amount);

        // Überprüfe, ob der Betrag in Cent angegeben ist (über 100 € ist wahrscheinlich in Cent)
        if (Math.abs(amount) > 10000 || typeof transaction.total_amount === 'string' && !transaction.total_amount.includes('.')) {
          totalAmount = amount / 100;
        } else {
          totalAmount = amount;
        }
      } else if (transaction.total !== undefined) {
        // Alternative Eigenschaft für den Betrag
        let total = parseFloat(transaction.total);

        // Überprüfe, ob der Betrag in Cent angegeben ist
        if (Math.abs(total) > 10000 || typeof transaction.total === 'string' && !transaction.total.includes('.')) {
          totalAmount = total / 100;
        } else {
          totalAmount = total;
        }
      } else if (transaction.items && transaction.items.length > 0) {
        // Berechne den Gesamtbetrag aus den Artikeln
        totalAmount = transaction.items.reduce((sum, item) => {
          const itemPrice = parseFloat(item.price || 0);
          const itemQuantity = parseInt(item.quantity || 1, 10);
          const itemAmount = itemPrice * itemQuantity;

          // Überprüfe, ob der Preis in Cent angegeben ist
          if (itemPrice > 1000) {
            return sum + (itemAmount / 100);
          }
          return sum + itemAmount;
        }, 0);
      }

      // Betrag auf 2 Dezimalstellen runden
      return parseFloat(totalAmount.toFixed(2));
    } catch (error) {
      log.error('Fehler bei der Betragsberechnung:', error.message);
      return 0;
    }
  }

  /**
   * Extrahiert den VorgangsTyp aus dem tse_entry
   * @param {string} tseEntry Der tse_entry-String (Format: 1,2,...)
   * @returns {number} VorgangsTyp (default: 1)
   */
  _getVorgangsTypFromTseEntry(tseEntry) {
    if (!tseEntry) return 1; // Standard: Beleg

    const parts = tseEntry.split(',');
    if (parts.length < 2) return 1;

    return parseInt(parts[1], 10) || 1;
  }

  /**
   * ProcessData für die TSE basierend auf den Transaktionsdaten erstellen
   * @param {Object} transactionData Transaktionsdaten
   * @returns {string} Formatierte ProcessData für die TSE
   */
  _createProcessData(transactionData) {
    try {
      // Teil 1: VorgangsTyp-Mapping - Präfix ermitteln (nur für Logging-Zwecke)
      let vorgangsTypPrefix = "Beleg"; // Standard-Typ

      // Prüfen, ob tse_entry vorhanden ist und VorgangsTyp daraus extrahieren
      if (transactionData.tse_entry) {
        const parts = transactionData.tse_entry.split(',');
        if (parts.length >= 2) {
          const vorgangsTyp = parseInt(parts[1], 10) || 1;

          // VorgangsTyp-Mapping für deutsches Kassengesetz
          switch (vorgangsTyp) {
            case 1: vorgangsTypPrefix = "Beleg"; break;
            case 2: vorgangsTypPrefix = "AVTransfer"; break;
            case 3: vorgangsTypPrefix = "AVBestellung"; break;
            case 4: vorgangsTypPrefix = "AVTraining"; break;
            case 5: vorgangsTypPrefix = "AVBelegstorno"; break;
            case 6: vorgangsTypPrefix = "AVBelegabbruch"; break;
            case 7: vorgangsTypPrefix = "AVSachbezug"; break;
            case 8: vorgangsTypPrefix = "AVSonstige"; break;
            case 9: vorgangsTypPrefix = "AVRechnung"; break;
            default: vorgangsTypPrefix = "Beleg"; break;
          }
        }
      } else if (transactionData.transaction_sub_type === "Anfangsbestand") {
        // Wenn kein tse_entry vorhanden, aber es sich um einen Anfangsbestand handelt
        vorgangsTypPrefix = "AVTransfer";
      }

      // Teil 2: USt-Sätze und Beträge sammeln (0.00_2.55_0.00_0.00_0.00)
      let taxAmounts = [];

      // Extrahiere Steuerwerte aus tse_entry, falls vorhanden
      if (transactionData.tse_entry && typeof transactionData.tse_entry === 'string') {
        // Format: "1,1,0.00,0.00,0.00,0.00,14.90,14.90,0.00"
        const taxValues = transactionData.tse_entry.split(',');

        // Ab dem dritten Wert sind die Steuerbeträge enthalten (Indizes 2-6)
        if (taxValues.length >= 8) {
          // Extrahiere die Steuerwerte direkt aus tse_entry
          taxAmounts = [
            taxValues[2] || "0.00", // 19% Steuer
            taxValues[3] || "0.00", // 7% Steuer
            taxValues[4] || "0.00", // 10.7% Steuer (besondere Vorsteuer)
            taxValues[5] || "0.00", // 5.5% Steuer (andere besondere Vorsteuer)
            taxValues[6] || "0.00"  // 0% Steuer (ohne Mehrwertsteuer)
          ];

          log.info('Steuerbeträge aus tse_entry extrahiert:', taxAmounts);
        }
      } else {
        // Fallback: Standard-Umsatzsteuer-Werte aus transactionData extrahieren
        let umsatzsteuer19 = 0;
        let umsatzsteuer7 = 0;
        let umsatzsteuer0 = 0;

        // Werte aus den Transaktionsdaten extrahieren (falls vorhanden)
        if (transactionData.total_vat_19 !== undefined) {
          umsatzsteuer19 = parseFloat(transactionData.total_vat_19) / 100;
        }

        if (transactionData.total_vat_7 !== undefined) {
          umsatzsteuer7 = parseFloat(transactionData.total_vat_7) / 100;
        }

        if (transactionData.total_vat_0 !== undefined || transactionData.total_net_0 !== undefined) {
          umsatzsteuer0 = parseFloat(transactionData.total_net_0 || 0) / 100;
        }

        // Umsatzsteuer-Werte formatieren
        taxAmounts = [
          umsatzsteuer19.toFixed(2),
          umsatzsteuer7.toFixed(2),
          "0.00", // Besondere Vorsteuer 10.7%
          "0.00", // Besondere Vorsteuer 5.5%
          umsatzsteuer0.toFixed(2)
        ];

        log.info('Steuerbeträge aus total_vat_* Feldern extrahiert:', taxAmounts);
      }

      // Teil 3: Zahlungsmittel und Beträge (2.55:Bar)
      let payments = [];
      let totalAmount = this._calculateTotalAmount(transactionData);

      // Zahlungsarten aus den Transaktionsdaten extrahieren
      if (transactionData.payments && transactionData.payments.length > 0) {
        transactionData.payments.forEach(payment => {
          let betrag = parseFloat(payment.amount);
          // Wenn Betrag in Cent ist, in Euro umrechnen
          if (betrag > 100) {
            betrag = betrag / 100;
          }
          let zahlungsart = payment.name || payment.type || "Bar";

          // Standardisierte Zahlungsarten-Bezeichnungen
          if (zahlungsart.toLowerCase().includes("bar")) {
            zahlungsart = "Bar";
          } else if (zahlungsart.toLowerCase().includes("karte") || zahlungsart.toLowerCase().includes("card")) {
            zahlungsart = "Karte";
          } else if (zahlungsart.toLowerCase().includes("kredit")) {
            zahlungsart = "Kredit";
          }

          payments.push(`${betrag.toFixed(2)}:${zahlungsart}`);
        });
      } else if (transactionData.tse_entry) {
        // Zahlungen aus tse_entry extrahieren
        const tseEntryParts = transactionData.tse_entry.split(',');
        if (tseEntryParts.length >= 9) {
          const barBetrag = parseFloat(tseEntryParts[7] || '0.00');
          const unbarBetrag = parseFloat(tseEntryParts[8] || '0.00');

          if (barBetrag !== 0) {
            payments.push(`${barBetrag.toFixed(2)}:Bar`);
          }

          if (unbarBetrag !== 0) {
            payments.push(`${unbarBetrag.toFixed(2)}:Unbar`);
          }

          // Wenn keine Zahlungen extrahiert wurden, Fallback auf Gesamtbetrag
          if (payments.length === 0) {
            payments.push(`${totalAmount.toFixed(2)}:Bar`);
          }
        } else {
          // Standard-Zahlungsmittel, wenn tse_entry ungültiges Format hat
          payments.push(`${totalAmount.toFixed(2)}:Bar`);
        }
      } else {
        // Standard-Zahlungsmittel, wenn keine Zahlungen und kein tse_entry angegeben sind
        payments.push(`${totalAmount.toFixed(2)}:Bar`);
      }

      // ANPASSUNG: Nur den Teil ohne Präfix erstellen und zurückgeben
      const taxAndPaymentPart = `${taxAmounts.join('_')}^${payments.join(',')}`;

      // Für Logging-Zwecke erstellen wir auch die vollständige ProcessData mit Präfix
      const fullProcessData = `${vorgangsTypPrefix}^${taxAndPaymentPart}`;

      log.info('ProcessData für TSE (ohne Präfix):', taxAndPaymentPart);
      log.info('Vollständige ProcessData (für Referenz):', fullProcessData);

      // WICHTIGE ÄNDERUNG: Nur die ProcessData ohne Präfix zurückgeben
      return taxAndPaymentPart;
    } catch (error) {
      log.error('Fehler bei der ProcessData-Erstellung:', error.message);
      // Standardwert bei Fehler ohne Präfix
      return '0.00_0.00_0.00_0.00_0.00^0.00:Bar';
    }
  }

  /**
   * Steuerliche Werte aus dem tse_entry extrahieren
   */
  _extractTaxSet(transactionData) {
    try {
      const taxSet = [];

      if (transactionData.tse_entry && typeof transactionData.tse_entry === 'string') {
        // Format: "1,2,0.00,0.00,0.00,0.00,50.00,50.00,0.00"
        const taxValues = transactionData.tse_entry.split(',');

        if (taxValues.length >= 8) {
          // Angenommen:
          // Index 2: 19% MwSt
          // Index 3: 7% MwSt
          // Index 4: 10.7% MwSt (besondere Vorsteuer)
          // Index 5: 5.5% MwSt (andere besondere Vorsteuer)
          // Index 6: 0% MwSt (ohne Mehrwertsteuer)

          // 19% MwSt
          const mwst19 = parseFloat(taxValues[2] || '0.00');
          if (mwst19 > 0) {
            taxSet.push({
              taxRate: 19,
              amount: mwst19 * 0.19,
              netAmount: mwst19
            });
          }

          // 7% MwSt
          const mwst7 = parseFloat(taxValues[3] || '0.00');
          if (mwst7 > 0) {
            taxSet.push({
              taxRate: 7,
              amount: mwst7 * 0.07,
              netAmount: mwst7
            });
          }

          // 10.7% MwSt (besondere Vorsteuer)
          const mwst107 = parseFloat(taxValues[4] || '0.00');
          if (mwst107 > 0) {
            taxSet.push({
              taxRate: 10.7,
              amount: mwst107 * 0.107,
              netAmount: mwst107
            });
          }

          // 5.5% MwSt (andere besondere Vorsteuer)
          const mwst55 = parseFloat(taxValues[5] || '0.00');
          if (mwst55 > 0) {
            taxSet.push({
              taxRate: 5.5,
              amount: mwst55 * 0.055,
              netAmount: mwst55
            });
          }

          // 0% MwSt (ohne Mehrwertsteuer)
          const mwst0 = parseFloat(taxValues[6] || '0.00');
          if (mwst0 !== 0) {
            taxSet.push({
              taxRate: 0,
              amount: 0,
              netAmount: mwst0
            });
          }

          // Wenn keine Steuerinformationen gefunden wurden, Gesamtbetrag mit 0% MwSt
          if (taxSet.length === 0) {
            const totalAmount = this._calculateTotalAmount(transactionData);
            taxSet.push({
              taxRate: 0,
              amount: 0,
              netAmount: totalAmount
            });
          }
        }
      }

      // Wenn keine tse_entry oder Fehler beim Parsen, Fallback auf einfache Werte
      if (taxSet.length === 0) {
        const totalAmount = this._calculateTotalAmount(transactionData);

        // Bei Anfangsbestand immer 0% MwSt
        if (transactionData.transaction_sub_type === 'Anfangsbestand') {
          taxSet.push({
            taxRate: 0,
            amount: 0,
            netAmount: totalAmount
          });
        } else {
          // Bei normalen Verkäufen Standard-MwSt
          taxSet.push({
            taxRate: 19,
            amount: 0,
            netAmount: 0
          });
        }
      }

      return taxSet;
    } catch (error) {
      log.error('Fehler beim Extrahieren der Steuerinformationen:', error.message);

      // Fallback mit 0% MwSt
      return [{
        taxRate: 0,
        amount: 0,
        netAmount: this._calculateTotalAmount(transactionData)
      }];
    }
  }

  /**
   * Zahlungsinformationen aus dem tse_entry extrahieren
   */
  _extractPayments(transactionData) {
    try {
      const payments = [];
      const totalAmount = this._calculateTotalAmount(transactionData);

      if (transactionData.tse_entry && typeof transactionData.tse_entry === 'string') {
        // Format: "1,2,0.00,0.00,0.00,0.00,50.00,50.00,0.00"
        const tseEntryParts = transactionData.tse_entry.split(',');

        if (tseEntryParts.length >= 9) {
          const barBetrag = parseFloat(tseEntryParts[7] || '0.00');
          const unbarBetrag = parseFloat(tseEntryParts[8] || '0.00');

          // Füge Barzahlung hinzu, wenn vorhanden (auch für negative Beträge)
          if (barBetrag !== 0) {
            payments.push({
              type: "bar",
              amount: barBetrag,
              name: "Bargeld"
            });
          }

          // Füge unbare Zahlung hinzu, wenn vorhanden
          if (unbarBetrag !== 0) {
            payments.push({
              type: "unbar",
              amount: unbarBetrag,
              name: "Kartenzahlung"
            });
          }

          // Wenn beide Werte 0 sind, aber die Gesamtsumme nicht 0 ist,
          // dann verwende die Gesamtsumme als Barzahlung
          if (barBetrag === 0 && unbarBetrag === 0 && totalAmount !== 0) {
            payments.push({
              type: "bar",
              amount: totalAmount,
              name: "Bargeld"
            });
          }
        }
      }

      // Wenn keine Zahlungen extrahiert wurden oder kein tse_entry vorhanden ist
      if (payments.length === 0) {
        payments.push({
          type: "bar",
          amount: totalAmount,
          name: "Bargeld"
        });
      }

      return payments;
    } catch (error) {
      log.error('Fehler beim Extrahieren der Zahlungsinformationen:', error.message);

      // Fallback bei Fehler
      return [{
        type: "bar",
        amount: this._calculateTotalAmount(transactionData),
        name: "Bargeld"
      }];
    }
  }

  /**
   * Erstellt einen QR-Code im DSFinV-K-konformen Format
   * @param {Object} transactionData - Die Transaktionsdaten
   * @param {Object} tseSigData - Die TSE-Signaturdaten (mit Signatur, Counter, etc.)
   * @param {Object} storedTransaction - Die gespeicherte Transaktion mit Start-Daten
   * @returns {Promise<string>} - Das QR-Code-Format als String
   */
  async createDSFinVKQRCode(transactionData, tseSigData, storedTransaction) {
    try {
      log.info('Erstelle QR-Code im DSFinV-K-Format:', {
        transactionId: transactionData?.id || 'unbekannt',
        storedTransactionNumber: storedTransaction?.tseTransactionNumber || 'unbekannt'
      });

      // Stelle sicher, dass wir gültige Parameter haben
      if (!transactionData) {
        log.warn('Keine transactionData für QR-Code-Erstellung vorhanden');
        transactionData = {};
      }

      if (!tseSigData) {
        log.warn('Keine tseSigData für QR-Code-Erstellung vorhanden');
        tseSigData = {};
      }

      if (!storedTransaction) {
        log.warn('Keine storedTransaction für QR-Code-Erstellung vorhanden');
        storedTransaction = {};
      }

      // 1. QR-Code-Version: V0
      const qrCodeVersion = "V0";

      // 2. Kassen-Seriennummer / Client-ID
      const kassenSeriennummer = this.tseConfig.client_id || "Kasse1";

      // 3. ProcessType
      const processType = "Kassenbeleg-V1";

      // 4. ProcessData
      let processData = "";
      try {
        // WICHTIG: Hier den ursprünglichen Präfix nicht verwenden!
        // Für QR-Code immer Beleg als Präfix verwenden, da die Steuer- und Zahlungsdaten wichtiger sind
        const vorgangsTyp = this._getVorgangsTypFromTseEntry(transactionData.tse_entry);
        const isPrefixRequired = (vorgangsTyp !== 1);

        processData = this._createProcessData(transactionData);

        // Wenn ProcessData bereits ein Präfix hat (z.B. "AVTransfer^"), dieses NICHT entfernen
        // Es ist wichtig, dass "Beleg^" oder "AVTransfer^" enthalten bleibt

      } catch (processDataError) {
        log.error('Fehler beim Erstellen der ProcessData:', processDataError.message);
        processData = "Beleg^0.00_0.00_0.00_0.00_0.00^0.00:Bar";
      }

      // 5. Transaktionsnummer
      const transaktionsNummer = storedTransaction.tseTransactionNumber || "0";

      // 6. Signaturzähler
      const signaturZaehler = tseSigData.signatureCounter || "0";

      // 7. Start-Zeit (startTransaction)
      let startZeit = storedTransaction.tseLogTime || new Date().toISOString();
      // Zeit-Konvertierung: Wenn im Format DD.MM.YYYY HH:MM:SS, dann in ISO umwandeln
      if (startZeit && !startZeit.includes('T')) {
        try {
          // Convert DD.MM.YYYY HH:MM:SS format to ISO-8601
          const parts = startZeit.split(' ');
          if (parts.length === 2) {
            const dateParts = parts[0].split('.');
            if (dateParts.length === 3) {
              startZeit = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}T${parts[1]}.000Z`;
              log.info('Start-Zeit in ISO-Format konvertiert:', startZeit);
            }
          }
        } catch (formatError) {
          log.error('Fehler bei der Konvertierung des Start-Zeit-Formats:', formatError.message);
          startZeit = new Date().toISOString(); // Fallback to current time in ISO format
        }
      }

      // 8. Log-Time (finishTransaction)
      let logTime = tseSigData.logTime || new Date().toISOString();
      // Zeit-Konvertierung: Wenn im Format DD.MM.YYYY HH:MM:SS, dann in ISO umwandeln
      if (logTime && !logTime.includes('T')) {
        try {
          // Convert DD.MM.YYYY HH:MM:SS format to ISO-8601
          const parts = logTime.split(' ');
          if (parts.length === 2) {
            const dateParts = parts[0].split('.');
            if (dateParts.length === 3) {
              logTime = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}T${parts[1]}.000Z`;
              log.info('Log-Zeit in ISO-Format konvertiert:', logTime);
            }
          }
        } catch (formatError) {
          log.error('Fehler bei der Konvertierung des Log-Zeit-Formats:', formatError.message);
          logTime = new Date().toISOString(); // Fallback to current time in ISO format
        }
      }

      // 9. Signatur-Algorithmus
      let sigAlg = tseSigData.signatureAlgorithm || "ecdsa-plain-SHA256";

      // 10. Log-Time-Format
      let logTimeFormat = tseSigData.logTimeFormat || "unixTime";

      // 11. Signatur
      let signatur = tseSigData.signatureFinish || "";

      // 12. Public Key
      let publicKey = tseSigData.publicKey || "";

      log.info('QR-Code-Felder vor Abruf fehlender Daten:', {
        signaturVorhanden: !!signatur,
        publicKeyVorhanden: !!publicKey
      });

      // Wenn Signatur oder Public Key fehlen, hole sie direkt vom TSE
      if (!signatur || !publicKey || !logTimeFormat || !sigAlg) {
        try {
          log.info('Fehlende Daten für QR-Code, hole direkte TSE-Infos');

          const tseInfo = await this.getTseInfo();

          if (!signatur && tseInfo.signature) {
            signatur = tseInfo.signature;
            log.info('Signatur aus TSE-Infos geladen:', signatur.substring(0, 20) + '...');
          }

          if (!publicKey && tseInfo.publicKey) {
            publicKey = tseInfo.publicKey;
            log.info('Public Key aus TSE-Infos geladen:', publicKey.substring(0, 20) + '...');
          }

          if (tseInfo.signatureAlgorithm) {
            sigAlg = tseInfo.signatureAlgorithm;
            log.info('Signatur-Algorithmus aus TSE-Infos geladen:', sigAlg);
          }

          if (tseInfo.logTimeFormat) {
            logTimeFormat = tseInfo.logTimeFormat;
            log.info('Log-Time-Format aus TSE-Infos geladen:', logTimeFormat);
          }

          log.info('TSE-Infos nach Abruf:', {
            signaturVorhanden: !!signatur,
            publicKeyVorhanden: !!publicKey,
            sigAlgVorhanden: !!sigAlg,
            logTimeFormatVorhanden: !!logTimeFormat
          });
        } catch (infoError) {
          log.error('Fehler beim Laden der TSE-Infos:', infoError.message);
        }
      }

      // Stelle sicher, dass immer zumindest leere Strings verwendet werden
      signatur = signatur || "";
      publicKey = publicKey || "";

      if (!signatur) {
        log.warn('QR-Code wird ohne Signatur erstellt!');
      }

      if (!publicKey) {
        log.warn('QR-Code wird ohne Public Key erstellt!');
      }

      // QR-Code im DSFinV-K-Format erstellen
      const qrCode = `${qrCodeVersion};${kassenSeriennummer};${processType};${processData};${transaktionsNummer};${signaturZaehler};${startZeit};${logTime};${sigAlg};${logTimeFormat};${signatur};${publicKey}`;

      log.info('QR-Code im DSFinV-K-Format erstellt:', {
        length: qrCode.length,
        format: 'V0;kassenSeriennummer;processType;processData;transaktionsNummer;signaturZaehler;startZeit;logTime;sigAlg;logTimeFormat;signatur;publicKey'
      });

      return qrCode;
    } catch (error) {
      log.error('Fehler bei der DSFinV-K-QR-Code-Erstellung:', error.message);

      // Erstelle einen minimalen QR-Code als Fallback
      const minimalQRCode = `V0;${this.tseConfig.client_id || "Kasse1"};Kassenbeleg-V1;Beleg^0.00_0.00_0.00_0.00_0.00^0.00:Bar;${storedTransaction?.tseTransactionNumber || "0"};${tseSigData?.signatureCounter || "0"};${storedTransaction?.tseLogTime || new Date().toISOString()};${tseSigData?.logTime || new Date().toISOString()};ecdsa-plain-SHA256;unixTime;;`;

      log.info('Minimaler QR-Code als Fallback erstellt');
      return minimalQRCode;
    }
  }

  /**
  * Erstellt einen QR-Code im vereinfachten Format gemäß den deutschen Fiskalvorschriften
  * @param {Object} transactionData - Die Transaktionsdaten
  * @param {Object} tseSigData - Die TSE-Signaturdaten
  * @returns {Promise<string>} Der QR-Code im Format für deutsche Fiskalbelege
  */
 async createGermanFiscalQRCode(transactionData, tseSigData) {
  try {
    log.info('Erstelle QR-Code für deutschen Fiskalbeleg:', {
      transactionId: transactionData?.id || 'unbekannt'
    });

    // Stelle sicher, dass wir gültige Parameter haben
    if (!transactionData) {
      log.warn('Keine transactionData für deutsche Fiskalbeleg-QR-Code-Erstellung vorhanden');
      transactionData = {};
    }

    if (!tseSigData) {
      log.warn('Keine tseSigData für deutsche Fiskalbeleg-QR-Code-Erstellung vorhanden');
      tseSigData = {};
    }

    // Version: Immer V0
    const version = "V0";

    // Seriennummer aus tseSigData oder via TSE abrufen
    let serialNumber = tseSigData.serialNumber || "";

    // Wenn keine Seriennummer vorhanden, versuche sie direkt abzurufen
    if (!serialNumber) {
      try {
        log.info('Seriennummer fehlt, hole TSE-Infos für Seriennummer');
        const tseInfo = await this.getTseInfo();
        serialNumber = tseInfo.serialNumber || "";
        log.info('Seriennummer aus TSE-Infos geladen:', serialNumber);
      } catch (error) {
        log.error('Fehler beim Laden der Seriennummer für Fiskalbeleg:', error.message);
      }
    }

    // Prüfe, ob die Seriennummer ein gültiges Format hat
    if (!serialNumber || serialNumber.includes(" ")) {
      // Fallback-Seriennummer verwenden, wenn die Seriennummer ungültig ist oder fehlt
      serialNumber = "B7BE13C75A5F08B785901675B7D07BCC8F1A844EC0799C33AE7A19A0A7D1F1D4";
      log.warn('Ungültige oder fehlende Seriennummer, verwende Fallback-Seriennummer');
    }

    // Signaturzähler prioritär aus den TSE-Signaturdaten abrufen
    let signatureCounter = "";

    // 1. Versuche zuerst den Signaturzähler aus tseSigData.signatureCounter zu bekommen
    if (tseSigData.signatureCounter) {
      signatureCounter = tseSigData.signatureCounter;
      log.info('Signaturzähler aus tseSigData.signatureCounter geladen:', signatureCounter);
    }
    // 2. Wenn nicht vorhanden, versuche ihn aus transactionData.tse_signatur_counter zu bekommen
    else if (transactionData.tse_signatur_counter) {
      signatureCounter = transactionData.tse_signatur_counter;
      log.info('Signaturzähler aus Transaction.tse_signatur_counter geladen:', signatureCounter);
    }
    // 3. Wenn immer noch nicht gefunden, versuche aus gespeicherter Transaktion
    else {
      const storedTransaction = this.transactions.get(transactionData.id);
      if (storedTransaction && storedTransaction.tseSignatureCounter && storedTransaction.tseSignatureCounter !== "0") {
        signatureCounter = storedTransaction.tseSignatureCounter;
        log.info('Signaturzähler aus gespeicherter Transaktion geladen:', signatureCounter);
      }
      // 4. Als letzten Versuch, hole TSE-Infos
      else {
        try {
          log.info('Signaturzähler fehlt oder ist 0, hole TSE-Infos');
          const tseInfo = await this.getTseInfo();
          signatureCounter = tseInfo.signatureCounter || "";
          log.info('Signaturzähler aus TSE-Infos geladen:', signatureCounter);
        } catch (error) {
          log.error('Fehler beim Laden des Signaturzählers für Fiskalbeleg:', error.message);
        }
      }
    }

    // Wenn der Signaturzähler immer noch 0 oder leer ist, setzen wir einen Standardwert
    if (!signatureCounter || signatureCounter === "0" || parseInt(signatureCounter, 10) === 0) {
      signatureCounter = "1"; // Ein Minimalwert zur Vermeidung von Validierungsfehlern
      log.info('Verwende Standardwert 1 für den Signaturzähler');
    }

    // Stelle sicher, dass der Signaturzähler eine Zahl ist
    signatureCounter = parseInt(signatureCounter, 10).toString();

    // Zeitstempel: Entweder aus logTime oder aktuelle Zeit
    let timestamp = tseSigData.logTime || new Date().toISOString();

    // Stellen sicher, dass der Zeitstempel im ISO-8601-Format ist
    if (timestamp && !timestamp.includes('T')) {
      try {
        // Convert DD.MM.YYYY HH:MM:SS format to ISO-8601
        const parts = timestamp.split(' ');
        if (parts.length === 2) {
          const dateParts = parts[0].split('.');
          if (dateParts.length === 3) {
            const day = dateParts[0].padStart(2, '0');
            const month = dateParts[1].padStart(2, '0');
            const year = dateParts[2];
            const time = parts[1];
            timestamp = `${year}-${month}-${day}T${time}.000Z`;
            log.info('Zeitstempel in ISO-Format konvertiert:', timestamp);
          }
        }
      } catch (formatError) {
        log.error('Fehler bei der Konvertierung des Zeitstempel-Formats:', formatError.message);
        timestamp = new Date().toISOString(); // Fallback to current time in ISO format
      }
    }

    // Betrag: Aus den Transaktionsdaten oder 0.00 als Fallback
    let amount = this._calculateTotalAmount(transactionData);

    // Formatiere den Betrag mit 2 Dezimalstellen und Punkt als Dezimaltrenner
    const formattedAmount = amount.toFixed(2);

    // Erstelle den QR-Code im deutschen Fiskalbeleg-Format: V0;[Seriennummer];[Nummer];[Zeitstempel];[Betrag]
    const qrCode = `${version};${serialNumber};${signatureCounter};${timestamp};${formattedAmount}`;

    log.info('QR-Code für deutschen Fiskalbeleg erstellt:', {
      format: 'V0;[Seriennummer];[Nummer];[Zeitstempel];[Betrag]',
      example: qrCode.substring(0, 40) + '...'
    });

    return qrCode;
  } catch (error) {
    log.error('Fehler bei der deutschen Fiskalbeleg-QR-Code-Erstellung:', error.message);

    // Erstelle einen minimalen QR-Code als Fallback
    const fallbackTimestamp = new Date().toISOString();
    const fallbackAmount = "0.00";
    const fallbackQRCode = `V0;B7BE13C75A5F08B785901675B7D07BCC8F1A844EC0799C33AE7A19A0A7D1F1D4;1;${fallbackTimestamp};${fallbackAmount}`;

    log.info('Minimaler Fiskalbeleg-QR-Code als Fallback erstellt');
    return fallbackQRCode;
  }
}

/**
 * TSE-Informationen abrufen (Public Key, Seriennummer, etc.)
 * @returns {Promise<Object>} TSE-Informationen
 */
async getTseInfo() {
  return this.operationQueue.addOperation(this._getTseInfoImpl.bind(this), []);
}

async _getTseInfoImpl() {
  try {
    log.info('Rufe TSE-Infos vom EasyTSE-Service ab');

    // Versuch 1: Verwende den /info-Endpunkt
    try {
      const response = await axios.post(`${this.serviceUrl}/info`, {
        clientId: this.tseConfig.client_id || 'Kasse1'
      }, {
        timeout: this.requestTimeout
      });

      let responseData = response.data;
      if (typeof responseData === 'string') {
        try {
          responseData = JSON.parse(responseData);
        } catch (parseError) {
          log.error('Fehler beim Parsen der info-Antwort:', parseError.message);
        }
      }

      if (responseData && responseData.success) {
        const result = responseData.data || {};
        log.info('TSE-Infos erfolgreich über /info abgerufen', {
          publicKeyAvailable: !!result.publicKey,
          signatureAvailable: !!result.signature,
          serialNumberAvailable: !!result.serialNumber,
          signatureAlgorithm: result.signatureAlgorithm || 'nicht verfügbar',
          logTimeFormat: result.logTimeFormat || 'nicht verfügbar'
        });
        return result;
      } else {
        log.warn('Fehler beim Abrufen der TSE-Infos via /info:', responseData?.error || 'Unbekannter Fehler');
        // Fahre mit dem nächsten Versuch fort
      }
    } catch (infoError) {
      log.warn('Fehler beim Aufruf des /info-Endpunkts:', infoError.message);
      // Fahre mit dem nächsten Versuch fort
    }

    // Versuch 2: Hole Public Key und Signature separat
    let tseInfo = {
      publicKey: '',
      signature: '',
      serialNumber: this.tseConfig.client_id || 'K001',
      signatureAlgorithm: 'ecdsa-plain-SHA256',
      logTimeFormat: 'unixTime',
      signatureCounter: '0'
    };

    // Hole Public Key
    try {
      const keyResponse = await axios.post(`${this.serviceUrl}/getPublicKey`, {
        clientId: this.tseConfig.client_id || 'Kasse1'
      }, {
        timeout: this.requestTimeout
      });

      if (keyResponse.data && keyResponse.data.success &&
          keyResponse.data.data && keyResponse.data.data.publicKey) {
        tseInfo.publicKey = keyResponse.data.data.publicKey;
        log.info('Public Key erfolgreich abgerufen:', tseInfo.publicKey.substring(0, 20) + '...');
      } else {
        log.warn('Fehler beim Abrufen des Public Keys:',
                keyResponse.data?.error || 'Unbekannter Fehler');
      }
    } catch (keyError) {
      log.warn('Fehler beim Aufruf des /getPublicKey-Endpunkts:', keyError.message);
    }

    // Hole Signatur (verwende einen Standard-Wert für transactionNumber, wenn nicht verfügbar)
    try {
      const signatureResponse = await axios.post(`${this.serviceUrl}/getSignature`, {
        clientId: this.tseConfig.client_id || 'Kasse1',
        transactionNumber: '0'
      }, {
        timeout: this.requestTimeout
      });

      if (signatureResponse.data && signatureResponse.data.success &&
          signatureResponse.data.data) {
        const data = signatureResponse.data.data;

        if (data.signature) {
          tseInfo.signature = data.signature;
          log.info('Signatur erfolgreich abgerufen:', tseInfo.signature.substring(0, 20) + '...');
        }

        if (data.signatureCounter) {
          tseInfo.signatureCounter = data.signatureCounter;
          log.info('Signaturzähler erfolgreich abgerufen:', tseInfo.signatureCounter);
        }
      } else {
        log.warn('Fehler beim Abrufen der Signatur:',
                 signatureResponse.data?.error || 'Unbekannter Fehler');
      }
    } catch (signatureError) {
      log.warn('Fehler beim Aufruf des /getSignature-Endpunkts:', signatureError.message);
    }

    // Überprüfen, ob wir mindestens einige Daten haben
    if (tseInfo.publicKey || tseInfo.signature) {
      log.info('TSE-Infos teilweise abgerufen', {
        publicKeyAvailable: !!tseInfo.publicKey,
        signatureAvailable: !!tseInfo.signature,
        signatureCounterAvailable: !!tseInfo.signatureCounter
      });
      return tseInfo;
    }

    // Fallback-Werte, wenn alles fehlschlägt
    log.warn('Keine TSE-Infos verfügbar, verwende Fallback-Werte');
    return {
      publicKey: 'RHVtbXlQdWJsaWNLZXk=',
      signature: 'RHVtbXlTaWduYXR1cmU=',
      serialNumber: this.tseConfig.client_id || 'K001',
      signatureAlgorithm: 'ecdsa-plain-SHA256',
      logTimeFormat: 'unixTime',
      signatureCounter: '0'
    };
  } catch (error) {
    log.error('Unerwarteter Fehler beim Abrufen der TSE-Infos:', error.message);
    // Fallback-Werte
    return {
      publicKey: 'RHVtbXlQdWJsaWNLZXk=',
      signature: 'RHVtbXlTaWduYXR1cmU=',
      serialNumber: this.tseConfig.client_id || 'K001',
      signatureAlgorithm: 'ecdsa-plain-SHA256',
      logTimeFormat: 'unixTime',
      signatureCounter: '0'
    };
  }
}
}

module.exports = TseClient;