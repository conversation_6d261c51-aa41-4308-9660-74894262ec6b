<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>EC-Kartenzahlung</title>
  <style>
    body {
  font-family: Arial, sans-serif;
  background-color: #f5f5f5;
  margin: 0;
  padding: 15px;
  overflow: hidden;
  user-select: none;
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
    
.container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 15px;
  display: flex;
  flex-direction: column;
  height: 100vh; 
  max-height: calc(100vh - 10px); 
}
    
    h1 {
      text-align: center;
      color: #333;
      margin-top: 0;
      margin-bottom: 20px;
    }
    
    .spinner {
      align-self: center;
      width: 20px;
      height: 20px;
      border: 5px solid rgba(0, 123, 255, 0.3);
      border-radius: 50%;
      border-top-color: #007bff;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 20px;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    
    .status {
      padding: 15px;
      margin: 15px 0;
      border-radius: 4px;
      font-size: 16px;
      font-weight: bold;
      text-align: center;
      background-color: #fff3cd;
      color: #856404;
      border: 1px solid #ffeeba;
      word-wrap: break-word;
    }
    
    .status.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .details {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
      flex: 1;
    }
    
    .details p {
      margin: 5px 0;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
    }
    
    .details p strong {
      font-weight: bold;
      margin-right: 10px;
      min-width: 100px;
    }
    
    .details p span {
      text-align: right;
      word-break: break-word;
      max-width: calc(100% - 110px);
    }
    
    #transaction-id {
      font-size: 0.85em; 
      overflow-wrap: break-word;
      word-wrap: break-word;
      hyphens: auto; 
    }
    
    .buttons {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
    }
    
    /* Allgemeiner Button-Stil mit Flex-Anpassungen */
    .btn {
      flex: 1;
      margin: 0 5px;
      padding: 10px;
      border-radius: 4px;
      font-weight: bold;
      cursor: pointer;
      transition: background-color 0.2s;
      border: none;
    }
    
    /* Der Primäre Button (Kundenbeleg) */
    .btn-primary {
      background-color: #64748B;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #475569;
    }
    
    /* Der Sekundäre Button (Händlerbeleg) */
    .btn-secondary {
      background-color: #64748B;
      color: white;
    }
    
    .btn-secondary:hover {
      background-color: #475569;
    }
    
    /* Schließen-Button */
    .btn.btn-danger {
      background-color: #dc3545;
    }
    
    .btn.btn-danger:hover {
      background-color: #c82333;
    }
    .btn-custom {
      background-color: #475569; 
      color: #78FA8D; 
    }

    .btn-custom:hover {
      background-color: #2a323d;
      color: #78FA8D; 
    }
    
    @media (max-width: 340px) {
      .details p {
        flex-direction: column;
      }
      
      .details p span {
        text-align: left;
        max-width: 100%;
      }
      
      .buttons {
        flex-direction: column;
      }
      
      .btn {
        margin: 5px 0;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>EC-Kartenzahlung</h1>
    
    <div class="spinner" id="spinner"></div>
    
    <div class="status" id="status">
      Bitte Karte an Terminal halten oder einstecken...
    </div>
    
    <div class="details">
      <p>
        <strong>Betrag:</strong>
        <span id="amount">0,00 €</span>
      </p>
      <p>
        <strong>Transaktion:</strong>
        <span id="transaction-id">-</span>
      </p>
      <p>
        <strong>Status:</strong>
        <span id="status-text">Warte auf Kartenzahlung</span>
      </p>
    </div>
    
    <div class="buttons">
      <button class="btn btn-primary" id="print-customer-btn">Kundenbeleg drucken</button>
      <button class="btn btn-secondary" id="print-merchant-btn">Händlerbeleg drucken</button>
      <button class="btn btn-danger" id="abort-btn">Abbrechen</button>
      <button class="btn btn-custom" id="close-btn" style="display: none;">Schließen</button>
    </div>
  </div>
  
  <script>
    // DOM-Elemente
    const spinner = document.getElementById('spinner');
    const status = document.getElementById('status');
    const amount = document.getElementById('amount');
    const transactionId = document.getElementById('transaction-id');
    const statusText = document.getElementById('status-text');
    const abortBtn = document.getElementById('abort-btn');
    const closeBtn = document.getElementById('close-btn');
    const printCustomerBtn = document.getElementById('print-customer-btn');
    const printMerchantBtn = document.getElementById('print-merchant-btn');
    
    // Status-Aktualisierung
function updateStatus(type, message) {
  status.className = 'status ' + (type || '');
  status.textContent = message;
  
  // Spinner nur bei 'success' oder 'error' ausblenden
  if (type === 'success') {
    // Bei Erfolg: Spinner ausblenden, Druck-Buttons einblenden
    spinner.style.display = 'none';
    abortBtn.style.display = 'none';
    closeBtn.style.display = 'inline-block';
    printCustomerBtn.style.display = 'inline-block';
    printMerchantBtn.style.display = 'inline-block';
  } else if (type === 'error') {
    // Bei Fehler: Spinner ausblenden, KEINE Druck-Buttons
    spinner.style.display = 'none';
    abortBtn.style.display = 'none';
    closeBtn.style.display = 'inline-block';
    printCustomerBtn.style.display = 'none';
    printMerchantBtn.style.display = 'none';
  } else {
    // Bei "in Bearbeitung": Spinner anzeigen, Abbruch-Button anzeigen
    spinner.style.display = 'block';
    abortBtn.style.display = 'inline-block';
    closeBtn.style.display = 'none';
    printCustomerBtn.style.display = 'none';
    printMerchantBtn.style.display = 'none';
  }
}
    
    // Helper-Funktion für korrekte Betragsformatierung
    function formatAmount(amountValue) {
  // Debug-Ausgabe
  console.log(`Formatiere Betrag: ${amountValue} (Typ: ${typeof amountValue})`);
  
  // Sicherstellen, dass der Betrag definiert und nummerisch ist
  if (amountValue === undefined || amountValue === null) {
    console.warn('Undefinierter Betrag empfangen');
    return '0,00 €';
  }
  
  let value = amountValue;
  
  // Wenn der Betrag ein String ist, konvertieren
  if (typeof value === 'string') {
    value = parseFloat(value.replace(',', '.'));
  }
  
  // WICHTIG: Die ZVT-Beträge sind IMMER in Cent, daher immer durch 100 teilen
  // Unabhängig vom Wert - auch kleine Beträge wie 1 Cent werden korrekt umgerechnet
  value = value / 100;
  
  // Debugging-Ausgabe nach Umrechnung
  console.log(`Nach Umrechnung: ${value} €`);
  
  // Immer zwei Dezimalstellen mit Komma als Trennzeichen
  return value.toLocaleString('de-DE', { 
    minimumFractionDigits: 2, 
    maximumFractionDigits: 2 
  }) + ' €';
}
    
    // Druck-Buttons initial ausblenden
    printCustomerBtn.style.display = 'none';
    printMerchantBtn.style.display = 'none';
    
    // Event-Listener für initiale Daten
    if (window.api && window.api.onPaymentData) {
      window.api.onPaymentData((data) => {
        console.log('Initiale Zahlungsdaten empfangen:', data);
        
        // Betrag formatieren und anzeigen
        if (data.amount !== undefined) {
          console.log(`Initialer Betrag: ${data.amount} (Typ: ${typeof data.amount})`);
          amount.textContent = formatAmount(data.amount);
        }
        
        // Transaktions-ID setzen
        if (data.transactionId) {
          transactionId.textContent = data.transactionId;
        }
        
        // Wenn es ein Tagesabschluss ist, spezielle Anzeige setzen
        if (data.isEndOfDay) {
          document.querySelector('h1').textContent = 'Tagesabschluss';
          updateStatus('', 'Tagesabschluss wird durchgeführt...');
          statusText.textContent = 'Tagesabschluss';
        }
      });
    }
    
    // Event-Listener für Updates
    if (window.api && window.api.onPaymentUpdate) {
      window.api.onPaymentUpdate((data) => {
        console.log('Payment update received:', data);
        
        // Status aktualisieren
        if (data.status === 'success') {
          updateStatus('success', data.message || 'Zahlung erfolgreich!');
          statusText.textContent = data.statusText || 'Erfolgreich';
          
          // Debugging-Info ausgeben
          console.log('SUCCESS STATUS EMPFANGEN - UI wird aktualisiert');
          
          // Button-Status aktualisieren
          abortBtn.style.display = 'none';
          closeBtn.style.display = 'inline-block';
          printCustomerBtn.style.display = 'inline-block';
          printMerchantBtn.style.display = 'inline-block';
          
          // Debugging-Trace zur Sichtbarkeit der Buttons
          console.log('Button-Status: closeBtn visible, printButtons visible');
        } else if (data.status === 'error') {
          updateStatus('error', data.message || 'Fehler bei der Zahlung');
          statusText.textContent = data.statusText || 'Fehlgeschlagen';
          
          // Debugging-Info ausgeben
          console.log('ERROR STATUS EMPFANGEN - UI wird aktualisiert');
          
          // Button-Status aktualisieren
          abortBtn.style.display = 'none';
          closeBtn.style.display = 'inline-block';
        } else {
          updateStatus('', data.message || 'Verarbeite Zahlung...');
          statusText.textContent = data.statusText || 'In Bearbeitung';
        }
        
        // Betrag und Transaktions-ID aktualisieren, falls vorhanden
        if (data.amount !== undefined) {
          console.log(`Update-Betrag: ${data.amount} (Typ: ${typeof data.amount})`);
          amount.textContent = formatAmount(data.amount);
        }
        
        if (data.transactionId) {
          transactionId.textContent = data.transactionId;
        }
        
        // Verfügbarkeit der Belege berücksichtigen
        if (data.hasOwnProperty('hasCustomerReceipt')) {
          if (data.hasCustomerReceipt === false) {
            printCustomerBtn.disabled = true;
            printCustomerBtn.title = "Kein Kundenbeleg verfügbar";
          } else {
            printCustomerBtn.disabled = false;
            printCustomerBtn.title = "";
          }
        }
        
        if (data.hasOwnProperty('hasMerchantReceipt')) {
          if (data.hasMerchantReceipt === false) {
            printMerchantBtn.disabled = true;
            printMerchantBtn.title = "Kein Händlerbeleg verfügbar";
          } else {
            printMerchantBtn.disabled = false;
            printMerchantBtn.title = "";
          }
        }
      });
    }
    
    // Auch auf das payment-result Event hören (als Backup)
    if (window.api && typeof window.api.onPaymentResult === 'function') {
      window.api.onPaymentResult((data) => {
        console.log('Payment result received:', data);
        
        // Ähnlich wie bei onPaymentUpdate reagieren
        if (data.success) {
          updateStatus('success', 'Zahlung erfolgreich!');
          statusText.textContent = 'Erfolgreich';
          
          // Button-Status aktualisieren
          abortBtn.style.display = 'none';
          closeBtn.style.display = 'inline-block';
          printCustomerBtn.style.display = 'inline-block';
          printMerchantBtn.style.display = 'inline-block';
        } else {
          updateStatus('error', data.error || 'Fehler bei der Zahlung');
          statusText.textContent = 'Fehlgeschlagen';
          
          // Button-Status aktualisieren 
          abortBtn.style.display = 'none';
          closeBtn.style.display = 'inline-block';
        }
        
        // Auch hier Betrag aktualisieren, falls vorhanden
        if (data.amount !== undefined) {
          console.log(`Result-Betrag: ${data.amount} (Typ: ${typeof data.amount})`);
          amount.textContent = formatAmount(data.amount);
        }
      });
    }
    
    // Abbrechen-Button
    abortBtn.addEventListener('click', () => {
      if (window.api && window.api.closeModal) {
        // Status auf "Wird abgebrochen" setzen
        updateStatus('', 'Zahlung wird abgebrochen...');
        statusText.textContent = 'Wird abgebrochen';
        
        // Modal mit Abbruchsignal schließen
        window.api.closeModal();
      }
    });
    
    // Schließen-Button (erscheint nach Erfolg/Fehler)
    closeBtn.addEventListener('click', () => {
      if (window.api && window.api.closeModalWithoutAbort) {
        // Modal ohne Abbruchsignal schließen
        window.api.closeModalWithoutAbort();
      }
    });
    
    // Kundenbeleg drucken Button
    printCustomerBtn.addEventListener('click', () => {
      if (window.api && window.api.printCustomerReceipt) {
        printCustomerBtn.disabled = true;
        window.api.printCustomerReceipt();
        updateStatus('success', 'Kundenbeleg wird gedruckt...');
      }
    });
    
    // Händlerbeleg drucken Button
    printMerchantBtn.addEventListener('click', () => {
      console.log('Händlerbeleg-Button wurde geklickt'); // Debug-Log hinzufügen
      if (window.api && window.api.printMerchantReceipt) {
        console.log('printMerchantReceipt API-Funktion gefunden, sende Anfrage...'); // Debug-Log
        printMerchantBtn.disabled = true;
        window.api.printMerchantReceipt();
        updateStatus('success', 'Händlerbeleg wird gedruckt...');
      } else {
        console.error('printMerchantReceipt API-Funktion nicht verfügbar!'); // Fehler-Log
      }
    });
  </script>
</body>
</html>