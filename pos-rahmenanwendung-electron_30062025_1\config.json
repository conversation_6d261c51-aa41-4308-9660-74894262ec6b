{"api": {"configUrl": "https://api.wizid.com/pos-manage/config", "authKey": "WQee4Kv8IchNXxWXBrgVc7FcXTu73yR3aegExlvMODUm37At"}, "windowSettings": {"width": 1920, "height": 1080, "fullscreen": true, "kiosk": true, "showMenuBar": false}, "adminPassword": "59071", "fallbackUrl": "https://wizid.com", "customer_display": false, "customer_display_options": {"polling_interval": 2000}, "printer": {"epson": {"ip": "*************", "port": "", "model": "TM-M30III"}}, "mqtt": {"broker": "wss://iot-wss.wizid.com:443/mqtt", "options": {"clientId": "WQee4Kv8IchNXxWXBrgVc7FcXTu73yR3aegExlvMODUm37At", "clean": true, "connectTimeout": 4000, "reconnectPeriod": 1000}}, "fiskaly_config": {"api_url": "https://kassensichv-middleware.fiskaly.com/api/v2", "api_key": "test_4mcmhxdmicvm7m7fk6wmumx2l_dp-dev", "api_secret": "5PN8eThNnspNgDM0RBjvDhy1kJQ3dIBuHiHxRUvu8P8", "tss_id": "638038cf-9680-4adf-a283-a83b2c2a1d18", "client_id": "K001", "admin_pin": "12345678"}, "tse_provider": "epson", "tse_required": true, "logging": {"enabled": true, "logToConsole": true, "logToFile": true, "logLevel": "debug", "consoleLevel": "debug", "fileLevel": "error", "maxLogFileSize": 10485760, "maxLogFiles": 10, "cleanupOnExit": true, "maxLogAgeDays": 30, "detailedMqttLogging": true, "cartLogging": "minimal", "webhookEnabled": true, "webhookUrl": "https://ticketpay.webhook.office.com/webhookb2/3f52cb81-3786-4b69-8d79-f905e59c7c51@c192a92f-0fec-4b9a-9eef-d0bba22513e1/IncomingWebhook/a9c487c5640c41dc9681776b7408ba8f/48bf6fa7-de86-4dda-9f44-e786e66a5bd0/V2ji-OTLbSx-GRdIEfaDcljG0tBrjXWACgXmBkULErxas1", "webhookSenderName": "POS Fehlerprotokoll"}, "zvt_config": {"merchant_name": "", "merchant_address": "", "merchant_zip_city": "", "merchant_line1": "", "merchant_line2": "", "auto_day_end": false, "auto_merchant_receipt": true}, "printOptions": {"printXML": true, "printFGL": false}, "autoUpdate": false}