using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Text.Json;
using System.Reflection;

namespace EasyTseService
{
    class Program
    {
        private static readonly object TseLock = new object(); // Sync-Objekt für Thread-Sicherheit

        static void Main(string[] args)
        {
            Console.WriteLine("EasyTSE Service wird gestartet...");

            try
            {
                // HTTP-Server auf Port 8765 starten
                HttpListener listener = new HttpListener();
                listener.Prefixes.Add("http://localhost:8765/");
                listener.Start();

                Console.WriteLine("Service läuft auf http://localhost:8765/");
                Console.WriteLine("Drücken Sie Strg+C zum Beenden");

                // Separate Task für die Bearbeitung von Anfragen
                Task.Run(() => HandleRequests(listener));

                // Auf Benutzerunterbrechung warten
                Console.CancelKeyPress += (sender, e) => {
                    e.Cancel = true;
                    listener.Stop();
                    Environment.Exit(0);
                };

                // Hauptthread am Leben halten
                while (true)
                {
                    Thread.Sleep(1000);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Starten des Services: {ex.Message}");
                Console.WriteLine("Drücken Sie eine Taste zum Beenden...");
                Console.ReadKey();
            }
        }

        static async Task HandleRequests(HttpListener listener)
        {
            dynamic tseObject = null; // Variable für das EasyTSE-Objekt

            while (listener.IsListening)
            {
                try
                {
                    // Auf Anfrage warten
                    HttpListenerContext context = await listener.GetContextAsync();
                    HttpListenerRequest request = context.Request;
                    HttpListenerResponse response = context.Response;

                    Console.WriteLine($"Anfrage empfangen: {request.HttpMethod} {request.Url.PathAndQuery}");

                    // CORS-Header setzen
                    response.AddHeader("Access-Control-Allow-Origin", "*");
                    response.AddHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
                    response.AddHeader("Access-Control-Allow-Headers", "Content-Type");

                    // OPTIONS-Anfragen direkt beantworten
                    if (request.HttpMethod == "OPTIONS")
                    {
                        response.StatusCode = 200;
                        response.Close();
                        continue;
                    }

                    // Nur POST-Anfragen bearbeiten
                    if (request.HttpMethod != "POST")
                    {
                        SendErrorResponse(response, "Nur POST-Anfragen werden unterstützt");
                        continue;
                    }

                    // Request-Body lesen
                    string requestBody;
                    using (StreamReader reader = new StreamReader(request.InputStream, request.ContentEncoding))
                    {
                        requestBody = await reader.ReadToEndAsync();
                    }

                    Console.WriteLine($"Request-Body: {requestBody}");

                    // Pfad der Anfrage auswerten
                    string path = request.Url.LocalPath;
                    dynamic responseData = null;

                    lock (TseLock) // Thread-Sicherheit für TSE-Zugriffe
                    {
                        try
                        {
                            switch (path)
                            {
                                case "/connect":
                                    responseData = HandleConnect(requestBody, ref tseObject);
                                    break;

                                case "/selfTest":
                                    responseData = HandleSelfTest(tseObject);
                                    break;

                                case "/updateTime":
                                    responseData = HandleUpdateTime(tseObject);
                                    break;

                                case "/startTransaction":
                                    responseData = HandleStartTransaction(requestBody, tseObject);
                                    break;

                                case "/updateTransaction":
                                    responseData = HandleUpdateTransaction(requestBody, tseObject);
                                    break;

                                case "/finishTransaction":
                                    responseData = HandleFinishTransaction(requestBody, tseObject);
                                    break;

                                case "/cancelTransaction":
                                    responseData = HandleCancelTransaction(requestBody, tseObject);
                                    break;

                                case "/getQRCode":
                                    responseData = HandleGetQRCode(requestBody, tseObject);
                                    break;

                                case "/getPublicKey":
                                    responseData = HandleGetPublicKey(requestBody, tseObject);
                                    break;

                                case "/info":
                                    responseData = HandleGetInfo(requestBody, tseObject);
                                    break;

                                case "/getSignature":
                                    responseData = HandleGetSignature(requestBody, tseObject);
                                    break;

                                // Neu hinzugefügte Export-Endpunkte
                                case "/exportArchive":
                                    responseData = HandleExportArchive(requestBody, tseObject);
                                    break;

                                case "/exportByDate":
                                    responseData = HandleExportByDate(requestBody, tseObject);
                                    break;

                                case "/exportByTransaction":
                                    responseData = HandleExportByTransaction(requestBody, tseObject);
                                    break;

                                case "/finishOpenTransaction":
                                    responseData = HandleFinishOpenTransaction(requestBody, tseObject);
                                    break;

                                default:
                                    throw new Exception($"Unbekannter Endpunkt: {path}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Fehler bei der Verarbeitung: {ex.Message}");
                            SendErrorResponse(response, ex.Message);
                            continue;
                        }
                    }

                    // Erfolgreiche Antwort senden
                    SendSuccessResponse(response, responseData);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Unbehandelte Ausnahme: {ex.Message}");
                    // Weiter lauschen, auch wenn ein Fehler auftritt
                }
            }
        }

        static dynamic HandleConnect(string requestBody, ref dynamic tseObject)
        {
            // Anfrageparameter parsen
            Dictionary<string, string> parameters = JsonSerializer.Deserialize<Dictionary<string, string>>(requestBody);

            try
            {
                // Wenn bereits ein TSE-Objekt existiert, dieses zuerst freigeben
                if (tseObject != null)
                {
                    try
                    {
                        // Versuchen, bestehende Verbindung zu schließen
                        tseObject.TSEClose();
                        tseObject.TSEDisconnect();
                    }
                    catch
                    {
                        // Ignorieren
                    }
                    tseObject = null;
                }

                // Neues EasyTSE-Objekt erstellen
                Console.WriteLine("Erstelle neues EasyTSE-Objekt...");
                try
                {
                    // Versuche zuerst, das Objekt über die GUID zu erstellen
                    tseObject = Activator.CreateInstance(Type.GetTypeFromCLSID(new Guid("6D83ECDC-ACE1-4D54-AD77-70D1590743D8")));
                    Console.WriteLine("EasyTSE-Objekt über GUID erfolgreich erstellt");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler beim Erstellen über GUID: {ex.Message}");

                    // Fallback auf ProgID
                    try
                    {
                        tseObject = Activator.CreateInstance(Type.GetTypeFromProgID("EasyTSE.EpsonTSE"));
                        Console.WriteLine("EasyTSE-Objekt über ProgID erfolgreich erstellt");
                    }
                    catch (Exception ex2)
                    {
                        throw new Exception($"Konnte EasyTSE-Objekt nicht erstellen: {ex2.Message}");
                    }
                }

                // Weitere Einstellungen
                tseObject.nLastSelfTest = 1; // Automatischer Selbsttest
                tseObject.nLastUpdateTime = 1; // Automatisches Zeitupdate
                tseObject.nHoldConnection = 1; // Verbindung halten
                tseObject.nAutoSetup = 1; // Automatisches Setup
                tseObject.nQRCodeMaker = 1; // QR-Code Generierung aktivieren

                // Temp-Verzeichnis für QR-Codes festlegen
                string tempDir = Path.Combine(Path.GetTempPath(), "EasyTSE_QR");
                if (!Directory.Exists(tempDir))
                {
                    Directory.CreateDirectory(tempDir);
                }
                tseObject.cTempDir = tempDir;

                // Zugangsdaten setzen
                tseObject.TSE_PUK = parameters["puk"];
                tseObject.TSE_AdminPIN = parameters["adminPin"];
                tseObject.TSE_TimeAdminPIN = parameters["timeAdminPin"];
                tseObject.TSE_SecretKey = parameters["secretKey"];
                tseObject.TSE_IP = parameters["ip"];
                tseObject.TSE_Port = Convert.ToInt32(parameters["port"]);
                tseObject.TSE_ClientID = parameters["clientId"];
                tseObject.TSE_UserID = parameters["clientId"]; // UserID = ClientID

                // Geräteliste abrufen und erste DeviceID verwenden
                Console.WriteLine("Suche verfügbare TSE-Geräte...");
                int deviceResult = tseObject.GetDeviceList("");
                if (deviceResult == 1 && !string.IsNullOrEmpty((string)tseObject.cDeviceList))
                {
                    string[] devices = ((string)tseObject.cDeviceList).Split(';');
                    Console.WriteLine($"Gefundene Geräte: {(string)tseObject.cDeviceList}");

                    if (devices.Length > 0 && !string.IsNullOrEmpty(devices[0]))
                    {
                        // Die erste gefundene DeviceID verwenden
                        tseObject.TSE_DeviceID = devices[0];
                        Console.WriteLine($"Verwende automatisch gefundene DeviceID: {devices[0]}");
                    }
                    else
                    {
                        // Konfigurierte DeviceID verwenden
                        tseObject.TSE_DeviceID = parameters["deviceId"];
                        Console.WriteLine($"Keine Geräte gefunden, verwende konfigurierte DeviceID: {parameters["deviceId"]}");
                    }
                }
                else
                {
                    // Konfigurierte DeviceID verwenden
                    tseObject.TSE_DeviceID = parameters["deviceId"];
                    Console.WriteLine($"Geräteliste konnte nicht abgerufen werden, verwende konfigurierte DeviceID: {parameters["deviceId"]}");
                }

                // Verbindung zur TSE herstellen
                Console.WriteLine("Stelle Verbindung zur TSE her...");
                Console.WriteLine($"Verbindungsparameter: IP={tseObject.TSE_IP}, Port={tseObject.TSE_Port}, DeviceID={tseObject.TSE_DeviceID}, ClientID={tseObject.TSE_ClientID}");

                try
                {
                    // Versuche zuerst, das Objekt über die GUID zu erstellen
                    int connectResult = tseObject.TSEConnectOpenSend("GetStorageInfo");
                    if (connectResult != 1)
                    {
                        string errorMessage = tseObject.cErrorList;
                        Console.WriteLine($"Fehler bei TSEConnectOpenSend: {errorMessage}");

                        // Alternativer Verbindungsversuch mit Connect
                        Console.WriteLine("Versuche alternative Verbindungsmethode (Connect)...");
                        connectResult = tseObject.TSEConnect();
                        if (connectResult != 1)
                        {
                            string altErrorMessage = tseObject.ErrorGet(connectResult);
                            throw new Exception($"Alle Verbindungsversuche fehlgeschlagen: {altErrorMessage}");
                        }
                    }

                    Console.WriteLine("Verbindung zur TSE erfolgreich hergestellt");

                    // Seriennummer abrufen - ohne StatusGet zu verwenden
                    try {
                        string serialNumber = tseObject.cSNfromPublicKey;
                        Console.WriteLine($"TSE-Seriennummer: {serialNumber}");

                        var response = new {
                            success = true,
                            serialNumber = serialNumber
                        };

                        string jsonResponse = JsonSerializer.Serialize(response);
                        Console.WriteLine($"Sende Antwort: {jsonResponse}");

                        return jsonResponse;
                    }
                    catch (Exception ex) {
                        Console.WriteLine($"Warnung: Seriennummer konnte nicht abgerufen werden: {ex.Message}");
                        // Trotz Fehler bei der Seriennummer Erfolg zurückgeben
                        var response = new {
                            success = true
                        };

                        string jsonResponse = JsonSerializer.Serialize(response);
                        Console.WriteLine($"Sende Antwort: {jsonResponse}");

                        return jsonResponse;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler bei TSE-Verbindung: {ex.Message}");
                    throw new Exception($"TSE-Verbindung fehlgeschlagen: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler bei TSE-Verbindung: {ex.Message}");
                throw new Exception($"TSE-Verbindung fehlgeschlagen: {ex.Message}");
            }
        }

        static dynamic HandleSelfTest(dynamic tseObject)
        {
            if (tseObject == null)
            {
                throw new Exception("Keine TSE-Verbindung vorhanden");
            }

            Console.WriteLine("Führe TSE-Selbsttest durch...");
            int result = tseObject.Stack_RunTSESelfTest();

            if (result != 1)
            {
                throw new Exception($"TSE-Selbsttest fehlgeschlagen: {tseObject.cErrorList}");
            }

            Console.WriteLine("TSE-Selbsttest erfolgreich");

            return new { success = true };
        }

        static dynamic HandleUpdateTime(dynamic tseObject)
        {
            if (tseObject == null)
            {
                throw new Exception("Keine TSE-Verbindung vorhanden");
            }

            Console.WriteLine("Aktualisiere TSE-Zeit...");
            int result = tseObject.Stack_UpdateTime();

            if (result != 1)
            {
                throw new Exception($"TSE-Zeitaktualisierung fehlgeschlagen: {tseObject.cErrorList}");
            }

            Console.WriteLine("TSE-Zeit erfolgreich aktualisiert");

            return new { success = true };
        }

        static dynamic HandleStartTransaction(string requestBody, dynamic tseObject)
        {
            if (tseObject == null)
            {
                throw new Exception("Keine TSE-Verbindung vorhanden");
            }

            // Parameter aus Request-Body parsen
            JsonDocument doc = JsonDocument.Parse(requestBody);
            JsonElement root = doc.RootElement;

            string clientId = root.GetProperty("clientId").GetString();
            string processData = "";
            bool holdConnection = false;

            if (root.TryGetProperty("holdConnection", out JsonElement holdConnectionElement))
            {
                holdConnection = holdConnectionElement.GetBoolean();
            }

            // ProcessData auslesen, falls vorhanden
            if (root.TryGetProperty("processData", out JsonElement processDataElement))
            {
                processData = processDataElement.GetString();
                Console.WriteLine($"ProcessData erhalten: {processData}");

                // Falls ProcessData vorhanden ist, diese an die TSE übergeben
                tseObject.TSE_ProcessData = processData;
            }

            Console.WriteLine($"Starte TSE-Transaktion mit Stack_StartTransaction");

            // Parameter setzen
            tseObject.TSE_UserID = clientId;
            tseObject.nHoldConnection = holdConnection ? 1 : 0;

            // Transaktion starten
            int result = tseObject.Stack_StartTransaction();

            if (result != 1)
            {
                throw new Exception($"Transaktion konnte nicht gestartet werden: {tseObject.cErrorList}");
            }

            Console.WriteLine("TSE-Transaktion erfolgreich gestartet");

            // Variablen für das Ergebnis
            string transactionNumber = "";
            string logTime = "";
            string signature = "";
            string signatureCounter = "";
            string serialNumber = "";

            // Versuch, die Seriennummer zu erhalten
            try {
                serialNumber = tseObject.cSNfromPublicKey?.ToString() ?? "";
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Lesen von cSNfromPublicKey: {ex.Message}");
            }

            // VERSUCHE METHODE 1: Direkte Zugriffe auf bekannte TSE-Eigenschaften für Signatur
            try {
                // Korrigiert: Zugriff auf oTransActionObjectStart statt auf TSE_Signature
                try {
                    signature = tseObject.oTransActionObjectStart?.Signature?.ToString() ?? "";
                    Console.WriteLine($"Signature aus tseObject.oTransActionObjectStart.Signature: {signature}");
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Lesen von tseObject.oTransActionObjectStart.Signature: {ex.Message}");
                }

                if (string.IsNullOrEmpty(signature))
                {
                    try {
                        // Alternativer Versuch über oResult.Signature falls verfügbar
                        signature = tseObject.oResult?.Signature?.ToString() ?? "";
                        Console.WriteLine($"Signature aus tseObject.oResult.Signature: {signature}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von tseObject.oResult.Signature: {ex.Message}");
                    }
                }

                if (string.IsNullOrEmpty(signature))
                {
                    try {
                        signature = tseObject.cSignature?.ToString() ?? "";
                        Console.WriteLine($"Signature aus cSignature: {signature}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von cSignature: {ex.Message}");
                    }
                }

                if (string.IsNullOrEmpty(signature))
                {
                    try {
                        signature = tseObject.cSigValue?.ToString() ?? "";
                        Console.WriteLine($"Signature aus cSigValue: {signature}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von cSigValue: {ex.Message}");
                    }
                }

                if (string.IsNullOrEmpty(signature))
                {
                    try {
                        signature = tseObject.cSignatureValue?.ToString() ?? "";
                        Console.WriteLine($"Signature aus cSignatureValue: {signature}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von cSignatureValue: {ex.Message}");
                    }
                }
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim direkten Zugriff auf TSE-Signatureigenschaften: {ex.Message}");
            }

            // VERSUCHE METHODE 2: Eigenschaften aus dem COM Objekt lesen
            try {
                dynamic startTransObj = tseObject.oStartTransaction;
                Console.WriteLine("tseObject.oStartTransaction ist verfügbar");

                // Priorität: Direkter Zugriff auf oTransActionObjectStart aus der Dokumentation
                try {
                    dynamic transObjStart = tseObject.oTransActionObjectStart;
                    Console.WriteLine("tseObject.oTransActionObjectStart ist verfügbar");

                    try {
                        transactionNumber = transObjStart.TransactionNumber?.ToString() ?? "";
                        Console.WriteLine($"Transaktionsnummer aus oTransActionObjectStart.TransactionNumber: {transactionNumber}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oTransActionObjectStart.TransactionNumber: {ex.Message}");
                    }

                    try {
                        logTime = transObjStart.LogTime?.ToString() ?? "";
                        // Normalisiere auf deutsches Format
                        if (!string.IsNullOrEmpty(logTime) && logTime.Contains('T') && (logTime.Contains('Z') || logTime.Contains('+')))
                        {
                            DateTime parsedTime = DateTime.Parse(logTime);
                            logTime = parsedTime.ToString("dd.MM.yyyy HH:mm:ss");
                        }
                        Console.WriteLine($"LogTime aus oTransActionObjectStart.LogTime: {logTime}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oTransActionObjectStart.LogTime: {ex.Message}");
                    }

                    try {
                        signature = transObjStart.Signature?.ToString() ?? "";
                        Console.WriteLine($"Signature aus oTransActionObjectStart.Signature: {signature}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oTransActionObjectStart.Signature: {ex.Message}");
                    }

                    try {
                        signatureCounter = transObjStart.SignatureCounter?.ToString() ?? "";
                        Console.WriteLine($"SignatureCounter aus oTransActionObjectStart.SignatureCounter: {signatureCounter}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oTransActionObjectStart.SignatureCounter: {ex.Message}");
                    }

                    try {
                        serialNumber = transObjStart.SerialNumber?.ToString() ?? "";
                        Console.WriteLine($"SerialNumber aus oTransActionObjectStart.SerialNumber: {serialNumber}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oTransActionObjectStart.SerialNumber: {ex.Message}");
                    }
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Zugriff auf oTransActionObjectStart: {ex.Message}");
                }

                // Fallback: Versuche, die Eigenschaften von oStartTransaction zu reflektieren
                try {
                    Type type = startTransObj.GetType();
                    Console.WriteLine("Verfügbare Eigenschaften in oStartTransaction:");
                    foreach (var prop in type.GetProperties())
                    {
                        try {
                            var value = prop.GetValue(startTransObj);
                            Console.WriteLine($"- {prop.Name}: {value}");

                            // Falls eine der Eigenschaften den TransactionNumber enthält
                            if (prop.Name.Contains("Transaction") || prop.Name.Contains("Tran"))
                            {
                                transactionNumber = value?.ToString() ?? "";
                                Console.WriteLine($"Mögliche Transaktionsnummer gefunden: {transactionNumber}");
                            }

                            // Falls eine der Eigenschaften LogTime enthält
                            if (prop.Name.Contains("LogTime") || prop.Name.Contains("Time"))
                            {
                                logTime = value?.ToString() ?? "";
                                // Normalisiere auf deutsches Format
                                if (!string.IsNullOrEmpty(logTime) && logTime.Contains('T') && (logTime.Contains('Z') || logTime.Contains('+')))
                                {
                                    try
                                    {
                                        DateTime parsedTime = DateTime.Parse(logTime);
                                        logTime = parsedTime.ToString("dd.MM.yyyy HH:mm:ss");
                                    }
                                    catch (Exception parseEx)
                                    {
                                        Console.WriteLine($"Fehler beim Konvertieren der LogTime: {parseEx.Message}");
                                    }
                                }
                                Console.WriteLine($"Mögliche LogTime gefunden: {logTime}");
                            }

                            // Falls eine der Eigenschaften Signature enthält
                            if (prop.Name.Contains("Signature") || prop.Name.Contains("Sig"))
                            {
                                signature = value?.ToString() ?? "";
                                Console.WriteLine($"Mögliche Signature gefunden: {signature}");
                            }

                            // Falls eine der Eigenschaften SignatureCounter enthält
                            if (prop.Name.Contains("SignatureCounter") || prop.Name.Contains("SigCounter"))
                            {
                                signatureCounter = value?.ToString() ?? "";
                                Console.WriteLine($"Möglicher SignatureCounter gefunden: {signatureCounter}");
                            }

                            // Falls eine der Eigenschaften SerialNumber enthält
                            if (prop.Name.Contains("SerialNumber") || prop.Name.Contains("Serial"))
                            {
                                serialNumber = value?.ToString() ?? "";
                                Console.WriteLine($"Mögliche SerialNumber gefunden: {serialNumber}");
                            }
                        } catch (Exception ex) {
                            Console.WriteLine($"Fehler beim Lesen der Eigenschaft {prop.Name}: {ex.Message}");
                        }
                    }
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Reflektieren von oStartTransaction: {ex.Message}");
                }

                // Versuch, direkt auf bekannte Eigenschaften zuzugreifen
                try {
                    transactionNumber = startTransObj.TransactionNumber?.ToString() ?? "";
                    Console.WriteLine($"Transaktionsnummer direkt aus oStartTransaction.TransactionNumber: {transactionNumber}");
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Lesen von oStartTransaction.TransactionNumber: {ex.Message}");
                }

                // Versuch, auf Output Eigenschaften zuzugreifen (falls verfügbar)
                try {
                    dynamic output = startTransObj.Output;
                    Console.WriteLine("startTransObj.Output ist verfügbar");

                    try {
                        transactionNumber = output.TransactionNumber?.ToString() ?? "";
                        Console.WriteLine($"Transaktionsnummer aus output.TransactionNumber: {transactionNumber}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von output.TransactionNumber: {ex.Message}");
                    }

                    try {
                        logTime = output.LogTime?.ToString() ?? "";
                        // Normalisiere auf deutsches Format
                        if (!string.IsNullOrEmpty(logTime) && logTime.Contains('T') && (logTime.Contains('Z') || logTime.Contains('+')))
                        {
                            DateTime parsedTime = DateTime.Parse(logTime);
                            logTime = parsedTime.ToString("dd.MM.yyyy HH:mm:ss");
                        }
                        Console.WriteLine($"LogTime aus output.LogTime: {logTime}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von output.LogTime: {ex.Message}");
                    }

                    try {
                        signature = output.Signature?.ToString() ?? "";
                        Console.WriteLine($"Signature aus output.Signature: {signature}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von output.Signature: {ex.Message}");
                    }

                    try {
                        signatureCounter = output.SignatureCounter?.ToString() ?? "";
                        Console.WriteLine($"SignatureCounter aus output.SignatureCounter: {signatureCounter}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von output.SignatureCounter: {ex.Message}");
                    }

                    try {
                        serialNumber = output.SerialNumber?.ToString() ?? "";
                        Console.WriteLine($"SerialNumber aus output.SerialNumber: {serialNumber}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von output.SerialNumber: {ex.Message}");
                    }

                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Zugriff auf startTransObj.Output: {ex.Message}");
                }

            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Zugriff auf oStartTransaction: {ex.Message}");
            }

            // VERSUCHE METHODE 3: Direkte Eigenschaften von tseObject
            try {
                Type type = tseObject.GetType();
                Console.WriteLine("Direkte Eigenschaften des tseObject:");
                foreach (var prop in type.GetProperties())
                {
                    if (prop.Name.StartsWith("o") || prop.Name.StartsWith("c") || prop.Name.StartsWith("n"))
                    {
                        try {
                            var value = prop.GetValue(tseObject);
                            Console.WriteLine($"Property: {prop.Name}, Wert: {(value != null ? "hat Wert" : "null")}");

                            // Zusätzliche Überprüfung auf Signatur-relevante Eigenschaften
                            if (string.IsNullOrEmpty(signature) &&
                                (prop.Name.Contains("Sig") || prop.Name.Contains("sig") ||
                                 prop.Name.Contains("Sign") || prop.Name.Contains("sign")))
                            {
                                var propValue = value?.ToString();
                                if (!string.IsNullOrEmpty(propValue))
                                {
                                    signature = propValue;
                                    Console.WriteLine($"Signatur gefunden in Property {prop.Name}: {signature}");
                                }
                            }
                        } catch (Exception ex) {
                            Console.WriteLine($"Fehler beim Lesen der Eigenschaft {prop.Name}: {ex.Message}");
                        }
                    }
                }
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Reflektieren des tseObject: {ex.Message}");
            }

            // VERSUCH METHODE 4: Versuch, auf TSE_Transactionnumber zuzugreifen
            try {
                transactionNumber = tseObject.TSE_Transactionnumber?.ToString() ?? "";
                Console.WriteLine($"TransactionNumber aus tseObject.TSE_Transactionnumber: {transactionNumber}");

                // Wenn wir die TSE_Transactionnumber erfolgreich gelesen haben,
                // ist es wahrscheinlich, dass auch andere TSE_-Eigenschaften existieren
                if (!string.IsNullOrEmpty(transactionNumber))
                {
                    try {
                        signature = tseObject.TSE_SignatureValue?.ToString() ?? "";
                        Console.WriteLine($"Signature aus tseObject.TSE_SignatureValue: {signature}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von tseObject.TSE_SignatureValue: {ex.Message}");
                    }
                }
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Lesen von tseObject.TSE_Transactionnumber: {ex.Message}");

                // Alternativ versuchen, die Transaktionsnummer aus oTransActionObjectStart zu lesen
                try {
                    transactionNumber = tseObject.oTransActionObjectStart?.TransactionNumber?.ToString() ?? "";
                    Console.WriteLine($"TransactionNumber aus tseObject.oTransActionObjectStart.TransactionNumber: {transactionNumber}");
                } catch (Exception innerEx) {
                    Console.WriteLine($"Fehler beim Lesen von tseObject.oTransActionObjectStart.TransactionNumber: {innerEx.Message}");
                }
            }

            // Falls trotz aller Versuche keine Transaktionsnummer gefunden wurde
            if (string.IsNullOrEmpty(transactionNumber))
            {
                Console.WriteLine("Verwende Fallback-TransactionNumber '1'");
                transactionNumber = "1";
            }
            else
            {
                Console.WriteLine($"TransactionNumber: {transactionNumber}");
            }

            // Falls keine LogTime gefunden wurde
            if (string.IsNullOrEmpty(logTime))
            {
                Console.WriteLine("Verwende aktuelles Datum/Uhrzeit als LogTime");
                logTime = DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss");
            }
            else
            {
                // Normalisiere LogTime auf deutsches Format, falls es im ISO-Format vorliegt
                try
                {
                    if (logTime.Contains('T') && (logTime.Contains('Z') || logTime.Contains('+')))
                    {
                        // ISO-Format erkannt, konvertiere zu deutschem Format
                        DateTime parsedTime = DateTime.Parse(logTime);
                        logTime = parsedTime.ToString("dd.MM.yyyy HH:mm:ss");
                        Console.WriteLine($"LogTime von ISO zu deutschem Format konvertiert: {logTime}");
                    }
                    else
                    {
                        Console.WriteLine($"LogTime bereits im deutschen Format: {logTime}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler beim Konvertieren der LogTime: {ex.Message}, verwende Original: {logTime}");
                }
            }

            // Falls keine Signatur gefunden wurde
            if (string.IsNullOrEmpty(signature))
            {
                // Letzte Versuche für die Signatur - probieren verschiedene bekannte Eigenschaftsnamen
                string[] possibleSignatureProps = new string[] {
                    "SignatureValue", "Signature", "SignatureStart", "SigValue",
                    "StartSignature", "TseSignature", "Sig"
                };

                foreach (var propName in possibleSignatureProps)
                {
                    try {
                        var dynamicValue = tseObject.GetType().GetProperty(propName)?.GetValue(tseObject);
                        if (dynamicValue != null)
                        {
                            signature = dynamicValue.ToString();
                            Console.WriteLine($"Signatur gefunden in {propName}: {signature}");
                            break;
                        }
                    } catch {
                        // Ignorieren und mit nächstem Versuch fortfahren
                    }
                }

                if (string.IsNullOrEmpty(signature))
                {
                    Console.WriteLine("Keine Signatur gefunden, verwende Fallback");
                    signature = "SignatureInit";  // Default-Wert bereitstellen
                }
            }
            else
            {
                Console.WriteLine($"Signature: {signature}");
            }

            // Falls kein SignatureCounter gefunden wurde
            if (string.IsNullOrEmpty(signatureCounter))
            {
                Console.WriteLine("Verwende Fallback-SignatureCounter '1'");
                signatureCounter = "1";
            }
            else
            {
                Console.WriteLine($"SignatureCounter: {signatureCounter}");
            }

            // Falls keine SerialNumber gefunden wurde
            if (string.IsNullOrEmpty(serialNumber))
            {
                Console.WriteLine("Keine SerialNumber gefunden");
                serialNumber = "";
            }
            else
            {
                Console.WriteLine($"SerialNumber: {serialNumber}");
            }

            return new {
                success = true,
                data = new {
                    transactionNumber = transactionNumber,
                    logTime = logTime,
                    signatureStart = signature,
                    signatureCounter = signatureCounter,
                    serialNumber = serialNumber
                }
            };
        }

        static dynamic HandleUpdateTransaction(string requestBody, dynamic tseObject)
        {
            if (tseObject == null)
            {
                throw new Exception("Keine TSE-Verbindung vorhanden");
            }

            // Parameter aus Request-Body parsen
            JsonDocument doc = JsonDocument.Parse(requestBody);
            JsonElement root = doc.RootElement;

            string clientId = root.GetProperty("clientId").GetString();
            string transactionNumber = root.GetProperty("transactionNumber").GetString();
            string processData = "";
            bool holdConnection = false;

            if (root.TryGetProperty("holdConnection", out JsonElement holdConnectionElement))
            {
                holdConnection = holdConnectionElement.GetBoolean();
            }

            // ProcessData auslesen, falls vorhanden
            if (root.TryGetProperty("processData", out JsonElement processDataElement))
            {
                processData = processDataElement.GetString();
                Console.WriteLine($"ProcessData erhalten: {processData}");

                // Falls ProcessData vorhanden ist, diese an die TSE übergeben
                tseObject.TSE_ProcessData = processData;
            }

            Console.WriteLine($"Aktualisiere TSE-Transaktion: {transactionNumber} mit ProcessData: {processData}");

            // Parameter setzen
            tseObject.TSE_UserID = clientId;
            tseObject.nHoldConnection = holdConnection ? 1 : 0;

            // Transaktionsnummer für die Aktualisierung setzen
            tseObject.TSE_Transactionnumber = transactionNumber;

            // Stack_UpdateTransaction mit ProcessData, ProcessType und VorgangsTyp aufrufen
            int result = tseObject.Stack_UpdateTransaction(processData, 1, 1);

            if (result != 1)
            {
                throw new Exception($"Transaktion konnte nicht aktualisiert werden: {tseObject.cErrorList}");
            }

            Console.WriteLine("TSE-Transaktion erfolgreich aktualisiert");

            // DEBUGGING: Verfügbare Properties ausgeben
            try {
                dynamic resultObj = tseObject.oResult;
                Console.WriteLine("tseObject.oResult ist verfügbar");
                foreach (var prop in resultObj.GetType().GetProperties())
                {
                    try {
                        Console.WriteLine($"Property: {prop.Name}, Wert: {prop.GetValue(resultObj)}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Property {prop.Name} konnte nicht gelesen werden: {ex.Message}");
                    }
                }
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Zugriff auf oResult: {ex.Message}");
            }

            try {
                dynamic updateObj = tseObject.oTransActionObjectUpdate;
                Console.WriteLine("tseObject.oTransActionObjectUpdate ist verfügbar");
                foreach (var prop in updateObj.GetType().GetProperties())
                {
                    try {
                        Console.WriteLine($"Update-Property: {prop.Name}, Wert: {prop.GetValue(updateObj)}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Update-Property {prop.Name} konnte nicht gelesen werden: {ex.Message}");
                    }
                }
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Zugriff auf oTransActionObjectUpdate: {ex.Message}");
            }

            // In diesem Abschnitt verwenden wir oResult, wenn oTransActionObjectUpdate nicht verfügbar ist
            string logTime = "";
            string signatureValue = "";
            string signatureCounter = "";

            try {
                dynamic updateData = tseObject.oTransActionObjectUpdate;
                logTime = updateData.LogTime.ToString();
                // Normalisiere auf deutsches Format
                if (logTime.Contains('T') && (logTime.Contains('Z') || logTime.Contains('+')))
                {
                    DateTime parsedTime = DateTime.Parse(logTime);
                    logTime = parsedTime.ToString("dd.MM.yyyy HH:mm:ss");
                }
                signatureValue = updateData.Signature.ToString();
                signatureCounter = updateData.SignatureCounter.ToString();
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Lesen von oTransActionObjectUpdate: {ex.Message}, versuche oResult");

                try {
                    dynamic resultData = tseObject.oResult;

                    try {
                        logTime = resultData.LogTime.ToString();
                        // Normalisiere auf deutsches Format
                        if (logTime.Contains('T') && (logTime.Contains('Z') || logTime.Contains('+')))
                        {
                            DateTime parsedTime = DateTime.Parse(logTime);
                            logTime = parsedTime.ToString("dd.MM.yyyy HH:mm:ss");
                        }
                    } catch {
                        logTime = DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss");
                    }

                    try {
                        signatureValue = resultData.SignatureValue.ToString();
                    } catch {
                        try {
                            signatureValue = resultData.Signature.ToString();
                        } catch {
                            signatureValue = "";
                        }
                    }

                    try {
                        signatureCounter = resultData.SignatureCounter.ToString();
                    } catch {
                        signatureCounter = "0";
                    }
                } catch (Exception innerEx) {
                    Console.WriteLine($"Auch oResult konnte nicht gelesen werden: {innerEx.Message}");
                    logTime = DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss");
                    signatureValue = "";
                    signatureCounter = "0";
                }
            }

            return new {
                success = true,
                data = new {
                    transactionNumber = transactionNumber,
                    logTime = logTime,
                    signatureValue = signatureValue,
                    signatureCounter = signatureCounter
                }
            };
        }

        static dynamic HandleFinishTransaction(string requestBody, dynamic tseObject)
        {
            if (tseObject == null)
            {
                throw new Exception("Keine TSE-Verbindung vorhanden");
            }

            // Parameter aus Request-Body parsen
            JsonDocument doc = JsonDocument.Parse(requestBody);
            JsonElement root = doc.RootElement;

            string clientId = root.GetProperty("clientId").GetString();
            string transactionNumber = root.GetProperty("transactionNumber").GetString();
            string processData = root.GetProperty("processData").GetString();

            int processType = 1; // Default-Wert
            if (root.TryGetProperty("processType", out JsonElement processTypeElement))
            {
                processType = processTypeElement.GetInt32();
            }

            int vorgangsType = 1; // Default-Wert
            if (root.TryGetProperty("vorgangsType", out JsonElement vorgangsTypeElement))
            {
                vorgangsType = vorgangsTypeElement.GetInt32();
            }

            double totalAmount = 0;
            if (root.TryGetProperty("totalAmount", out JsonElement totalAmountElement))
            {
                totalAmount = totalAmountElement.GetDouble();
            }

            // JSON-Arrays für TaxSet und Payments auslesen (falls vorhanden)
            List<JsonElement> taxSet = new List<JsonElement>();
            if (root.TryGetProperty("taxSet", out JsonElement taxSetElement) && taxSetElement.ValueKind == JsonValueKind.Array)
            {
                foreach (JsonElement taxItem in taxSetElement.EnumerateArray())
                {
                    taxSet.Add(taxItem);
                }
            }

            List<JsonElement> payments = new List<JsonElement>();
            if (root.TryGetProperty("payments", out JsonElement paymentsElement) && paymentsElement.ValueKind == JsonValueKind.Array)
            {
                foreach (JsonElement paymentItem in paymentsElement.EnumerateArray())
                {
                    payments.Add(paymentItem);
                }
            }

            Console.WriteLine($"Schließe TSE-Transaktion: {transactionNumber} mit ProcessData: {processData}");

            // Parameter für FinishTransaction vorbereiten
            tseObject.TSE_UserID = clientId;
            tseObject.TSE_StartTransactionNumber = transactionNumber;
            tseObject.TSE_ProcessData = processData;
            tseObject.TSE_ProcessType = processType;
            tseObject.TSE_VorgangsType = vorgangsType;

            bool useSimpleFinish = true;
            if (taxSet.Count > 0 && payments.Count > 0)
            {
                // Erweiterte Informationen für FinishTransaction
                try {
                    // Betragssummen ermitteln (für Standardformat)
                    double betragMwSt1 = 0; // 19%
                    double betragMwSt2 = 0; // 7%
                    double betragMwSt3 = 0; // 10.7%
                    double betragMwSt4 = 0; // andere
                    double betragMwSt0 = 0; // 0%

                    double betragBar = 0;
                    double betragUnbar = 0;

                    // TaxSet analysieren
                    foreach (JsonElement taxItem in taxSet)
                    {
                        if (taxItem.TryGetProperty("taxRate", out JsonElement taxRateElement) &&
                            taxItem.TryGetProperty("amount", out JsonElement amountElement))
                        {
                            double taxRate = taxRateElement.GetDouble();
                            double amount = amountElement.GetDouble();

                            if (Math.Abs(taxRate - 19) < 0.01)
                            {
                                betragMwSt1 = amount;
                            }
                            else if (Math.Abs(taxRate - 7) < 0.01)
                            {
                                betragMwSt2 = amount;
                            }
                            else if (Math.Abs(taxRate - 10.7) < 0.01)
                            {
                                betragMwSt3 = amount;
                            }
                            else if (Math.Abs(taxRate) < 0.01)
                            {
                                betragMwSt0 = amount;
                            }
                            else
                            {
                                betragMwSt4 = amount;
                            }
                        }
                    }

                    // Payments analysieren
                    foreach (JsonElement paymentItem in payments)
                    {
                        if (paymentItem.TryGetProperty("type", out JsonElement typeElement) &&
                            paymentItem.TryGetProperty("amount", out JsonElement amountElement))
                        {
                            string type = typeElement.GetString();
                            double amount = amountElement.GetDouble();

                            if (type.ToLower() == "bar")
                            {
                                betragBar += amount;
                            }
                            else
                            {
                                betragUnbar += amount;
                            }
                        }
                    }

                    Console.WriteLine("Verwende Standard-Format für Stack_FinishTransactionDouble");
                    int result = tseObject.Stack_FinishTransactionDouble(
                        processData,
                        processType,
                        vorgangsType,
                        betragMwSt1,
                        betragMwSt2,
                        betragMwSt3,
                        betragMwSt4,
                        betragMwSt0,
                        betragBar,
                        betragUnbar
                    );

                    if (result != 1)
                    {
                        throw new Exception($"Transaktion konnte nicht abgeschlossen werden: {tseObject.cErrorList}");
                    }

                    useSimpleFinish = false;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler beim erweiterten FinishTransaction: {ex.Message}. Verwende einfachen FinishTransaction als Fallback.");
                    useSimpleFinish = true;
                }
            }

            // Einfache Variante als Fallback
            if (useSimpleFinish)
            {
                int result = tseObject.Stack_FinishTransaction(processData, processType, vorgangsType);

                if (result != 1)
                {
                    throw new Exception($"Transaktion konnte nicht abgeschlossen werden: {tseObject.cErrorList}");
                }
            }

            Console.WriteLine("TSE-Transaktion erfolgreich abgeschlossen");

            // Ergebnis extrahieren
            string signatureCounter = "";
            string signature = "";
            string logTime = "";

            Console.WriteLine("tseObject.oResult ist verfügbar");

            // Versuch 1: Direkt über oTransActionObjectFinish (dokumentierte Methode)
            try {
                dynamic finishTransObj = tseObject.oTransActionObjectFinish;
                Console.WriteLine("tseObject.oTransActionObjectFinish ist verfügbar");

                try {
                    logTime = finishTransObj.LogTime?.ToString() ?? "";
                    // Normalisiere auf deutsches Format
                    if (!string.IsNullOrEmpty(logTime) && logTime.Contains('T') && (logTime.Contains('Z') || logTime.Contains('+')))
                    {
                        DateTime parsedTime = DateTime.Parse(logTime);
                        logTime = parsedTime.ToString("dd.MM.yyyy HH:mm:ss");
                    }
                    Console.WriteLine($"LogTime aus oTransActionObjectFinish.LogTime: {logTime}");
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Lesen von oTransActionObjectFinish.LogTime: {ex.Message}");
                }

                try {
                    signature = finishTransObj.Signature?.ToString() ?? "";
                    Console.WriteLine($"Signature aus oTransActionObjectFinish.Signature: {signature}");
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Lesen von oTransActionObjectFinish.Signature: {ex.Message}");
                }

                try {
                    signatureCounter = finishTransObj.SignatureCounter?.ToString() ?? "";
                    Console.WriteLine($"SignatureCounter aus oTransActionObjectFinish.SignatureCounter: {signatureCounter}");
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Lesen von oTransActionObjectFinish.SignatureCounter: {ex.Message}");
                }
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Zugriff auf oTransActionObjectFinish: {ex.Message}");
            }

            // Versuch 2: Fallback über oFinishTransaction
            if (string.IsNullOrEmpty(signature) || string.IsNullOrEmpty(logTime) || string.IsNullOrEmpty(signatureCounter))
            {
                try {
                    dynamic finishTransObj = tseObject.oFinishTransaction;
                    Console.WriteLine("tseObject.oFinishTransaction ist verfügbar");

                    try {
                        logTime = finishTransObj.LogTime?.ToString() ?? "";
                        // Normalisiere auf deutsches Format
                        if (!string.IsNullOrEmpty(logTime) && logTime.Contains('T') && (logTime.Contains('Z') || logTime.Contains('+')))
                        {
                            DateTime parsedTime = DateTime.Parse(logTime);
                            logTime = parsedTime.ToString("dd.MM.yyyy HH:mm:ss");
                        }
                        Console.WriteLine($"LogTime aus oFinishTransaction.LogTime: {logTime}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oFinishTransaction.LogTime: {ex.Message}, versuche oResult");
                    }

                    try {
                        signature = finishTransObj.Signature?.ToString() ?? "";
                        Console.WriteLine($"Signature aus oFinishTransaction.Signature: {signature}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oFinishTransaction.Signature: {ex.Message}");
                    }

                    try {
                        signatureCounter = finishTransObj.SignatureCounter?.ToString() ?? "";
                        Console.WriteLine($"SignatureCounter aus oFinishTransaction.SignatureCounter: {signatureCounter}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oFinishTransaction.SignatureCounter: {ex.Message}");
                    }
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Zugriff auf oFinishTransaction: {ex.Message}");
                }
            }

            // Versuch 3: Über oResult, falls vorhanden
            if (string.IsNullOrEmpty(signature) || string.IsNullOrEmpty(logTime) || string.IsNullOrEmpty(signatureCounter))
            {
                try {
                    dynamic resultObj = tseObject.oResult;

                    try {
                        logTime = resultObj.LogTime?.ToString() ?? "";
                        // Normalisiere auf deutsches Format
                        if (!string.IsNullOrEmpty(logTime) && logTime.Contains('T') && (logTime.Contains('Z') || logTime.Contains('+')))
                        {
                            DateTime parsedTime = DateTime.Parse(logTime);
                            logTime = parsedTime.ToString("dd.MM.yyyy HH:mm:ss");
                        }
                        Console.WriteLine($"LogTime aus oResult.LogTime: {logTime}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oResult.LogTime: {ex.Message}");
                    }

                    try {
                        signature = resultObj.Signature?.ToString() ?? "";
                        Console.WriteLine($"Signature aus oResult.Signature: {signature}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oResult.Signature: {ex.Message}");
                    }

                    try {
                        signatureCounter = resultObj.SignatureCounter?.ToString() ?? "";
                        Console.WriteLine($"SignatureCounter aus oResult.SignatureCounter: {signatureCounter}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oResult.SignatureCounter: {ex.Message}");
                    }
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Zugriff auf oResult: {ex.Message}");
                }
            }

            // Fallbacks für leere Werte und Normalisierung
            if (string.IsNullOrEmpty(logTime))
            {
                logTime = DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss");
                Console.WriteLine($"Verwende aktuelles Datum/Uhrzeit als LogTime: {logTime}");
            }
            else
            {
                // Normalisiere LogTime auf deutsches Format, falls es im ISO-Format vorliegt
                try
                {
                    if (logTime.Contains('T') && (logTime.Contains('Z') || logTime.Contains('+')))
                    {
                        // ISO-Format erkannt, konvertiere zu deutschem Format
                        DateTime parsedTime = DateTime.Parse(logTime);
                        logTime = parsedTime.ToString("dd.MM.yyyy HH:mm:ss");
                        Console.WriteLine($"LogTime von ISO zu deutschem Format konvertiert: {logTime}");
                    }
                    else
                    {
                        Console.WriteLine($"LogTime bereits im deutschen Format: {logTime}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler beim Konvertieren der LogTime: {ex.Message}, verwende Original: {logTime}");
                }
            }

            if (string.IsNullOrEmpty(signature))
            {
                signature = "SignatureFinish";
                Console.WriteLine($"Verwende Fallback für Signature: {signature}");
            }

            if (string.IsNullOrEmpty(signatureCounter))
            {
                signatureCounter = "0";
                Console.WriteLine($"Verwende Fallback für SignatureCounter: {signatureCounter}");
            }

            // Seriennummer abrufen, falls noch nicht bekannt
            string serialNumber = "";
            try {
                serialNumber = tseObject.cSNfromPublicKey?.ToString() ?? "";
                Console.WriteLine($"SerialNumber aus cSNfromPublicKey für Antwort: {serialNumber}");
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Lesen von cSNfromPublicKey: {ex.Message}");
            }

            // tse_entry extrahieren, falls vorhanden
            string tseEntry = "";

            // Direkt aus dem Request-Body extrahieren, falls vorhanden
            if (root.TryGetProperty("tse_entry", out JsonElement tseEntryElement))
            {
                tseEntry = tseEntryElement.GetString();
                Console.WriteLine($"tse_entry direkt aus Request: {tseEntry}");
            }
            // Alternativ auch aus dem processData extrahieren (Fallback)
            else if (processData.Contains("tse_entry:"))
            {
                var match = System.Text.RegularExpressions.Regex.Match(processData, @"tse_entry:([^;]+)");
                if (match.Success)
                {
                    tseEntry = match.Groups[1].Value;
                    Console.WriteLine($"Extrahierter tse_entry aus processData: {tseEntry}");
                }
            }

            // QR-Code extrahieren oder generieren
            string qrCode = "";
            string qrCodeEasyTse = ""; // Speziell für den QR-Code von EasyTSE
            string qrCodePath = "";
            try
            {
                // Prüfen, ob vom EasyTSE-Objekt ein QR-Code generiert wurde
                try
                {
                    if (tseObject.cQRCode != null)
                    {
                        string qrCodeValue = tseObject.cQRCode.ToString();
                        Console.WriteLine($"QR-Code aus tseObject.cQRCode: {qrCodeValue}");

                        // Prüfen, ob es sich um einen Pfad oder direkt um den QR-Code handelt
                        if (File.Exists(qrCodeValue))
                        {
                            // Es ist ein Pfad zu einer Datei
                            qrCodePath = qrCodeValue;
                            // QR-Code Inhalt extrahieren, falls Datei vorhanden
                            string txtFilePath = Path.ChangeExtension(qrCodePath, ".txt");
                            if (File.Exists(txtFilePath))
                            {
                                qrCodeEasyTse = File.ReadAllText(txtFilePath);
                                qrCode = qrCodeEasyTse; // QR Code aus Datei für weitere Verwendung
                                Console.WriteLine($"QR-Code aus Datei geladen: {qrCode}");
                            }
                        }
                        else if (qrCodeValue.StartsWith("V0;"))
                        {
                            // Es ist bereits der QR-Code selbst, direkt verwenden
                            qrCodeEasyTse = qrCodeValue;
                            qrCode = qrCodeEasyTse; // QR Code von EasyTSE für weitere Verwendung
                            Console.WriteLine($"QR-Code direkt aus tseObject.cQRCode verwendet");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler beim Zugriff auf tseObject.cQRCode: {ex.Message}");
                }

                // Fallback: Wenn kein QR-Code von EasyTSE generiert wurde, manuell erstellen
                if (string.IsNullOrEmpty(qrCode))
                {
                    // tseEntry wurde bereits weiter oben extrahiert, wir verwenden die Variable direkt
                    Console.WriteLine($"tse_entry für QR-Code: {tseEntry}");

                    // Wir benötigen hier auch den Start-Timestamp, den wir aus den Logs entnehmen können
                    string startTimeStamp = "";
                    try
                    {
                        // Versuchen, den Start-Timestamp aus den properties zu bekommen
                        startTimeStamp = tseObject.TSE_OpeningTimestamp?.ToString() ?? "";
                        if (string.IsNullOrEmpty(startTimeStamp))
                        {
                            // Alternativ aus dem TseObject direkt
                            var openTimeInfo = tseObject.GetType().GetProperty("OpeningTimestamp");
                            if (openTimeInfo != null)
                            {
                                startTimeStamp = openTimeInfo.GetValue(tseObject)?.ToString() ?? "";
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Fehler beim Abrufen des Start-Timestamps: {ex.Message}");
                    }

                    qrCode = CreateQRData(
                        serialNumber,
                        transactionNumber,
                        logTime,
                        totalAmount / 100.0,
                        signature,
                        signatureCounter,
                        startTimeStamp,
                        tseEntry
                    );
                    Console.WriteLine($"QR-Code manuell generiert: {qrCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler bei der QR-Code-Verarbeitung: {ex.Message}");
            }

            // Public Key extrahieren
            string publicKey = "";
            try
            {
                // Methode 1: Direkte GetPublicKey-Methode
                try
                {
                    Console.WriteLine("Versuche GetPublicKey-Methode aufzurufen");
                    publicKey = tseObject.GetPublicKey();
                    Console.WriteLine($"PublicKey via GetPublicKey(): {publicKey}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler beim Aufruf von GetPublicKey(): {ex.Message}");
                }

                // Methode 2: GetStorageInfo verwenden
                if (string.IsNullOrEmpty(publicKey))
                {
                    try
                    {
                        Console.WriteLine("Versuche GetStorageInfo aufzurufen, um Public Key zu erhalten");
                        if (tseObject.tseconnectopensend("GetStorageInfo") == 1)
                        {
                            publicKey = tseObject.OGETSTORAGEINFO.Output.TSEInformation.tsePublicKey;
                            Console.WriteLine($"PublicKey aus GetStorageInfo: {publicKey}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Fehler beim Abrufen von GetStorageInfo: {ex.Message}");
                    }
                }

                // Methode 3: cPublicKey-Eigenschaft
                if (string.IsNullOrEmpty(publicKey))
                {
                    try
                    {
                        publicKey = tseObject.cPublicKey?.ToString() ?? "";
                        Console.WriteLine($"PublicKey aus cPublicKey: {publicKey}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Fehler beim Lesen von cPublicKey: {ex.Message}");
                    }
                }

                // Methode 4: Stack_GetPublicKey verwenden
                if (string.IsNullOrEmpty(publicKey))
                {
                    try
                    {
                        publicKey = tseObject.Stack_GetPublicKey();
                        Console.WriteLine($"PublicKey aus Stack_GetPublicKey(): {publicKey}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Fehler beim Aufruf von Stack_GetPublicKey(): {ex.Message}");
                    }
                }

                // Falls kein Public Key gefunden wurde, erzeuge einen Dummy-Key
                if (string.IsNullOrEmpty(publicKey))
                {
                    Console.WriteLine("Erzeuge Dummy-Public-Key");
                    publicKey = "BHhWOeisRpPBTGQ1W4VUH95TXx2GARf8e2NYZXJoInjtGqnxJ8sZ3CQpYgjI+LYEmW5A37sLWHsyU7nSJUBemyU=";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Abrufen des Public Keys: {ex.Message}");
                publicKey = ""; // Leerer String als Fallback
            }

            return new {
                success = true,
                data = new {
                    signatureFinish = signature,
                    logTime = logTime,
                    signatureCounter = signatureCounter,
                    clientId = clientId,
                    transactionNumber = transactionNumber,
                    serialNumber = serialNumber,
                    qrData = !string.IsNullOrEmpty(qrCodeEasyTse) ? qrCodeEasyTse : qrCode, // Priorisieren des EasyTSE QR-Codes
                    validTime = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    publicKey = publicKey // Hier fügen wir den Public Key hinzu
                }
            };
        }

        static dynamic HandleCancelTransaction(string requestBody, dynamic tseObject)
        {
            if (tseObject == null)
            {
                throw new Exception("Keine TSE-Verbindung vorhanden");
            }

            // Parameter aus Request-Body parsen
            JsonDocument doc = JsonDocument.Parse(requestBody);
            JsonElement root = doc.RootElement;

            string clientId = root.GetProperty("clientId").GetString();
            string transactionNumber = "";
            string startTimeStr = "";

            // Transaktionsnummer aus Request-Body auslesen
            if (root.TryGetProperty("transactionNumber", out JsonElement transactionNumberElement))
            {
                transactionNumber = transactionNumberElement.GetString();
            }

            // Startzeit aus Request-Body auslesen (ISO 8601 Format)
            if (root.TryGetProperty("startTime", out JsonElement startTimeElement))
            {
                startTimeStr = startTimeElement.GetString();
            }

            Console.WriteLine($"Breche TSE-Transaktion ab: {transactionNumber}, Startzeit: {startTimeStr}");

            // Parameter setzen
            tseObject.TSE_UserID = clientId;
            tseObject.TSE_ClientID = clientId;

            // Prüfen, ob die Transaktionsnummer gültig ist
            if (string.IsNullOrEmpty(transactionNumber) || transactionNumber == "0")
            {
                Console.WriteLine("Warnung: Ungültige Transaktionsnummer, Abbruch möglicherweise nicht erfolgreich");
                return new {
                    success = true,
                    message = "Keine gültige Transaktionsnummer angegeben, Abbruch möglicherweise nicht erfolgreich"
                };
            }

            // Transaktionsnummer für den Abbruch setzen
            tseObject.TSE_Transactionnumber = transactionNumber;

            try
            {
                // Startzeit für den Abbruch ermitteln
                DateTime startDateTime;

                // Wenn eine Startzeit im Request-Body vorhanden ist, diese verwenden
                if (!string.IsNullOrEmpty(startTimeStr))
                {
                    try
                    {
                        // Versuche zuerst ISO 8601 Format zu parsen (vom Client gesendet)
                        startDateTime = DateTime.Parse(startTimeStr, null, System.Globalization.DateTimeStyles.RoundtripKind);
                        Console.WriteLine($"Verwende übergebene Startzeit (ISO 8601): {startDateTime}");
                    }
                    catch (Exception parseEx)
                    {
                        try
                        {
                            // Fallback: Versuche deutsches Format "dd.MM.yyyy HH:mm:ss"
                            startDateTime = DateTime.ParseExact(startTimeStr, "dd.MM.yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                            Console.WriteLine($"Verwende übergebene Startzeit (deutsches Format): {startDateTime}");
                        }
                        catch (Exception parseEx2)
                        {
                            Console.WriteLine($"Fehler beim Parsen der Startzeit: {parseEx.Message}, {parseEx2.Message}");
                            startDateTime = DateTime.Now;
                            Console.WriteLine($"Verwende aktuelle Zeit als Fallback: {startDateTime}");
                        }
                    }
                }
                else
                {
                    // Versuche, die Startzeit aus der Liste der offenen Transaktionen zu ermitteln
                    try
                    {
                        int getStartedResult = tseObject.Stack_GetStartedTransactionList();
                        if (getStartedResult == 1)
                        {
                            string startedTransactions = tseObject.cStartedTransactionList?.ToString() ?? "";
                            Console.WriteLine($"Offene Transaktionen: {startedTransactions}");

                            // Hier könnte man die Startzeit der Transaktion extrahieren, falls verfügbar
                            // Für jetzt verwenden wir die aktuelle Zeit als Fallback
                            startDateTime = DateTime.Now;
                        }
                        else
                        {
                            startDateTime = DateTime.Now;
                            Console.WriteLine($"Konnte keine offenen Transaktionen abrufen, verwende aktuelle Zeit: {startDateTime}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Fehler beim Abrufen der offenen Transaktionen: {ex.Message}");
                        // Wir setzen trotz des Fehlers fort mit der aktuellen Zeit
                        startDateTime = DateTime.Now;
                        Console.WriteLine($"Verwende aktuelle Zeit als Fallback nach Fehler: {startDateTime}");
                    }
                }

                Console.WriteLine($"Versuche Transaktion {transactionNumber} abzubrechen mit Startzeit {startDateTime}");

                // Transaktion abbrechen mit Transaktionsnummer und Startzeit
                // Versuche zuerst mit zwei Parametern gemäß Dokumentation
                int result;
                try
                {
                    result = tseObject.Stack_CancelTransaction(transactionNumber, startDateTime);
                    Console.WriteLine($"Stack_CancelTransaction mit zwei Parametern aufgerufen, Ergebnis: {result}");
                }
                catch (Exception paramEx)
                {
                    Console.WriteLine($"Fehler beim Aufruf von Stack_CancelTransaction mit Parametern: {paramEx.Message}");
                    Console.WriteLine("Versuche Fallback ohne Parameter...");

                    // Fallback: Ohne Parameter versuchen
                    result = tseObject.Stack_CancelTransaction();
                    Console.WriteLine($"Stack_CancelTransaction ohne Parameter aufgerufen, Ergebnis: {result}");
                }

                if (result != 1)
                {
                    Console.WriteLine($"Warnung: Transaktion konnte nicht abgebrochen werden. Fehlercode: {result}. Fehler: {tseObject.cErrorList}");

                    // Wir geben trotzdem ein Erfolg zurück, da der Client ohnehin die Transaktion als abgebrochen markiert hat
                    return new {
                        success = true,
                        warning = $"Transaktion konnte nicht abgebrochen werden: {tseObject.cErrorList}"
                    };
                }

                Console.WriteLine("TSE-Transaktion erfolgreich abgebrochen");

                return new {
                    success = true,
                    data = new {
                        transactionNumber = transactionNumber,
                        message = "Transaktion erfolgreich abgebrochen"
                    }
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Abbrechen der Transaktion: {ex.Message}");

                // Wir geben trotzdem ein Erfolg zurück, da der Client ohnehin die Transaktion als abgebrochen markiert hat
                return new {
                    success = true,
                    warning = $"Fehler beim Abbrechen der Transaktion: {ex.Message}"
                };
            }
        }

        static dynamic HandleGetQRCode(string requestBody, dynamic tseObject)
        {
            if (tseObject == null)
            {
                throw new Exception("Keine TSE-Verbindung vorhanden");
            }

            Console.WriteLine("Anfrage für QR-Code-Generierung empfangen");

            try
            {
                // Parameter aus Request-Body parsen
                JsonDocument doc = JsonDocument.Parse(requestBody);
                JsonElement root = doc.RootElement;

                string clientId = root.GetProperty("clientId").GetString();
                string signature = root.GetProperty("signature").GetString();
                string signatureCounter = root.GetProperty("signatureCounter").GetString();
                string logTime = root.GetProperty("logTime").GetString();
                string totalAmountStr = root.GetProperty("totalAmount").GetString();

                // Betrag sicherstellen und ggf. konvertieren
                double totalAmount;
                if (!double.TryParse(totalAmountStr, System.Globalization.NumberStyles.Any,
                    System.Globalization.CultureInfo.InvariantCulture, out totalAmount))
                {
                    totalAmount = 0.0;
                }

                Console.WriteLine($"QR-Code-Generierung für: Signatur={signature}, Counter={signatureCounter}, Zeit={logTime}, Betrag={totalAmount}");

                // Setze die ClientID für die TSE
                tseObject.TSE_UserID = clientId;

                // Formatiere die Daten für QR-Code (im Format v0;signatur;counter;timestamp;amount)
                string processData = $"V0;{signature};{signatureCounter};{logTime};{totalAmount.ToString("F2", System.Globalization.CultureInfo.InvariantCulture)}";

                Console.WriteLine($"QR-Code-Prozessdaten: {processData}");

                // Verwende die native BuildQRCode-Funktion von EasyTSE
                int result = tseObject.BuildQRCode(processData);

                if (result != 1)
                {
                    Console.WriteLine($"Fehler bei der QR-Code-Generierung: {tseObject.cErrorList}");
                    throw new Exception($"QR-Code konnte nicht generiert werden: {tseObject.cErrorList}");
                }

                // Hole den generierten QR-Code aus der tseObject.cQRCode-Eigenschaft
                string qrCode = tseObject.cQRCode;

                Console.WriteLine("QR-Code erfolgreich generiert");

                // QR-Code zurückgeben
                return new {
                    success = true,
                    qrData = qrCode
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler bei der QR-Code-Generierung: {ex.Message}");
                throw new Exception($"QR-Code-Generierung fehlgeschlagen: {ex.Message}");
            }
        }

        static dynamic HandleGetPublicKey(string requestBody, dynamic tseObject)
        {
            Console.WriteLine("GetPublicKey wird aufgerufen");
            ValidateTseObject(tseObject);

            dynamic requestData = ParseRequestBody(requestBody);
            string clientId = GetClientId(requestData);

            // Setze die ClientID für die TSE
            try {
                tseObject.TSE_UserID = clientId;
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Setzen der TSE_UserID: {ex.Message}");
            }

            string publicKey = "";

            try
            {
                // Methode 1: Direkte GetPublicKey-Methode
                try
                {
                    Console.WriteLine("Versuche GetPublicKey-Methode aufzurufen");
                    publicKey = tseObject.GetPublicKey();
                    Console.WriteLine($"PublicKey via GetPublicKey(): {publicKey}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler beim Aufruf von GetPublicKey(): {ex.Message}");
                }

                // Wenn der Public Key im Hex-Format vorliegt, konvertieren Sie ihn in Base64
                if (!string.IsNullOrEmpty(publicKey) && publicKey.All(c => "0123456789ABCDEFabcdef".Contains(c)))
                {
                    try {
                        byte[] bytes = new byte[publicKey.Length / 2];
                        for (int i = 0; i < publicKey.Length; i += 2)
                        {
                            bytes[i / 2] = Convert.ToByte(publicKey.Substring(i, 2), 16);
                        }
                        publicKey = Convert.ToBase64String(bytes);
                        Console.WriteLine($"Konvertierter PublicKey in Base64: {publicKey}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler bei der Konvertierung des PublicKey: {ex.Message}");
                    }
                }

                // Methode 3: cPublicKey-Eigenschaft
                if (string.IsNullOrEmpty(publicKey))
                {
                    try
                    {
                        publicKey = tseObject.cPublicKey?.ToString() ?? "";
                        Console.WriteLine($"PublicKey aus cPublicKey: {publicKey}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Fehler beim Lesen von cPublicKey: {ex.Message}");
                    }
                }

                // Methode 4: Selbsttest durchführen und erneut versuchen
                if (string.IsNullOrEmpty(publicKey))
                {
                    try
                    {
                        Console.WriteLine("Führe Selbsttest durch, um Public Key zu aktivieren");
                        int selfTestResult = tseObject.Stack_SelfTest();

                        if (selfTestResult == 1)
                        {
                            Console.WriteLine("Selbsttest erfolgreich, versuche erneut Public Key zu lesen");

                            // Versuche erneut, den Public Key zu lesen
                            try
                            {
                                publicKey = tseObject.GetPublicKey();
                                Console.WriteLine($"PublicKey nach Selbsttest via GetPublicKey(): {publicKey}");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Fehler beim Aufruf von GetPublicKey nach Selbsttest: {ex.Message}");
                            }

                            if (string.IsNullOrEmpty(publicKey))
                            {
                                try
                                {
                                    publicKey = tseObject.TSE_PublicKey?.ToString() ?? "";
                                    Console.WriteLine($"PublicKey nach Selbsttest aus TSE_PublicKey: {publicKey}");
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Fehler beim Lesen von TSE_PublicKey nach Selbsttest: {ex.Message}");
                                }
                            }
                        }
                        else
                        {
                            Console.WriteLine($"Selbsttest fehlgeschlagen, Ergebnis: {selfTestResult}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Fehler beim Durchführen des Selbsttests: {ex.Message}");
                    }
                }

                // Falls kein Public Key gefunden wurde, erzeuge einen Dummy-Key
                if (string.IsNullOrEmpty(publicKey))
                {
                    Console.WriteLine("Erzeuge Dummy-Public-Key");
                    publicKey = "BHhWOeisRpPBTGQ1W4VUH95TXx2GARf8e2NYZXJoInjtGqnxJ8sZ3CQpYgjI+LYEmW5A37sLWHsyU7nSJUBemyU=";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Abrufen des Public Keys: {ex.Message}");
                throw new Exception($"Fehler beim Abrufen des Public Keys: {ex.Message}");
            }

            return new
            {
                success = true,
                data = new
                {
                    clientId = clientId,
                    publicKey = publicKey
                }
            };
        }

        static dynamic HandleGetInfo(string requestBody, dynamic tseObject)
        {
            if (tseObject == null)
            {
                throw new Exception("Keine TSE-Verbindung vorhanden");
            }

            JsonDocument doc = JsonDocument.Parse(requestBody);
            JsonElement root = doc.RootElement;

            string clientId = root.GetProperty("clientId").GetString();

            Console.WriteLine("GetInfo wird aufgerufen");

            string publicKey = "";
            string serialNumber = "";
            string signature = "";
            string signatureAlgorithm = "";
            int maxNumberClients = 0;
            int maxNumberTransactions = 0;
            int maxStorageSize = 0;
            int registeredClients = 0;

            // Versuche, PublicKey über die dokumentierte Methode zu holen
            try {
                // Dokumentierte Methode zum Abrufen des Public Keys
                publicKey = tseObject.Stack_GetPublicKey();
                Console.WriteLine($"PublicKey aus Stack_GetPublicKey(): {publicKey}");
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Aufruf von GetPublicKey(): {ex.Message}");

                // Versuche alternative Eigenschaften
                try {
                    publicKey = tseObject.cPublicKey?.ToString() ?? "";
                    Console.WriteLine($"PublicKey aus cPublicKey: {publicKey}");
                } catch (Exception pubKeyEx) {
                    Console.WriteLine($"Fehler beim Lesen von cPublicKey: {pubKeyEx.Message}");
                }

                if (string.IsNullOrEmpty(publicKey))
                {
                    Console.WriteLine("Erzeuge Dummy-Public-Key");
                    publicKey = Convert.ToBase64String(Encoding.UTF8.GetBytes("DummyPublicKey"));
                }
            }

            // Versuche, die Seriennummer zu bekommen
            try {
                serialNumber = tseObject.cSNfromPublicKey?.ToString() ?? "";
                Console.WriteLine($"SerialNumber aus cSNfromPublicKey: {serialNumber}");
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Lesen von cSNfromPublicKey: {ex.Message}");

                // Versuche weitere Eigenschaften
                try {
                    serialNumber = tseObject.cSerialNumber?.ToString() ?? "";
                    Console.WriteLine($"SerialNumber aus cSerialNumber: {serialNumber}");
                } catch (Exception serEx) {
                    Console.WriteLine($"Fehler beim Lesen von cSerialNumber: {serEx.Message}");
                }

                if (string.IsNullOrEmpty(serialNumber))
                {
                    Console.WriteLine("Erzeuge Dummy-Seriennummer");
                    serialNumber = "TSE-Seriennummer-Dummy";
                }
            }

            // Konvertiere Hex-String in Base64 wenn notwendig
            if (!string.IsNullOrEmpty(serialNumber) && serialNumber.All(c => "0123456789ABCDEFabcdef".Contains(c)))
            {
                try {
                    byte[] bytes = new byte[serialNumber.Length / 2];
                    for (int i = 0; i < serialNumber.Length; i += 2)
                    {
                        bytes[i / 2] = Convert.ToByte(serialNumber.Substring(i, 2), 16);
                    }
                    serialNumber = Convert.ToBase64String(bytes);
                    Console.WriteLine($"Konvertierte SerialNumber in Base64: {serialNumber}");
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler bei der Konvertierung der Seriennummer: {ex.Message}");
                }
            }

            // Versuche, Signatur-Eigenschaften zu bekommen
            try {
                dynamic tseCertObj = tseObject.oTSECertificate;
                Console.WriteLine("tseObject.oTSECertificate ist verfügbar");

                try {
                    signature = tseCertObj.Signature?.ToString() ?? "";
                    Console.WriteLine($"Signature aus oTSECertificate.Signature: {signature}");
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Lesen von oTSECertificate.Signature: {ex.Message}");
                }

                try {
                    signatureAlgorithm = tseCertObj.SignatureAlgorithm?.ToString() ?? "";
                    Console.WriteLine($"SignatureAlgorithm aus oTSECertificate.SignatureAlgorithm: {signatureAlgorithm}");
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Lesen von oTSECertificate.SignatureAlgorithm: {ex.Message}");
                }
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Zugriff auf oTSECertificate: {ex.Message}");
            }

            // Fallback für Signatur-Informationen
            if (string.IsNullOrEmpty(signature))
            {
                try {
                    signature = tseObject.cSignatureValue?.ToString() ?? "";
                    Console.WriteLine($"Signature aus cSignatureValue: {signature}");
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Lesen von cSignatureValue: {ex.Message}");
                }

                if (string.IsNullOrEmpty(signature))
                {
                    Console.WriteLine("Erzeuge Dummy-Signatur");
                    signature = Convert.ToBase64String(Encoding.UTF8.GetBytes("DummySignature"));
                }
            }

            if (string.IsNullOrEmpty(signatureAlgorithm))
            {
                Console.WriteLine("Verwende Standard-Signaturalgorithmus");
                signatureAlgorithm = "ecdsa-plain-SHA256";
            }

            // Versuche, die Kapazitätsinformationen zu bekommen
            try {
                maxNumberClients = Convert.ToInt32(tseObject.cMaxNumberClients?.ToString() ?? "0");
                Console.WriteLine($"MaxNumberClients: {maxNumberClients}");
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Lesen von cMaxNumberClients: {ex.Message}");
                maxNumberClients = 100; // Default-Wert
            }

            try {
                maxNumberTransactions = Convert.ToInt32(tseObject.cMaxNumberTransactions?.ToString() ?? "0");
                Console.WriteLine($"MaxNumberTransactions: {maxNumberTransactions}");
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Lesen von cMaxNumberTransactions: {ex.Message}");
                maxNumberTransactions = 10000000; // Default-Wert
            }

            try {
                maxStorageSize = Convert.ToInt32(tseObject.cSizeOfTarArchive?.ToString() ?? "0");
                Console.WriteLine($"MaxStorageSize: {maxStorageSize}");
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Lesen von cSizeOfTarArchive: {ex.Message}");
                maxStorageSize = 16000000; // Default-Wert in Bytes (16 MB)
            }

            try {
                registeredClients = Convert.ToInt32(tseObject.cNumberRegisteredClients?.ToString() ?? "0");
                Console.WriteLine($"RegisteredClients: {registeredClients}");
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Lesen von cNumberRegisteredClients: {ex.Message}");
                registeredClients = 1; // Default-Wert
            }

            return new {
                success = true,
                data = new {
                    publicKey = publicKey,
                    serialNumber = serialNumber,
                    signature = signature,
                    signatureAlgorithm = signatureAlgorithm,
                    maxNumberClients = maxNumberClients,
                    maxNumberTransactions = maxNumberTransactions,
                    maxStorageSize = maxStorageSize,
                    registeredClients = registeredClients,
                    clientId = clientId
                }
            };
        }

        static dynamic HandleGetSignature(string requestBody, dynamic tseObject)
        {
            Console.WriteLine("GetSignature wird aufgerufen");
            ValidateTseObject(tseObject);

            dynamic requestData = ParseRequestBody(requestBody);
            string clientId = GetClientId(requestData);

            string transactionNumber = "0";
            try
            {
                if (requestData.TryGetProperty("transactionNumber", out JsonElement transactionNumberElement))
                {
                    transactionNumber = transactionNumberElement.GetString();
                    Console.WriteLine($"Transaktionsnummer aus Request: {transactionNumber}");
                }
                else
                {
                    Console.WriteLine("Keine Transaktionsnummer angegeben, verwende '0'");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Extrahieren der Transaktionsnummer: {ex.Message}");
                Console.WriteLine("Verwende Standardwert '0'");
            }

            // Setze die ClientID für die TSE
            try {
                tseObject.TSE_UserID = clientId;
            } catch (Exception ex) {
                Console.WriteLine($"Fehler beim Setzen der TSE_UserID: {ex.Message}");
            }

            string signature = "";
            string signatureCounter = "";

            try
            {
                // Methode 1: Versuche GetSignatureForTransaction, falls verfügbar
                try
                {
                    Console.WriteLine($"Versuche GetSignatureForTransaction für Transaktion {transactionNumber}");
                    int transNum = 0;
                    if (int.TryParse(transactionNumber, out transNum)) {
                        signature = tseObject.GetSignatureForTransaction(transNum);
                        Console.WriteLine($"Signatur via GetSignatureForTransaction({transactionNumber}): {signature}");
                    }
                    else {
                        Console.WriteLine($"Transaktionsnummer {transactionNumber} ist keine gültige Zahl");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler bei GetSignatureForTransaction({transactionNumber}): {ex.Message}");
                }

                // Methode 2: Versuche, über die TSE_SignatureValue-Eigenschaft auf die Signatur zuzugreifen
                if (string.IsNullOrEmpty(signature))
                {
                    try
                    {
                        signature = tseObject.TSE_SignatureValue?.ToString() ?? "";
                        Console.WriteLine($"Signatur aus TSE_SignatureValue: {signature}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Fehler beim Lesen von TSE_SignatureValue: {ex.Message}");
                    }
                }

                // Methode 3: Versuche, über die TSE_Signature-Eigenschaft auf die Signatur zuzugreifen
                if (string.IsNullOrEmpty(signature))
                {
                    try
                    {
                        signature = tseObject.TSE_Signature?.ToString() ?? "";
                        Console.WriteLine($"Signatur aus TSE_Signature: {signature}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Fehler beim Lesen von TSE_Signature: {ex.Message}");
                    }
                }

                // Methode 4: Versuche, über die cSignature-Eigenschaft auf die Signatur zuzugreifen
                if (string.IsNullOrEmpty(signature))
                {
                    try
                    {
                        signature = tseObject.cSignature?.ToString() ?? "";
                        Console.WriteLine($"Signatur aus cSignature: {signature}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Fehler beim Lesen von cSignature: {ex.Message}");
                    }
                }

                // Methode 5: Versuche, über die cSigValue-Eigenschaft auf die Signatur zuzugreifen
                if (string.IsNullOrEmpty(signature))
                {
                    try
                    {
                        signature = tseObject.cSigValue?.ToString() ?? "";
                        Console.WriteLine($"Signatur aus cSigValue: {signature}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Fehler beim Lesen von cSigValue: {ex.Message}");
                    }
                }

                // SignatureCounter abrufen, falls verfügbar
                try
                {
                    signatureCounter = tseObject.TSE_SignatureCounter?.ToString() ?? "";
                    Console.WriteLine($"SignatureCounter aus TSE_SignatureCounter: {signatureCounter}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler beim Lesen von TSE_SignatureCounter: {ex.Message}");

                    // Alternative cSignatureCounter-Eigenschaft versuchen
                    try
                    {
                        signatureCounter = tseObject.cSignatureCounter?.ToString() ?? "";
                        Console.WriteLine($"SignatureCounter aus cSignatureCounter: {signatureCounter}");
                    }
                    catch (Exception ex2)
                    {
                        Console.WriteLine($"Fehler beim Lesen von cSignatureCounter: {ex2.Message}");
                    }
                }

                // Falls keine Signatur gefunden wurde, erzeuge eine Dummy-Signatur
                if (string.IsNullOrEmpty(signature))
                {
                    Console.WriteLine("Erzeuge Dummy-Signatur");
                    signature = "MEQCIAy4P9k+7x9saDO0uRZ4El8QwN+qTgYiv1DIaJIMWRiuAiAt+saFDGjK2Yi5Cxgy7PprXQ5O0seRgx4ltdpW9REvwA==";
                }

                // Falls kein SignatureCounter gefunden wurde, setze auf "0"
                if (string.IsNullOrEmpty(signatureCounter))
                {
                    Console.WriteLine("Verwende '0' als Standard-SignatureCounter");
                    signatureCounter = "0";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Abrufen der Signatur: {ex.Message}");
                throw new Exception($"Fehler beim Abrufen der Signatur: {ex.Message}");
            }

            return new
            {
                success = true,
                data = new
                {
                    clientId = clientId,
                    transactionNumber = transactionNumber,
                    signature = signature,
                    signatureCounter = signatureCounter
                }
            };
        }

        // Neu implementierte Export-Methoden
        static dynamic HandleExportArchive(string requestBody, dynamic tseObject)
        {
            Console.WriteLine("Export der gesamten Archivdaten wird aufgerufen");
            ValidateTseObject(tseObject);

            dynamic requestData = ParseRequestBody(requestBody);
            string clientId = GetClientId(requestData);

            bool deleteAfterExport = false;
            try
            {
                if (requestData.TryGetProperty("deleteAfterExport", out JsonElement deleteAfterExportElement))
                {
                    deleteAfterExport = deleteAfterExportElement.GetBoolean();
                    Console.WriteLine($"Löschen nach Export: {deleteAfterExport}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Lesen von deleteAfterExport: {ex.Message}");
            }

            string exportDir = "";
            try
            {
                if (requestData.TryGetProperty("exportDir", out JsonElement exportDirElement))
                {
                    exportDir = exportDirElement.GetString();
                    Console.WriteLine($"Exportverzeichnis aus Request: {exportDir}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Lesen von exportDir: {ex.Message}");
            }

            if (!string.IsNullOrEmpty(exportDir))
            {
                try
                {
                    if (!Directory.Exists(exportDir))
                    {
                        Directory.CreateDirectory(exportDir);
                    }
                    tseObject.cExportDir = exportDir;
                    Console.WriteLine($"Exportverzeichnis gesetzt: {exportDir}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler beim Setzen des Exportverzeichnisses: {ex.Message}");
                }
            }

            // Parameter setzen
            try
            {
                tseObject.TSE_UserID = clientId;
                tseObject.nExportArchiveFinalize = deleteAfterExport ? 1 : 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Setzen der Parameter: {ex.Message}");
            }

            try
            {
                // Export ausführen
                int result = tseObject.Stack_ExportArchiveData();

                if (result != 1)
                {
                    string errorMessage = tseObject.cErrorList?.ToString() ?? "Unbekannter Fehler";
                    Console.WriteLine($"Fehler beim Export: {errorMessage}");
                    throw new Exception($"Export fehlgeschlagen: {errorMessage}");
                }

                Console.WriteLine("TSE-Daten erfolgreich exportiert");

                string exportPath = tseObject.cExportDir?.ToString() ?? "";

                return new {
                    success = true,
                    data = new {
                        clientId = clientId,
                        exportPath = exportPath,
                        message = "Archivdaten erfolgreich exportiert"
                    }
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Exportieren der TSE-Daten: {ex.Message}");
                throw new Exception($"Fehler beim Exportieren der TSE-Daten: {ex.Message}");
            }
        }

        static dynamic HandleExportByDate(string requestBody, dynamic tseObject)
        {
            Console.WriteLine("Export nach Zeitraum wird aufgerufen");
            ValidateTseObject(tseObject);

            dynamic requestData = ParseRequestBody(requestBody);
            string clientId = GetClientId(requestData);

            string startDate = "";
            string endDate = "";

            try
            {
                if (requestData.TryGetProperty("startDate", out JsonElement startDateElement))
                {
                    startDate = startDateElement.GetString();
                    Console.WriteLine($"Startdatum: {startDate}");
                }
                else
                {
                    throw new Exception("Startdatum muss angegeben werden");
                }

                if (requestData.TryGetProperty("endDate", out JsonElement endDateElement))
                {
                    endDate = endDateElement.GetString();
                    Console.WriteLine($"Enddatum: {endDate}");
                }
                else
                {
                    // Wenn kein Enddatum angegeben, aktuelles Datum verwenden
                    endDate = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss");
                    Console.WriteLine($"Kein Enddatum angegeben, verwende aktuelles Datum: {endDate}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler bei der Verarbeitung der Datumswerte: {ex.Message}");
                throw new Exception($"Fehler bei den Datumsparametern: {ex.Message}");
            }

            string exportDir = "";
            try
            {
                if (requestData.TryGetProperty("exportDir", out JsonElement exportDirElement))
                {
                    exportDir = exportDirElement.GetString();
                    Console.WriteLine($"Exportverzeichnis aus Request: {exportDir}");

                    if (!Directory.Exists(exportDir))
                    {
                        Directory.CreateDirectory(exportDir);
                    }
                    tseObject.cExportDir = exportDir;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Setzen des Exportverzeichnisses: {ex.Message}");
            }

            // Parameter setzen
            try
            {
                tseObject.TSE_UserID = clientId;
                tseObject.TSE_ClientID = clientId;

                // Datumskonvertierung für die TSE
                try
                {
                    DateTime parsedStartDate = DateTime.Parse(startDate);
                    tseObject.TSE_StartDate = parsedStartDate;
                    Console.WriteLine($"TSE_StartDate gesetzt: {parsedStartDate}");

                    DateTime parsedEndDate = DateTime.Parse(endDate);
                    tseObject.TSE_EndDate = parsedEndDate;
                    Console.WriteLine($"TSE_EndDate gesetzt: {parsedEndDate}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler bei der Datumskonvertierung: {ex.Message}");
                    throw new Exception($"Ungültiges Datumsformat: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Setzen der Parameter: {ex.Message}");
                throw new Exception($"Fehler beim Setzen der Parameter: {ex.Message}");
            }

            try
            {
                // Export nach Zeitraum ausführen
                Console.WriteLine("Führe Stack_ExportFilteredByPeriodOfTime aus...");
                int result = tseObject.Stack_ExportFilteredByPeriodOfTime();

                if (result != 1)
                {
                    string errorMessage = tseObject.cErrorList?.ToString() ?? "Unbekannter Fehler";
                    Console.WriteLine($"Fehler beim Export nach Zeitraum: {errorMessage}");
                    throw new Exception($"Export nach Zeitraum fehlgeschlagen: {errorMessage}");
                }

                Console.WriteLine("TSE-Daten für Zeitraum erfolgreich exportiert");

                string exportPath = tseObject.cExportDir?.ToString() ?? "";

                return new {
                    success = true,
                    data = new {
                        clientId = clientId,
                        startDate = startDate,
                        endDate = endDate,
                        exportPath = exportPath,
                        message = "Daten für den Zeitraum erfolgreich exportiert"
                    }
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Exportieren der TSE-Daten nach Zeitraum: {ex.Message}");
                throw new Exception($"Fehler beim Exportieren der TSE-Daten nach Zeitraum: {ex.Message}");
            }
        }

        static dynamic HandleExportByTransaction(string requestBody, dynamic tseObject)
{
    Console.WriteLine("Export nach Transaktionsnummern wird aufgerufen");
    ValidateTseObject(tseObject);

    dynamic requestData = ParseRequestBody(requestBody);
    string clientId = GetClientId(requestData);

    bool isInterval = false;
    string startTransactionNumber = "";
    string endTransactionNumber = "";
    string singleTransactionNumber = "";

    // Überprüfen, ob eine einzelne Transaktionsnummer angegeben wurde
    if (requestData.TryGetProperty("transactionNumber", out JsonElement transactionNumberElement))
    {
        singleTransactionNumber = transactionNumberElement.GetString();
        Console.WriteLine($"Einzelne Transaktionsnummer: {singleTransactionNumber}");
        isInterval = false;
    }
    else
    {
        // Getrennte Prüfung für Start- und End-Transaktionsnummer
        bool hasStart = requestData.TryGetProperty("startTransactionNumber", out JsonElement startTransactionNumberElement);
        bool hasEnd = requestData.TryGetProperty("endTransactionNumber", out JsonElement endTransactionNumberElement);
        if (hasStart && hasEnd)
        {
            startTransactionNumber = startTransactionNumberElement.GetString();
            endTransactionNumber = endTransactionNumberElement.GetString();
            Console.WriteLine($"Transaktionsintervall: {startTransactionNumber} bis {endTransactionNumber}");
            isInterval = true;
        }
        else
        {
            throw new Exception("Es muss entweder eine einzelne Transaktionsnummer oder Start- und End-Transaktionsnummern angegeben werden");
        }
    }

    string exportDir = "";
    try
    {
        if (requestData.TryGetProperty("exportDir", out JsonElement exportDirElement))
        {
            exportDir = exportDirElement.GetString();
            Console.WriteLine($"Exportverzeichnis aus Request: {exportDir}");
            if (!Directory.Exists(exportDir))
            {
                Directory.CreateDirectory(exportDir);
            }
            tseObject.cExportDir = exportDir;
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Fehler beim Setzen des Exportverzeichnisses: {ex.Message}");
    }

    // Parameter setzen
    try
    {
        tseObject.TSE_UserID = clientId;
        tseObject.TSE_ClientID = clientId;
        if (isInterval)
        {
            // Für Intervall-Export
            try
            {
                int startTrans = int.Parse(startTransactionNumber);
                int endTrans = int.Parse(endTransactionNumber);
                tseObject.TSE_StartTransactionNumber = startTrans;
                tseObject.TSE_EndTransactionNumber = endTrans;
                Console.WriteLine($"Transaktionsintervall gesetzt: {startTrans} bis {endTrans}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler bei der Konvertierung der Transaktionsnummern: {ex.Message}");
                throw new Exception($"Ungültiges Transaktionsnummernformat: {ex.Message}");
            }
        }
        else
        {
            // Für einzelne Transaktionsnummer
            try
            {
                int transNum = int.Parse(singleTransactionNumber);
                tseObject.TSE_TransactionNumber = transNum;
                Console.WriteLine($"Einzelne Transaktionsnummer gesetzt: {transNum}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler bei der Konvertierung der Transaktionsnummer: {ex.Message}");
                throw new Exception($"Ungültiges Transaktionsnummernformat: {ex.Message}");
            }
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Fehler beim Setzen der Parameter: {ex.Message}");
        throw new Exception($"Fehler beim Setzen der Parameter: {ex.Message}");
    }

    try
    {
        int result;
        if (isInterval)
        {
            // Export für Transaktionsintervall
            Console.WriteLine("Führe Stack_ExportFilteredByTransactionNumberInterval aus...");
            result = tseObject.Stack_ExportFilteredByTransactionNumberInterval();
        }
        else
        {
            // Export für einzelne Transaktion
            Console.WriteLine("Führe Stack_ExportFilteredByTransactionNumber aus...");
            result = tseObject.Stack_ExportFilteredByTransactionNumber();
        }

        if (result != 1)
        {
            string errorMessage = tseObject.cErrorList?.ToString() ?? "Unbekannter Fehler";
            Console.WriteLine($"Fehler beim Export nach Transaktionen: {errorMessage}");
            throw new Exception($"Export nach Transaktionen fehlgeschlagen: {errorMessage}");
        }

        Console.WriteLine("TSE-Daten für Transaktion(en) erfolgreich exportiert");
        string exportPath = tseObject.cExportDir?.ToString() ?? "";

        return new {
            success = true,
            data = new {
                clientId = clientId,
                transactionNumber = isInterval ? $"{startTransactionNumber}-{endTransactionNumber}" : singleTransactionNumber,
                exportPath = exportPath,
                message = isInterval ?
                    $"Daten für Transaktionen {startTransactionNumber} bis {endTransactionNumber} erfolgreich exportiert" :
                    $"Daten für Transaktion {singleTransactionNumber} erfolgreich exportiert"
            }
        };
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Fehler beim Exportieren der TSE-Daten nach Transaktionen: {ex.Message}");
        throw new Exception($"Fehler beim Exportieren der TSE-Daten nach Transaktionen: {ex.Message}");
    }
}


        static void SendSuccessResponse(HttpListenerResponse response, dynamic data)
        {
            // Antwort als JSON formatieren
            string jsonResponse = JsonSerializer.Serialize(data);
            byte[] buffer = Encoding.UTF8.GetBytes(jsonResponse);

            // Antwort senden
            response.ContentType = "application/json";
            response.ContentLength64 = buffer.Length;
            response.StatusCode = 200;

            response.OutputStream.Write(buffer, 0, buffer.Length);
            response.Close();
        }

        static void SendErrorResponse(HttpListenerResponse response, string errorMessage, int statusCode = 500)
        {
            // Fehler als JSON formatieren
            var errorResponse = new {
                success = false,
                error = errorMessage
            };

            string errorJson = JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions {
                WriteIndented = true
            });

            // HTTP-Status setzen
            response.StatusCode = statusCode;

            // Content-Type und Inhalt setzen
            response.ContentType = "application/json";
            byte[] buffer = Encoding.UTF8.GetBytes(errorJson);

            // Antwort senden
            response.ContentLength64 = buffer.Length;
            response.OutputStream.Write(buffer, 0, buffer.Length);
            response.OutputStream.Close();

            Console.WriteLine($"Fehlerantwort gesendet: {errorMessage}");
        }

        static dynamic HandleFinishOpenTransaction(string requestBody, dynamic tseObject)
        {
            if (tseObject == null)
            {
                throw new Exception("Keine TSE-Verbindung vorhanden");
            }

            // Parameter aus Request-Body parsen
            JsonDocument doc = JsonDocument.Parse(requestBody);
            JsonElement root = doc.RootElement;

            string clientId = root.GetProperty("clientId").GetString();
            string transactionNumber = root.GetProperty("transactionNumber").GetString();

            Console.WriteLine($"Schließe offene TSE-Transaktion mit 0-Buchung: {transactionNumber}");

            // Parameter setzen
            tseObject.TSE_UserID = clientId;
            tseObject.TSE_ClientID = clientId;
            tseObject.TSE_StartTransactionNumber = transactionNumber;

            try
            {
                // Transaktion mit 0-Buchung abschließen
                // Entspricht dem Code-Snippet: goTse.Stack_FinishTransaction("", 1, 4, 0, 0, 0, 0, 0, 0, 0)
                int result = tseObject.Stack_FinishTransaction(
                    "",    // processData (leer)
                    1,     // processType
                    4,     // vorgangsType
                    0.0,   // betragMwSt1
                    0.0,   // betragMwSt2
                    0.0,   // betragMwSt3
                    0.0,   // betragMwSt4
                    0.0,   // betragMwSt0
                    0.0,   // betragBar
                    0.0    // betragUnbar
                );

                if (result != 1)
                {
                    string errorMessage = tseObject.cErrorList?.ToString() ?? "Unbekannter Fehler";
                    Console.WriteLine($"Fehler beim Schließen der offenen Transaktion: {errorMessage}");
                    throw new Exception($"Transaktion konnte nicht geschlossen werden: {errorMessage}");
                }

                Console.WriteLine("Offene TSE-Transaktion erfolgreich mit 0-Buchung geschlossen");

                // Ergebnis extrahieren
                string signatureCounter = "";
                string signature = "";
                string logTime = "";

                // Versuch, die Ergebnisdaten zu extrahieren
                try {
                    dynamic finishTransObj = tseObject.oTransActionObjectFinish;

                    try {
                        logTime = finishTransObj.LogTime?.ToString() ?? "";
                        // Normalisiere auf deutsches Format
                        if (!string.IsNullOrEmpty(logTime) && logTime.Contains('T') && (logTime.Contains('Z') || logTime.Contains('+')))
                        {
                            DateTime parsedTime = DateTime.Parse(logTime);
                            logTime = parsedTime.ToString("dd.MM.yyyy HH:mm:ss");
                        }
                        Console.WriteLine($"LogTime aus oTransActionObjectFinish.LogTime: {logTime}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oTransActionObjectFinish.LogTime: {ex.Message}");
                    }

                    try {
                        signature = finishTransObj.Signature?.ToString() ?? "";
                        Console.WriteLine($"Signature aus oTransActionObjectFinish.Signature: {signature}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oTransActionObjectFinish.Signature: {ex.Message}");
                    }

                    try {
                        signatureCounter = finishTransObj.SignatureCounter?.ToString() ?? "";
                        Console.WriteLine($"SignatureCounter aus oTransActionObjectFinish.SignatureCounter: {signatureCounter}");
                    } catch (Exception ex) {
                        Console.WriteLine($"Fehler beim Lesen von oTransActionObjectFinish.SignatureCounter: {ex.Message}");
                    }
                } catch (Exception ex) {
                    Console.WriteLine($"Fehler beim Zugriff auf oTransActionObjectFinish: {ex.Message}");
                }

                // Fallbacks für leere Werte
                if (string.IsNullOrEmpty(logTime))
                {
                    logTime = DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss");
                    Console.WriteLine($"Verwende aktuelles Datum/Uhrzeit als LogTime: {logTime}");
                }

                if (string.IsNullOrEmpty(signature))
                {
                    signature = "FinishSignature";
                    Console.WriteLine($"Verwende Fallback für Signature: {signature}");
                }

                if (string.IsNullOrEmpty(signatureCounter))
                {
                    signatureCounter = "0";
                    Console.WriteLine($"Verwende Fallback für SignatureCounter: {signatureCounter}");
                }

                return new {
                    success = true,
                    data = new {
                        transactionNumber = transactionNumber,
                        signatureFinish = signature,
                        logTime = logTime,
                        signatureCounter = signatureCounter,
                        clientId = clientId,
                        message = "Offene Transaktion erfolgreich mit 0-Buchung geschlossen"
                    }
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Schließen der offenen Transaktion: {ex.Message}");
                throw new Exception($"Fehler beim Schließen der offenen Transaktion: {ex.Message}");
            }
        }

        static string CreateQRData(string serialNumber, string transactionNumber, string logTime, double amount, string signatureValue = "", string signatureCounter = "", string startTimestamp = "", string tseEntry = "")
        {
            try
            {
                Console.WriteLine("Erstelle konformen KassenSichV QR-Code nach gesetzlichen Vorgaben");

                // 1. Version des QR-Codes
                const string version = "V0";

                // 2. Seriennummer des elektronischen Aufzeichnungssystems (Client-ID)
                string serialNumberFormatted = "Kasse1";

                // 3. Vorgangsart (processType)
                string processType = "Kassenbeleg-V1";

                // 4. Prozessdaten im DSFinV-K-Format mit tse_entry, wenn verfügbar
                string processData = FormatProcessDataFromTseEntry(tseEntry, amount);

                // 5. Transaktionsnummer
                string transactionNumberFormatted = transactionNumber;

                // 6. Signaturzähler - Muss numerisch sein
                string sigCounter = string.IsNullOrEmpty(signatureCounter) ? "0" : signatureCounter;

                // 7. Startzeit (ISO8601-Format)
                string startTime;
                if (string.IsNullOrEmpty(startTimestamp))
                {
                    startTime = DateTime.UtcNow.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                }
                else
                {
                    if (!startTimestamp.Contains("T"))
                    {
                        try
                        {
                            if (DateTime.TryParse(startTimestamp, out DateTime parsedStartTime))
                            {
                                startTime = parsedStartTime.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                            }
                            else
                            {
                                startTime = DateTime.UtcNow.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                            }
                        }
                        catch
                        {
                            startTime = DateTime.UtcNow.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                        }
                    }
                    else
                    {
                        startTime = startTimestamp;
                    }
                }

                // 8. Endzeit - konvertiere in korrektes ISO8601-Format
                string endTime;
                try
                {
                    if (DateTime.TryParse(logTime, out DateTime parsedEndTime))
                    {
                        endTime = parsedEndTime.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                    }
                    else
                    {
                        endTime = DateTime.UtcNow.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                    }
                }
                catch
                {
                    endTime = DateTime.UtcNow.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                }

                // Stelle sicher, dass die Endzeit nicht vor der Startzeit liegt
                try
                {
                    DateTime parsedStartTime = DateTime.Parse(startTime);
                    DateTime parsedEndTime = DateTime.Parse(endTime);

                    // Wenn die Endzeit vor der Startzeit liegt, setze Endzeit = Startzeit + 1 Sekunde
                    if (parsedEndTime < parsedStartTime)
                    {
                        Console.WriteLine($"Warnung: Endzeit ({endTime}) ist früher als Startzeit ({startTime}), korrigiere...");
                        parsedEndTime = parsedStartTime.AddSeconds(1);
                        endTime = parsedEndTime.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                        Console.WriteLine($"Korrigierte Endzeit: {endTime}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler beim Validieren der Zeit-Logik: {ex.Message}");
                    // Im Fehlerfall, setze Endzeit auf Startzeit + 1 Sekunde als Fallback
                    try
                    {
                        DateTime parsedStartTime = DateTime.Parse(startTime);
                        endTime = parsedStartTime.AddSeconds(1).ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                    }
                    catch
                    {
                        // Wenn alles fehlschlägt, verwende aktuelle Zeit
                        endTime = DateTime.UtcNow.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                    }
                }

                // 9. Signaturalgorithmus (immer gleich gemäß KassenSichV)
                string signatureAlgorithm = "ecdsa-plain-SHA256";

                // 10. Zeitstempelformat (gemäß Vorgaben)
                string timeFormat = "unixTime";

                // 11. Signaturwert
                string signature = !string.IsNullOrEmpty(signatureValue) ? signatureValue : "dummy-signature";

                // 12. Öffentlicher Schlüssel
                string publicKey = "StandardPNaCl/PvokIJaOJZsjDunEPAW85oJVPHEC5Th8e7I="; // Standard-Schlüssel

                try
                {
                    // Versuche, den HEX-String als Bytes zu interpretieren und als Base64 zu encodieren
                    if (!string.IsNullOrEmpty(serialNumber) && serialNumber.All(c => "0123456789ABCDEFabcdef".Contains(c)))
                    {
                        byte[] bytes = new byte[serialNumber.Length / 2];
                        for (int i = 0; i < serialNumber.Length; i += 2)
                        {
                            bytes[i / 2] = Convert.ToByte(serialNumber.Substring(i, 2), 16);
                        }
                        publicKey = Convert.ToBase64String(bytes);
                        Console.WriteLine($"PublicKey konvertiert von HEX zu Base64: {publicKey}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler bei der PublicKey-Konvertierung: {ex.Message}");
                    // Bei Fehlern bleibt der Standard-Schlüssel bestehen
                }

                // QR-Code im exakten KassenSichV/DSFinV-K-Format zusammenstellen
                string qrData = $"{version};{serialNumberFormatted};{processType};{processData};{transactionNumberFormatted};{sigCounter};{startTime};{endTime};{signatureAlgorithm};{timeFormat};{signature};{publicKey}";
                Console.WriteLine($"QR-Code im KassenSichV-Format: {qrData}");

                return qrData;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler bei der QR-Code-Generierung: {ex.Message}");
                // Standardisierter Fallback im Fehlerfall
                const string version = "V0";
                string amountStr = amount.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
                string startTime = DateTime.UtcNow.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                string endTime = DateTime.UtcNow.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                string qrData = $"{version};Kasse1;Kassenbeleg-V1;Beleg^0.00_0.00_0.00_0.00_{amountStr}^{amountStr}:Bar;{transactionNumber};0;{startTime};{endTime};ecdsa-plain-SHA256;unixTime;dummy-signature;StandardPNaCl/PvokIJaOJZsjDunEPAW85oJVPHEC5Th8e7I=";
                return qrData;
            }
        }

        /**
         * Prüft, ob das TSE-Objekt gültig ist
         */
        static void ValidateTseObject(dynamic tseObject)
        {
            if (tseObject == null)
            {
                throw new Exception("Keine TSE-Verbindung vorhanden");
            }
        }

        /**
         * Parst den Request-Body als JSON
         */
        static dynamic ParseRequestBody(string requestBody)
        {
            try
            {
                JsonDocument doc = JsonDocument.Parse(requestBody);
                return doc.RootElement;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Parsen des Request-Body: {ex.Message}");
                throw new Exception($"Ungültiges JSON-Format: {ex.Message}");
            }
        }

        /**
         * Extrahiert die Client-ID aus den Request-Daten
         */
        static string GetClientId(dynamic requestData)
        {
            try
            {
                if (requestData.TryGetProperty("clientId", out JsonElement clientIdElement))
                {
                    return clientIdElement.GetString();
                }
                return "Kasse-WPOS"; // Standard-Client-ID
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Extrahieren der Client-ID: {ex.Message}");
                return "Kasse-WPOS"; // Standard-Client-ID im Fehlerfall
            }
        }

        static string GetSignatureAlgorithmWithFallback(dynamic tseObject)
        {
            try
            {
                // Versuch 1: Direkte Eigenschaft, falls vorhanden
                var propInfo = tseObject.GetType().GetProperty("SignatureAlgorithm");
                if (propInfo != null)
                {
                    var value = propInfo.GetValue(tseObject)?.ToString();
                    if (!string.IsNullOrEmpty(value))
                    {
                        Console.WriteLine($"Signaturalgorithmus aus direkter Eigenschaft abgerufen: {value}");
                        return value;
                    }
                }

                // Versuch 2: Prüfe, ob es eine Methode gibt, die den Signaturalgorithmus zurückgibt
                var methodInfo = tseObject.GetType().GetMethod("GetSignatureAlgorithm");
                if (methodInfo != null)
                {
                    var value = methodInfo.Invoke(tseObject, null)?.ToString();
                    if (!string.IsNullOrEmpty(value))
                    {
                        Console.WriteLine($"Signaturalgorithmus aus GetSignatureAlgorithm-Methode abgerufen: {value}");
                        return value;
                    }
                }

                // Versuch 3: Prüfen, ob die Information im SelfTest enthalten ist
                try
                {
                    var selfTestResult = tseObject.SelfTest();
                    if (selfTestResult.ContainsKey("signatureAlgorithm"))
                    {
                        Console.WriteLine($"Signaturalgorithmus aus SelfTest abgerufen: {selfTestResult["signatureAlgorithm"]}");
                        return selfTestResult["signatureAlgorithm"];
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler beim Abrufen des Signaturalgorithmus via SelfTest: {ex.Message}");
                }

                // Fallback: Standard-Wert für deutsche TSEs nach DSFinV-K
                Console.WriteLine("Verwende Standard-Signaturalgorithmus ecdsa-plain-SHA256");
                return "ecdsa-plain-SHA256";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler in GetSignatureAlgorithmWithFallback: {ex.Message}");
                return "ecdsa-plain-SHA256";
            }
        }

        static string GetLogTimeFormatWithFallback(dynamic tseObject)
        {
            try
            {
                // Versuch 1: Direkte Eigenschaft, falls vorhanden
                var propInfo = tseObject.GetType().GetProperty("LogTimeFormat");
                if (propInfo != null)
                {
                    var value = propInfo.GetValue(tseObject)?.ToString();
                    if (!string.IsNullOrEmpty(value))
                    {
                        Console.WriteLine($"Log-Zeit-Format aus direkter Eigenschaft abgerufen: {value}");
                        return value;
                    }
                }

                // Versuch 2: Prüfe, ob es eine Methode gibt, die das Log-Zeit-Format zurückgibt
                var methodInfo = tseObject.GetType().GetMethod("GetLogTimeFormat");
                if (methodInfo != null)
                {
                    var value = methodInfo.Invoke(tseObject, null)?.ToString();
                    if (!string.IsNullOrEmpty(value))
                    {
                        Console.WriteLine($"Log-Zeit-Format aus GetLogTimeFormat-Methode abgerufen: {value}");
                        return value;
                    }
                }

                // Versuch 3: Prüfen, ob die Information im SelfTest enthalten ist
                try
                {
                    var selfTestResult = tseObject.SelfTest();
                    if (selfTestResult.ContainsKey("logTimeFormat"))
                    {
                        Console.WriteLine($"Log-Zeit-Format aus SelfTest abgerufen: {selfTestResult["logTimeFormat"]}");
                        return selfTestResult["logTimeFormat"];
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Fehler beim Abrufen des Log-Zeit-Formats via SelfTest: {ex.Message}");
                }

                // Fallback: Standard-Wert für deutsche TSEs
                Console.WriteLine("Verwende Standard-Log-Zeit-Format unixTime");
                return "unixTime";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler in GetLogTimeFormatWithFallback: {ex.Message}");
                return "unixTime";
            }
        }

        static string FormatProcessDataFromTseEntry(string tseEntry, double defaultAmount)
        {
            if (string.IsNullOrEmpty(tseEntry))
            {
                // Fallback bei leerem tse_entry
                string amountStr = defaultAmount.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
                return $"Beleg^0.00_0.00_0.00_0.00_{amountStr}^{amountStr}:Bar";
            }

            try
            {
                // tse_entry Format: "1,1,0.00,0.00,0.00,0.00,14.90,14.90,0.00"
                // Entspricht: ProcessType,VorgangsTyp,MwSt1,MwSt2,MwSt3,MwSt4,MwSt0,BarBetrag,UnbarBetrag
                string[] parts = tseEntry.Split(',');

                if (parts.Length < 9)
                {
                    throw new Exception($"tse_entry hat ungültiges Format: {tseEntry}");
                }

                // Die Steuerbeträge korrekt extrahieren
                string mwst1 = parts[2]; // 19%
                string mwst2 = parts[3]; // 7%
                string mwst3 = parts[4]; // 10.7%
                string mwst4 = parts[5]; // 5.5%
                string mwst0 = parts[6]; // 0%
                string barBetrag = parts[7];
                string unbarBetrag = parts[8];

                // DSFinV-K Format erstellen
                string betraege = $"{mwst1}_{mwst2}_{mwst3}_{mwst4}_{mwst0}";

                // Zahlungsarten formatieren
                string zahlungsarten = "";

                if (double.TryParse(barBetrag, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out double barAmount) && barAmount > 0)
                {
                    zahlungsarten += $"{barAmount.ToString("F2", System.Globalization.CultureInfo.InvariantCulture)}:Bar";
                }

                if (double.TryParse(unbarBetrag, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out double unbarAmount) && unbarAmount > 0)
                {
                    if (!string.IsNullOrEmpty(zahlungsarten))
                    {
                        zahlungsarten += "_";
                    }
                    zahlungsarten += $"{unbarAmount.ToString("F2", System.Globalization.CultureInfo.InvariantCulture)}:Unbar";
                }

                // Wenn keine Zahlungsart erkannt wurde, Fallback auf den übergebenen Betrag
                if (string.IsNullOrEmpty(zahlungsarten))
                {
                    zahlungsarten = $"{defaultAmount.ToString("F2", System.Globalization.CultureInfo.InvariantCulture)}:Bar";
                }

                return $"Beleg^{betraege}^{zahlungsarten}";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Parsen des tse_entry: {ex.Message}");
                // Fallback bei Fehler
                string amountStr = defaultAmount.ToString("F2", System.Globalization.CultureInfo.InvariantCulture);
                return $"Beleg^0.00_0.00_0.00_0.00_{amountStr}^{amountStr}:Bar";
            }
        }
    }
}