/**
 * ZVT-Befehle und Befehlsgenerierung
 * Erweiterte Version mit Terminal-Initialisierung nach Registrierung
 * Löst das Verifone P400 Systemfehler-Problem bei der ersten Zahlung
 */
const loggerService = require("./logger-service");

class ZVTCommands {
  constructor(client, config) {
    this.client = client;
    this.config = config;
    this.log = loggerService.getModuleLogger("ZVTCommands");
  }

  /**
   * Erstellt ein ZVT-Kommando für Zahlung mit korrekter BCD-Betragsformatierung
   * @param {number} amount Betrag in Cent
   * @returns {Buffer} Kommando als Buffer
   */
  createPaymentCommand(amount) {
    // Basis-Befehl für ZVT-Zahlung
    const cmdPrefix = [0x06, 0x01, 0x07, 0x04, 0x00, 0x00, 0x00];
    
    // Umwandlung von Cent in Euro für die BCD-Kodierung
    const euros = Math.floor(amount / 100);
    const cents = amount % 100;
    
    // Die korrekten BCD-Bytes erzeugen
    let amountBCD = [];
    
    // Format: 4 Stellen Euro + 2 Stellen Cent
    // Beträge werden als 6-stelliger String formatiert
    let amountStr = euros.toString().padStart(4, '0') + cents.toString().padStart(2, '0');
    
    // BCD-Kodierung: Immer paarweise Ziffern zu Bytes zusammenfassen
    for (let i = 0; i < amountStr.length; i += 2) {
      const digit1 = parseInt(amountStr.substring(i, i + 1));
      const digit2 = parseInt(amountStr.substring(i + 1, i + 2));
      
      // BCD-Kodierung: Erste Ziffer in oberen 4 Bits, zweite Ziffer in unteren 4 Bits
      const bcdByte = (digit1 << 4) | digit2;
      amountBCD.push(bcdByte);
    }
    
    // Kompletten Befehl zusammenstellen
    const fullCommand = [...cmdPrefix, ...amountBCD];
    
    this.log.info(`Erzeuge Authorisierungskommando für ${amount} Cent (${euros},${cents.toString().padStart(2, '0')} €): [${
      fullCommand.map((b) => b.toString(16).padStart(2, "0")).join("-")
    }]`);
    
    return Buffer.from(fullCommand);
  }

  /**
   * Neue Methode für direkten Zahlungsbefehl (entspricht dem C#-Format)
   * @param {number} amount - Betrag in Cent
   * @returns {Buffer} Zahlungsbefehl
   */
  createDirectPaymentCommand(amount) {
    // Basis-Befehl für ZVT-Zahlung im Format der C#-Anwendung
    const cmdPrefix = [0x06, 0x01, 0x07, 0x04, 0x00, 0x00, 0x00];
    
    // Umwandlung von Cent in BCD-Format (2 Bytes für den Betrag)
    const amountBCD = this._formatAmountBCD(amount);
    
    // Kompletten Befehl zusammenstellen
    const fullCommand = Buffer.from([...cmdPrefix, ...amountBCD]);
    
    this.log.info(`Erzeuge direkten Zahlungsbefehl für ${amount} Cent: [${
      fullCommand.toString('hex').match(/.{1,2}/g).join('-').toUpperCase()
    }]`);
    
    return fullCommand;
  }

  /**
   * Hilfsfunktion zur Formatierung des Betrags im BCD-Format
   * @param {number} amount - Betrag in Cent
   * @returns {Array} BCD-formatierte Bytes
   * @private
   */
  _formatAmountBCD(amount) {
    // Sicherstellen, dass der Betrag eine ganze Zahl ist
    const amountInt = Math.round(amount);
    
    // BCD-Kodierung: 2 Bytes für bis zu 9999 Cent
    const highByte = Math.floor(amountInt / 100);
    const lowByte = amountInt % 100;
    
    // BCD-Format: Jede Dezimalziffer wird als 4 Bits kodiert
    const highByteBCD = ((Math.floor(highByte / 10) << 4) | (highByte % 10)) & 0xFF;
    const lowByteBCD = ((Math.floor(lowByte / 10) << 4) | (lowByte % 10)) & 0xFF;
    
    return [highByteBCD, lowByteBCD];
  }

  /**
   * Erstellt ein ZVT-Kommando für den Abbruch einer laufenden Transaktion
   * @returns {Object} Kommando mit Array von Buffers
   */
  createAbortCommand() {
    // Standard ZVT-Abbruch (06 B0 00)
    const standardAbort = Buffer.from([0x06, 0xb0, 0x00]);
    
    this.log.info("Erstelle Abbruchbefehl im ZVT-Standardformat");
    
    // Behalte das ursprüngliche Format bei, aber mit nur einem Befehl
    return {
      commands: [standardAbort]
    };
  }

  /**
   * Erstellt ein ZVT-Kommando zur Registrierung
   * @returns {Buffer} Kommando als Buffer
   */
  createRegisterCommand() {
    // The registration data bytes with B6 configuration flag
    const registrationDataBytes_B6 = [
      0x00, 0x00, 0x00, 0xB6, 0x09, 0x78, 0x03, 0x01,
      0x06, 0x06, 0x26, 0x04, 0x0A, 0x02, 0x06, 0xD3
    ];
    const commandData = Buffer.from(registrationDataBytes_B6);
    
    // Create buffer for complete command
    // Format: STX(06) + LEN(2 bytes) + payload(16 bytes)
    const buffer = Buffer.alloc(3 + commandData.length);
    
    // Set STX
    buffer[0] = 0x06;
    
    // Set length in correct byte order (big-endian)
    buffer.writeUInt16BE(commandData.length, 1);
    
    // Copy payload data
    commandData.copy(buffer, 3);
    
    this.log.info("Sending registration command with B6 flags in correct format");
    return buffer;
  }

  /**
   * Erstellt ein ZVT-Kommando für den Tagesabschluss
   * @returns {Buffer} Kommando als Buffer
   */
  createEndOfDayCommand() {
    const cmdData = Buffer.from([0x00, 0x00, 0x00]); // Ohne Passwort
    const cmd = Buffer.from([0x06, 0x50, cmdData.length]);
    return Buffer.concat([cmd, cmdData]);
  }

  /**
   * Erstellt ein ZVT-Kommando mit korrekter Länge und LRC-Prüfsumme
   * @param {number} command Der ZVT-Befehlscode
   * @param {Buffer} data Die Daten (ohne Header und LRC)
   * @returns {Buffer} Das vollständige ZVT-Kommando
   * @private
   */
  _createZVTCommand(command, data) {
    const bufferLength = (data ? data.length : 0) + (command ? 1 : 0);
    const buffer = Buffer.alloc(3 + bufferLength);
    
    // STX byte
    buffer[0] = 0x06;
    
    // Length as 2 bytes in BIG-ENDIAN order (not little-endian)
    buffer.writeUInt16BE(bufferLength, 1);
    
    let offset = 3;
    if (command !== null) {
      buffer[offset++] = command;
    }
    
    if (data && data.length > 0) {
      data.copy(buffer, offset);
    }
    
    return buffer;
  }

  /**
   * Registrierung am Terminal durchführen mit anschließender Initialisierung
   * ERWEITERTE VERSION: Löst das Verifone P400 Systemfehler-Problem
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async registerTerminal() {
    try {
      this.log.info("Registriere am Terminal");
      const command = this.createRegisterCommand();
      const response = await this.client.connection._sendCommand(command, 10000);
      
      if (response.success) {
        this.log.info("Terminal-Registrierung erfolgreich");
        
        // ERWEITERT: Terminal-Initialisierung nach erfolgreicher Registrierung
        const initializationSuccess = await this.performPostRegistrationInitialization();
        
        if (initializationSuccess) {
          this.log.info("Terminal-Initialisierung nach Registrierung erfolgreich abgeschlossen");
          return true;
        } else {
          this.log.warn("Terminal-Registrierung erfolgreich, aber Initialisierung fehlgeschlagen");
          // Trotzdem als erfolgreich werten, da die Grundregistrierung funktioniert hat
          return true;
        }
      } else {
        this.log.error("Terminal-Registrierung fehlgeschlagen, Status:", response.statusCode);
        this.client.lastError = `Registrierung fehlgeschlagen: ${response.statusCode}`;
        return false;
      }
    } catch (error) {
      this.log.error("Fehler bei Terminal-Registrierung:", error.message);
      this.client.lastError = error.message;
      return false;
    }
  }

  /**
   * NEUE METHODE: Führt eine erweiterte Initialisierung nach der Registrierung durch
   * Diese Sequenz "wärmt" das Terminal auf und verhindert Systemfehler bei der ersten echten Zahlung
   * Speziell entwickelt für Verifone P400 Terminals
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   * @private
   */
  async performPostRegistrationInitialization() {
    try {
      this.log.info("Starte erweiterte Terminal-Initialisierung nach Registrierung (Verifone P400 spezifisch)");
      
      // Schritt 1: Terminal-Stabilisierung und Behandlung anhängiger Nachrichten
      await this._waitForTerminalStabilization();
      
      // Schritt 2: Status-Abfrage zum "Aufwecken" des Terminals
      const statusCheckSuccess = await this._performStatusCheck();
      if (!statusCheckSuccess) {
        this.log.info("Status-Abfrage ohne Antwort - Terminal arbeitet möglicherweise im Hintergrund (normal)");
      }
      
      // Schritt 3: Finale Stabilisierungsphase
      await this._finalStabilizationWait();
      
      this.log.info("Terminal-Initialisierung erfolgreich abgeschlossen, Terminal ist bereit für reguläre Zahlungen");
      return true;
      
    } catch (error) {
      this.log.error("Fehler bei erweiterter Terminal-Initialisierung:", error.message);
      return false;
    }
  }

  /**
   * NEUE METHODE: Wartet auf Terminal-Stabilisierung nach Registrierung
   * Behandelt unerwartete Terminal-Nachrichten und bestätigt diese
   * @returns {Promise<void>}
   * @private
   */
  async _waitForTerminalStabilization() {
    this.log.info("Warte auf Terminal-Stabilisierung nach Registrierung...");
    
    // Warte zunächst kurz auf mögliche Terminal-Nachrichten
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Prüfe, ob das Terminal Nachrichten gesendet hat und bestätige diese
    await this._handlePendingTerminalMessages();
    
    // Weitere Stabilisierung
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  /**
   * NEUE METHODE: Behandelt anhängige Terminal-Nachrichten nach Registrierung
   * @returns {Promise<void>}
   * @private
   */
  async _handlePendingTerminalMessages() {
    try {
      this.log.info("Prüfe auf anhängige Terminal-Nachrichten nach Registrierung");
      
      // Prüfe, ob noch Daten im Response-Buffer sind
      if (this.client.connection.responseData && this.client.connection.responseData.length > 0) {
        this.log.info(`Gefundene Terminal-Nachricht nach Registrierung: ${this.client.connection.responseData.toString('hex').toUpperCase()}`);
        
        // Sende Bestätigung für alle anhängigen Nachrichten
        if (this.client.connection.socket && this.client.connection.connected) {
          const ackCommand = Buffer.from([0x80, 0x00, 0x00]);
          this.client.connection.socket.write(ackCommand);
          this.log.info("Bestätigung für anhängige Terminal-Nachricht gesendet");
          
          // Kurze Pause für Verarbeitung
          await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        // Buffer zurücksetzen für sauberen Zustand
        this.client.connection.responseData = Buffer.alloc(0);
        this.client.connection.currentReceipt = null;
        this.client.connection.receiptComplete = false;
      }
    } catch (error) {
      this.log.warn("Fehler beim Behandeln anhängiger Terminal-Nachrichten:", error.message);
    }
  }

  /**
   * NEUE METHODE: Führt eine Status-Abfrage durch um das Terminal zu aktivieren
   * Verbesserte Version mit robustem Timeout-Handling
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   * @private
   */
  async _performStatusCheck() {
    try {
      this.log.info("Führe Status-Abfrage zur Terminal-Aktivierung durch");
      
      // Stelle sicher, dass das Terminal bereit ist
      if (!this.client.connection.socket || !this.client.connection.connected) {
        this.log.warn("Terminal nicht verbunden für Status-Abfrage");
        return false;
      }
      
      // Prüfe nochmals auf anhängige Nachrichten vor Status-Abfrage
      await this._handlePendingTerminalMessages();
      
      // Status-Abfrage-Kommando (ZVT Standard - Befehl 0x05)
      const statusCommand = this._createZVTCommand(0x05, Buffer.alloc(0));
      
      // Verkürzte Timeout-Zeit und robusteres Error-Handling
      try {
        const response = await this.client.connection._sendCommand(statusCommand, 3000);
        
        if (response) {
          this.log.info("Status-Abfrage erfolgreich - Terminal ist aktiv und antwortet");
          return true;
        } else {
          this.log.info("Status-Abfrage ohne Antwort - Terminal arbeitet möglicherweise im Hintergrund (normal)");
          return false;
        }
      } catch (timeoutError) {
        this.log.info("Status-Abfrage Timeout - Terminal ist möglicherweise beschäftigt (normal bei manchen Terminals)");
        return false;
      }
      
    } catch (error) {
      this.log.info("Status-Abfrage übersprungen:", error.message);
      return false;
    }
  }

  /**
   * NEUE METHODE: Finale Wartezeit vor Freigabe des Terminals
   * @returns {Promise<void>}
   * @private
   */
  async _finalStabilizationWait() {
    this.log.info("Finale Stabilisierungsphase vor Terminal-Freigabe...");
    await new Promise(resolve => setTimeout(resolve, 1500)); // 1,5 Sekunden
  }
}

module.exports = ZVTCommands;