@echo off
REM Batch-Skript zum Importieren eines .cer-Zertifikats in den Root-Zertifikatsspeicher.
REM Dieses Skript MUSS als Administrator ausgeführt werden!

REM Pfad zur Zertifikatdatei (anpassen, falls nötig)
set "CERTFILE=.\tp-wizidpos.cer"

REM Überprüfen, ob die Zertifikatdatei existiert
if not exist "%CERTFILE%" (
    echo Fehler: Zertifikat "%CERTFILE%" wurde nicht gefunden!
    exit /b 1
)

echo Importiere Zertifikat "%CERTFILE%" in den Vertrauenswürdige Stammzertifizierungsstellen-Speicher...
certutil -addstore "Root" "%CERTFILE%"
if %errorlevel%==0 (
    echo Zertifikat wurde erfolgreich importiert.
) else (
    echo Fehler beim Importieren des Zertifikats.
)
exit /b 0