// Script zum Schließen einer spezifischen TSE-Transaktion
// Basiert auf dem EasyTSE-Beispiel für das manuelle Schließen alter Transaktionen

const axios = require('axios');

// Konfiguration - hier können Sie die Werte anpassen
const CONFIG = {
    serviceUrl: 'http://localhost:8765',
    clientId: 'K001',
    transactionNumber: 922,  // HIER DIE GEWÜNSCHTE TRANSAKTIONSNUMMER EINTRAGEN
    userId: 'K001',
    
    // FinishTransaction Parameter (entspricht dem VFP-Beispiel)
    processData: '',         // Leer, wie im Beispiel
    processType: 1,          // ProcessType = 1
    vorgangsType: 4,         // VorgangsType = 4 (entspricht dem Beispiel)
    
    // Beträge (alle 0, wie im Beispiel)
    betragMwSt1: 0,         // 19% MwSt
    betragMwSt2: 0,         // 7% MwSt  
    betragMwSt3: 0,         // 10.7% MwSt
    betragMwSt4: 0,         // 5.5% MwSt
    betragMwSt0: 0,         // 0% MwSt
    betragBar: 0,           // Barzahlung
    betragUnbar: 0          // Unbare Zahlung
};

async function closeSpecificTransaction() {
    try {
        console.log("------------------------------------------------------");
        console.log("----> Gestartete Transaktion schließen");
        console.log("------------------------------------------------------");
        console.log(`TSE_TransactionNumber: ${CONFIG.transactionNumber}`);
        console.log(`TSE_UserID: ${CONFIG.userId}`);
        console.log(`TSE_ClientID: ${CONFIG.clientId}`);
        console.log("");

        // Payload für den finishTransaction-Aufruf erstellen
        // Exakte Parameter aus dem VFP-Beispiel: Stack_FinishTransaction("", 1, 4, 0, 0, 0, 0, 0, 0, 0)
        const payload = {
            clientId: CONFIG.clientId,
            transactionNumber: String(CONFIG.transactionNumber),
            processData: CONFIG.processData,        // "" (leer)
            processType: CONFIG.processType,        // 1
            vorgangsType: CONFIG.vorgangsType,      // 4
            // Die 7 Betragsparameter aus dem VFP-Beispiel als direkte Werte
            betragMwSt1: CONFIG.betragMwSt1,       // 0
            betragMwSt2: CONFIG.betragMwSt2,       // 0
            betragMwSt3: CONFIG.betragMwSt3,       // 0
            betragMwSt4: CONFIG.betragMwSt4,       // 0
            betragMwSt0: CONFIG.betragMwSt0,       // 0
            betragBar: CONFIG.betragBar,           // 0
            betragUnbar: CONFIG.betragUnbar        // 0
        };

        console.log("Sende finishTransaction-Anfrage mit folgenden Parametern:");
        console.log(`- TransactionNumber: ${payload.transactionNumber}`);
        console.log(`- ProcessType: ${payload.processType}`);
        console.log(`- VorgangsType: ${payload.vorgangsType}`);
        console.log(`- ProcessData: "${payload.processData}"`);
        console.log("");

        // HTTP-Anfrage an den EasyTSE-Service senden
        const response = await axios.post(`${CONFIG.serviceUrl}/finishTransaction`, payload, {
            timeout: 30000
        });

        // Antwort verarbeiten
        let responseData = response.data;
        if (typeof responseData === 'string') {
            try {
                responseData = JSON.parse(responseData);
            } catch (parseError) {
                console.error('Fehler beim Parsen der Antwort:', parseError.message);
                return;
            }
        }

        if (responseData && responseData.success) {
            console.log("------------------------------------------------------");
            console.log("----> FinishTransaction erfolgreich");
            console.log("------------------------------------------------------");
            
            const result = responseData.data || {};
            
            console.log(`LogTime           : ${result.logTime || 'nicht verfügbar'}`);
            console.log(`SignatureCounter  : ${result.signatureCounter || 'nicht verfügbar'}`);
            console.log(`Signature         : ${result.signatureFinish || 'nicht verfügbar'}`);
            console.log(`SerialNumber      : ${result.serialNumber || 'nicht verfügbar'}`);
            console.log(`PublicKey         : ${result.publicKey || 'nicht verfügbar'}`);
            
            if (result.qrData) {
                console.log(`QR-Code           : ${result.qrData}`);
            }
            
            console.log("------------------------------------------------------");
            console.log("Transaktion erfolgreich geschlossen!");
            
        } else {
            const errorMessage = responseData?.error || 'Unbekannter Fehler';
            console.error("------------------------------------------------------");
            console.error("----> FinishTransaction fehlgeschlagen");
            console.error("------------------------------------------------------");
            console.error(`Fehler: ${errorMessage}`);
            console.error("------------------------------------------------------");
        }
        
    } catch (error) {
        console.error("------------------------------------------------------");
        console.error("----> Fehler beim Schließen der Transaktion");
        console.error("------------------------------------------------------");
        console.error(`Fehler: ${error.message}`);
        
        if (error.response) {
            console.error(`HTTP Status: ${error.response.status}`);
            console.error(`HTTP Data: ${JSON.stringify(error.response.data)}`);
        }
        console.error("------------------------------------------------------");
    }
}

// Script ausführen, wenn direkt aufgerufen
if (require.main === module) {
    console.log("=".repeat(60));
    console.log("TSE Transaktion manuell schließen");
    console.log("=".repeat(60));
    console.log("");
    
    closeSpecificTransaction().then(() => {
        console.log("");
        console.log("Script beendet.");
        process.exit(0);
    }).catch((error) => {
        console.error("Unerwarteter Fehler:", error.message);
        process.exit(1);
    });
}

module.exports = { closeSpecificTransaction, CONFIG };