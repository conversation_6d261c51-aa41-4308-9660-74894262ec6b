// Client für die Integration mit der Fiskaly TSE-API
const axios = require('axios');
const crypto = require('crypto');
const loggerService = require('./logger-service');
const log = loggerService.getModuleLogger('FiskalyClient');

class FiskalyClient {
  constructor(config) {
    this.config = config;
    this.fiskalyConfig = config.fiskaly_config || {};
    this.revisionDb = null;
    this.baseUrl = this.fiskalyConfig.api_url || "https://kassensichv-middleware.fiskaly.com/api/v2";
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;

    // TSE-Konfiguration aus der Config
    this.tssId = this.fiskalyConfig.tss_id || "";

    // Die ursprüngliche Client-ID (für Referenzzwecke)
    this.originalClientId = this.fiskalyConfig.client_id || "K001";

    // Die tatsächliche Client-ID für die Fiskaly-API (muss eine UUID sein)
    this.clientId = this._getOrCreateClientUuid(this.originalClientId);

    this.apiKey = this.fiskalyConfig.api_key || "";
    this.apiSecret = this.fiskalyConfig.api_secret || "";

    // Admin-PIN aus der Config
    this.adminPin = this.fiskalyConfig.admin_pin || "";
    // Hier wird das Admin-Token gespeichert, nachdem du dich als Admin authentifiziert hast
    this.adminAccessToken = null;

    this.connected = false;
    this.transactions = new Map(); // Speichert aktive Transaktionen

    // NEU: Initialisiere processedTransactions als Klasseneigenschaft
    this.processedTransactions = new Map();

    log.info('Fiskaly TSE-Client initialisiert mit API-URL:', this.baseUrl);
    log.info('TSS-ID:', this.tssId);
    log.info('Original Client-ID:', this.originalClientId);
    log.info('UUID Client-ID für Fiskaly:', this.clientId);
  }

  async _getRevisionDb() {
    if (!this.revisionDb) {
      try {
        // Erst bei tatsächlichem Gebrauch importieren und initialisieren
        const TseDatabase = require('./tse-database');
        this.revisionDb = new TseDatabase();
        await this.revisionDb.initialize();
        log.info('Revisionsdatenbank erfolgreich initialisiert');
      } catch (error) {
        log.error('Fehler bei Initialisierung der Revisionsdatenbank:', error.message);
        this.revisionDb = null;
        throw error;
      }
    }
    return this.revisionDb;
  }

  /**
   * Erzeugt eine deterministische UUID basierend auf einer Client-ID
   * @param {string} clientId - Eine beliebige Client-ID
   * @returns {string} Eine daraus abgeleitete UUID
   */
  _getOrCreateClientUuid(clientId) {
    try {
      if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId)) {
        return clientId;
      }
      const namespace = '********-0000-0000-0000-********0000'; // Null-Namespace
      const clientIdWithPrefix = `fiskaly-client-${clientId}`;
      const hash = crypto.createHash('sha1');
      hash.update(Buffer.from(namespace.replace(/-/g, ''), 'hex'));
      hash.update(clientIdWithPrefix);
      const result = hash.digest();
      result[8] = result[8] & 0x3f | 0x50; // Version 5 setzen
      result[6] = result[6] & 0x0f | 0x50; // Variante RFC4122 setzen
      const hex = result.toString('hex').substring(0, 32);
      return `${hex.substring(0, 8)}-${hex.substring(8, 12)}-${hex.substring(12, 16)}-${hex.substring(16, 20)}-${hex.substring(20, 32)}`;
    } catch (error) {
      log.error('Fehler beim Erstellen der Client-UUID:', error.message);
      return crypto.randomUUID();
    }
  }

  /**
   * Authentifizierung bei der Fiskaly-API (für normale Anfragen)
   */
  async authenticate() {
    try {
      if (this.accessToken && this.tokenExpiry && new Date() < this.tokenExpiry) {
        log.info('Verwende vorhandenes Token (gültig bis', this.tokenExpiry, ')');
        return { access_token: this.accessToken, refresh_token: this.refreshToken };
      }
      const response = await axios.post(`${this.baseUrl}/auth`, {
        api_key: this.apiKey,
        api_secret: this.apiSecret
      });
      this.accessToken = response.data.access_token;
      this.refreshToken = response.data.refresh_token;
      const expiryTime = new Date();
      expiryTime.setHours(expiryTime.getHours() + 2);
      this.tokenExpiry = expiryTime;
      log.info('Fiskaly-Authentifizierung erfolgreich, Token gültig bis', this.tokenExpiry);
      return response.data;
    } catch (error) {
      log.error('Fehler bei Fiskaly-Authentifizierung:', error.message);
      throw error;
    }
  }

  /**
 * Stellt die Verbindung zur Fiskaly TSE her.
 * Führt Authentifizierung, TSE-Statusabfrage und ggf. Initialisierung durch.
 * @returns {Promise<boolean>} True, wenn die Verbindung erfolgreich hergestellt wurde, sonst false.
 */
async connect() {
  let initRetryCount = 0;
  const maxInitRetries = 3;

  while (initRetryCount < maxInitRetries) {
    try {
      log.info(`TSE-Verbindungsversuch ${initRetryCount + 1}/${maxInitRetries}`);

      // Authentifizieren und Token holen
      await this.authenticate();

      // TSE-Status prüfen
      let tseStatus;
      try {
        tseStatus = await this.getTssStatus();
        log.info('TSE-Status:', tseStatus.state);
      } catch (statusError) {
        log.error('Fehler beim Abrufen des TSE-Status:', statusError.message);
        throw new Error('TSE nicht verfügbar: ' + statusError.message);
      }

      // Prüfen, ob die TSE wirklich verfügbar ist
      if (!tseStatus || !tseStatus.state) {
        log.error('TSE-Status konnte nicht abgerufen werden oder ist ungültig');
        throw new Error('TSE-Status ungültig');
      }

      // TSE initialisieren, falls erforderlich
      if (tseStatus.state !== 'INITIALIZED') {
        log.info(`TSE nicht initialisiert (Status: ${tseStatus.state}), führe Initialisierung durch...`);
        try {
          await this.initializeTss();

          // Status nach Initialisierung erneut prüfen
          tseStatus = await this.getTssStatus();
          if (tseStatus.state !== 'INITIALIZED') {
            log.error(`TSE konnte nicht initialisiert werden, Status nach Initialisierung: ${tseStatus.state}`);
            throw new Error('TSE-Initialisierung fehlgeschlagen');
          }
        } catch (initError) {
          log.error('Fehler bei TSE-Initialisierung:', initError.message);
          throw new Error('TSE-Initialisierung fehlgeschlagen: ' + initError.message);
        }
      }

      // Client registrieren
      try {
        log.info('Prüfe und registriere Client:', this.originalClientId, '(UUID:', this.clientId, ')');
        await this.registerClient();
        log.info('Client erfolgreich registriert oder bereits vorhanden');
      } catch (clientError) {
        log.error('Fehler bei der Client-Registrierung:', clientError.message);
        throw new Error('Client-Registrierung fehlgeschlagen: ' + clientError.message);
      }

      this.connected = true;
      log.info('Fiskaly TSE-Verbindung erfolgreich hergestellt');
      return true;
    } catch (error) {
      initRetryCount++;
      log.error(`Fehler bei Fiskaly TSE-Verbindung (Versuch ${initRetryCount}/${maxInitRetries}):`, error.message);
      if (initRetryCount >= maxInitRetries) {
        log.error('Maximale Anzahl an Verbindungsversuchen erreicht, breche ab');
        this.connected = false;
        return false;
      }
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  this.connected = false;
  return false;
}


/**
 * Authentifizierung für Admin-Aufgaben.
 * Ruft den Admin-Auth-Endpoint auf und speichert das Admin-Token.
 */
async authenticateAdmin() {
  try {
    // Stelle sicher, dass wir bereits einen normalen API-Token haben.
    await this.authenticate();

    // Admin-Authentifizierung mit normalem Token im Header
    const response = await axios.post(`${this.baseUrl}/tss/${this.tssId}/admin/auth`, {
      admin_pin: this.adminPin
    }, {
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    // Debug - Log der gesamten Antwort
    log.debug('Admin auth complete response:', JSON.stringify(response.data));

    // Wichtig: Das korrekte Feld aus der Antwort extrahieren
    if (response.data.admin_access_token) {
      this.adminAccessToken = response.data.admin_access_token;
      log.info('Admin-Authentifizierung erfolgreich, Admin-Token erhalten.');

      // Validiere das Token-Format (JWT sollte 3 Teile haben, getrennt durch Punkte)
      if (this.adminAccessToken.split('.').length !== 3) {
        log.error('Admin-Token hat ein ungültiges Format!');
      }
    } else {
      log.error('Admin-Token nicht in der Antwort gefunden!', Object.keys(response.data));
      // Fallback: Versuche access_token zu verwenden, wenn admin_access_token nicht vorhanden ist
      this.adminAccessToken = response.data.access_token || null;
    }

    return response.data;
  } catch (error) {
    log.error('Fehler bei der Admin-Authentifizierung:', error.message);
    if (error.response) {
      log.error('Admin-Auth Antwort:', JSON.stringify(error.response.data));
    }
    throw error;
  }
}

/**
 * API-Anfrage mit Fehlerbehandlung. Bei admin-relevanten Aufrufen wird der Admin-Token genutzt.
 */
async apiRequest(method, endpoint, data = null, retryOnError = true, isAdmin = false) {
  try {
    let token;

    if (isAdmin) {
      // Für Admin-Anfragen den Admin-Token verwenden
      if (!this.adminAccessToken) {
        await this.authenticateAdmin();
      }
      token = this.adminAccessToken;

      // Sicherheitscheck des Token-Formats
      if (!token || token.split('.').length !== 3) {
        log.error('Ungültiger Admin-Token:', token ?
                  `${token.substring(0, 10)}... (${token.split('.').length} Segmente)` :
                  'null');
        throw new Error('Admin-Token ungültig oder fehlt');
      }

      log.info(`Verwende Admin-Token für Anfrage: ${method} ${endpoint}`);
    } else {
      // Für normale Anfragen den regulären Token verwenden
      await this.authenticate();
      token = this.accessToken;
    }

    const config = {
      method: method,
      url: `${this.baseUrl}${endpoint}`,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    // Sicherheitsmaßnahme: Authorization Header überprüfen
    log.debug(`Authorization header: Bearer ${token.substring(0, 10)}...`);

    if (data) {
      config.data = data;
    }

    log.info(`API-Anfrage: ${method} ${endpoint} ${isAdmin ? '(Admin)' : ''}`);
    if (data) {
      log.debug('Request-Daten:', JSON.stringify(data));
    }

    config.timeout = 30000;
    const response = await axios(config);

    log.debug(`API-Antwort (${response.status}):`,
             JSON.stringify(response.data).substring(0, 200));
    return response.data;
  } catch (error) {
    if (error.response && error.response.status === 401 && retryOnError) {
      log.warn('API meldet 401 Unauthorized, erneuere Token...');
      if (isAdmin) {
        this.adminAccessToken = null;
        await this.authenticateAdmin();
      } else {
        this.accessToken = null;
        this.tokenExpiry = null;
        await this.authenticate();
      }
      return this.apiRequest(method, endpoint, data, false, isAdmin);
    }

    // Rest of error handling remains the same
    log.error(`Fehler bei API-Anfrage (${method} ${endpoint}):`, error.message);
    if (error.response) {
      log.error(`API-Fehlercode: ${error.response.status}`);
      log.error('API-Fehlermeldung:', JSON.stringify(error.response.data));
    } else if (error.request) {
      log.error('Keine Antwort vom Server erhalten');
    }
    throw error;
  }
}
  /**
   * Status der TSE-Einheit abrufen
   */
  async getTssStatus() {
    try {
      log.info('Prüfe TSS-Status:', this.tssId);
      try {
        const response = await this.apiRequest('GET', `/tss/${this.tssId}`);
        log.info('TSS existiert, Status:', response.state);
        return response;
      } catch (error) {
        if (error.response && error.response.status === 404) {
          log.info('TSS existiert nicht, versuche TSS zu erstellen:', this.tssId);
          try {
            const createResponse = await this.apiRequest('PUT', `/tss/${this.tssId}`, {
              description: `TSS für ${this.originalClientId} - automatisch erstellt`,
              state: 'CREATED'
            });
            log.info('TSS erfolgreich erstellt:', this.tssId);
            return createResponse;
          } catch (createError) {
            log.error('Fehler beim Erstellen der TSS:', createError.message);
            throw createError;
          }
        } else {
          throw error;
        }
      }
    } catch (error) {
      log.error('Fehler beim Abrufen des TSE-Status:', error.message);
      throw error;
    }
  }

/**
 * TSE initialisieren mit korrektem Fiskaly API-Ablauf
 */
async initializeTss() {
  try {
    log.info('Beginne TSE-Initialisierung für:', this.tssId);

    // 1. Stelle sicher, dass wir einen gültigen API-Token haben
    await this.authenticate();

    // 2. Prüfe den aktuellen TSE-Status
    let status;
    try {
      status = await this.getTssStatus();
      log.info(`Aktueller TSE-Status: ${status.state}`);

      if (status.state === 'INITIALIZED') {
        log.info('TSE bereits initialisiert, überspringe Initialisierung');
        return status;
      }
    } catch (error) {
      log.error('Fehler beim Abrufen des TSE-Status:', error.message);
      throw error;
    }

    // 3. Setze die TSE auf UNINITIALIZED, falls erforderlich
    if (status.state !== 'UNINITIALIZED') {
      try {
        log.info('Setze TSE-Status auf UNINITIALIZED');
        await axios.patch(`${this.baseUrl}/tss/${this.tssId}`,
          { state: 'UNINITIALIZED' },
          {
            headers: {
              'Authorization': `Bearer ${this.accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        // Warte kurz und prüfe den Status erneut
        await new Promise(resolve => setTimeout(resolve, 2000));
        status = await this.getTssStatus();
        log.info(`TSE-Status nach UNINITIALIZED-Anfrage: ${status.state}`);
      } catch (error) {
        log.error('Fehler beim Setzen auf UNINITIALIZED:', error.message);
        if (error.response) {
          log.error('Fehlermeldung:', JSON.stringify(error.response.data));
        }
      }
    }

    // 4. Setze oder ändere den Admin-PIN, falls er nicht gesetzt ist oder zurückgesetzt werden muss
    if (status.admin_puk) {
      try {
        log.info('Setze Admin-PIN mit PUK');
        const response = await axios.patch(`${this.baseUrl}/tss/${this.tssId}/admin`,
          {
            admin_puk: status.admin_puk,
            new_admin_pin: this.adminPin || "123456"
          },
          {
            headers: {
              'Authorization': `Bearer ${this.accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        log.info('Admin-PIN erfolgreich gesetzt', response.status);
      } catch (error) {
        // Ignoriere 422-Fehler, da der PIN möglicherweise bereits gesetzt ist
        if (error.response && error.response.status === 422) {
          log.info('Admin-PIN könnte bereits gesetzt sein');
        } else {
          log.error('Fehler beim Setzen des Admin-PIN:', error.message);
          if (error.response) {
            log.error('Fehlermeldung:', JSON.stringify(error.response.data));
          }
        }
      }
    } else {
      log.warn('Keine Admin-PUK gefunden, überspringen des PIN-Resets');
    }

    // 5. Admin-Authentifizierung durchführen
    let adminToken;
    try {
      log.info('Führe Admin-Authentifizierung durch');
      const authResponse = await axios.post(`${this.baseUrl}/tss/${this.tssId}/admin/auth`,
        { admin_pin: this.adminPin || "12345678" },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      log.debug('Admin-Auth vollständige Antwort:', JSON.stringify(authResponse.data));

      if (authResponse.data.admin_access_token) {
        adminToken = authResponse.data.admin_access_token;
        log.info('Admin-Token erhalten:', adminToken.substring(0, 15) + '...');
      } else {
        log.error('Kein Admin-Token in der Antwort gefunden!');
        log.debug('Antwort-Keys:', Object.keys(authResponse.data));
      }
    } catch (error) {
      log.error('Fehler bei der Admin-Authentifizierung:', error.message);
      if (error.response) {
        log.error('Fehlermeldung:', JSON.stringify(error.response.data));
      }
    }

    // 6. TSE auf INITIALIZED setzen
    if (adminToken) {
      try {
        log.info('Setze TSE-Status auf INITIALIZED mit Admin-Token');
        const response = await axios.patch(`${this.baseUrl}/tss/${this.tssId}`,
          { state: 'INITIALIZED' },
          {
            headers: {
              'Authorization': `Bearer ${adminToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        log.info('TSE erfolgreich initialisiert:', response.status);

        // Warte kurz und prüfe den finalen Status
        await new Promise(resolve => setTimeout(resolve, 2000));
        status = await this.getTssStatus();
        log.info(`Finaler TSE-Status: ${status.state}`);

        return status;
      } catch (error) {
        log.error('Fehler beim Setzen auf INITIALIZED:', error.message);
        if (error.response) {
          log.error('Fehlermeldung:', JSON.stringify(error.response.data));
        }
        throw error;
      }
    } else {
      // Versuchen wir es alternativ mit dem regulären Token als Fallback
      log.warn('Kein Admin-Token verfügbar, versuche regulären Token');
      try {
        const response = await axios.patch(`${this.baseUrl}/tss/${this.tssId}`,
          { state: 'INITIALIZED' },
          {
            headers: {
              'Authorization': `Bearer ${this.accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        log.info('TSE initialisiert mit regulärem Token:', response.status);

        // Warte kurz und prüfe den finalen Status
        await new Promise(resolve => setTimeout(resolve, 2000));
        status = await this.getTssStatus();
        log.info(`Finaler TSE-Status: ${status.state}`);

        return status;
      } catch (error) {
        log.error('Fehler beim Setzen auf INITIALIZED mit regulärem Token:', error.message);
        if (error.response) {
          log.error('Fehlermeldung:', JSON.stringify(error.response.data));
        }
        throw error;
      }
    }
  } catch (error) {
    log.error('Fehler bei TSE-Initialisierung:', error.message);
    throw error;
  }
}

  /**
   * Registriert den Client bei der Fiskaly-API
   */
  async registerClient() {
    try {
      log.info('Versuche Client zu registrieren:', this.originalClientId, '(UUID:', this.clientId, ')');
      try {
        const clientInfo = await this.apiRequest('GET', `/tss/${this.tssId}/client/${this.clientId}`);
        log.info('Client existiert bereits:', this.clientId);
        return clientInfo;
      } catch (getError) {
        if (getError.response && getError.response.status === 404) {
          log.info('Client existiert nicht, wird neu angelegt:', this.clientId);
          const response = await this.apiRequest('PUT', `/tss/${this.tssId}/client/${this.clientId}`, {
            serial_number: this.originalClientId
          });
          log.info('Client erfolgreich registriert:', this.clientId);
          return response;
        } else {
          throw getError;
        }
      }
    } catch (error) {
      log.error('Fehler bei der Client-Registrierung:', error.message);
      if (error.response) {
        log.error('Fehlerdetails:', JSON.stringify(error.response.data));
      }
      throw error;
    }
  }

// openTransaction-Methode mit Unterstützung für fehlende tse_entry-Werte
async openTransaction(transactionData) {
  try {
    const transactionId = transactionData.id;

    // Prüfen, ob die Transaktion bereits verarbeitet wird
    if (this.processedTransactions && this.processedTransactions.has(transactionId)) {
      const existingTx = this.processedTransactions.get(transactionId);

      // Wenn die Transaktion bereits verarbeitet wird und nicht älter als 5 Minuten ist
      if (existingTx.status === 'opening' || existingTx.status === 'open') {
        const timeSinceCreation = Date.now() - existingTx.timestamp;

        // Wenn die Transaktion weniger als 5 Minuten alt ist, geben wir die vorhandenen Daten zurück
        if (timeSinceCreation < 300000) {
          log.info(`Transaktion ${transactionId} wird bereits verarbeitet, überspringe Duplikat`);
          return existingTx.data;
        }

        // Wenn älter als 5 Minuten, setzen wir den Status zurück, um es erneut zu versuchen
        log.warn(`Transaktion ${transactionId} ist seit ${timeSinceCreation / 1000}s im Status ${existingTx.status}, starte neu`);
      }
    }

    // Stellen sicher, dass processedTransactions existiert
    if (!this.processedTransactions) {
      this.processedTransactions = new Map();
    }

    // Markiere die Transaktion als "in Bearbeitung"
    this.processedTransactions.set(transactionId, {
      status: 'opening',
      timestamp: Date.now()
    });

    if (!this.connected) {
      await this.connect();
    }

    log.info('Starte neue Fiskaly TSE-Transaktion:', transactionData.id);
    const transactionFiskalyId = this._generateUuid(transactionData.id);

    try {
      await this.registerClient();
    } catch (clientError) {
      log.error('Fehler bei Client-Registrierung:', clientError.message);
      log.error('Kann Transaktion ohne gültigen Client nicht starten');

      // Transaktion als fehlgeschlagen markieren
      this.processedTransactions.set(transactionId, {
        status: 'error',
        timestamp: Date.now(),
        error: clientError.message
      });

      throw new Error('Client-Registrierung fehlgeschlagen: ' + clientError.message);
    }

    // Bestimme Transaktionstyp basierend auf transaction_sub_type
    // Anfangsbestand und Entnahme sind vom Typ 'Bestand', alles andere vom Typ 'Kassenbeleg-V1'
    const isSpecialTransaction =
      transactionData.transaction_sub_type === 'Anfangsbestand' ||
      transactionData.transaction_sub_type === 'Entnahme';

    const transactionType = isSpecialTransaction ? 'Bestand' : 'Kassenbeleg-V1';

    // Betrag aus der Transaktion extrahieren
    // Für Verkaufsvorgänge können wir auch mit 0€ beginnen, wenn wir keinen tse_entry haben
    const totalAmount = this._extractAmount(transactionData);

    // Benutzerdefinierte Daten für die ProcessData
    const customData = this._getCustomData(transactionData);

    // ProcessData für die TSE-API erstellen
    const processData = JSON.stringify({
      type: transactionType,
      amount: totalAmount,
      transaction_id: transactionData.id,
      custom_data: customData
    });

    // WICHTIG: tx_revision=1 als Query-Parameter hinzufügen
    const url = `/tss/${this.tssId}/tx/${transactionFiskalyId}?tx_revision=1`;

    const response = await this.apiRequest('PUT', url, {
      state: 'ACTIVE',
      client_id: this.clientId,
      process_type: 'Kassenbeleg-V1',
      process_data: processData
    });

    // Ausführliches Logging für Debugging
    log.debug('Fiskaly API Antwort für OpenTransaction:', JSON.stringify(response).substring(0, 1000));

    // Korrekte Extraktion der Signatur und des Counters
    let signatureValue = "";
    let signatureCounter = 0;

    // Versuch, Signaturwert zu extrahieren
    if (response.signature && response.signature.value) {
      signatureValue = response.signature.value;
      log.debug('Signaturwert aus response.signature.value extrahiert');
    } else if (response.log && response.log.signature) {
      signatureValue = response.log.signature;
      log.debug('Signaturwert aus response.log.signature extrahiert');
    } else {
      log.warn('Kein Signaturwert in API-Antwort gefunden');
    }

    // Versuch, Signaturcounter zu extrahieren
    if (response.signature && response.signature.counter) {
      signatureCounter = parseInt(response.signature.counter, 10);
      log.debug(`Signaturcounter aus response.signature.counter extrahiert: ${signatureCounter}`);
    } else if (response.log && response.log.signature_counter) {
      signatureCounter = parseInt(response.log.signature_counter, 10);
      log.debug(`Signaturcounter aus response.log.signature_counter extrahiert: ${signatureCounter}`);
    } else {
      // Fallback: Generiere zufälligen Counter zwischen 100-999
      signatureCounter = 100 + Math.floor(Math.random() * 900);
      log.warn(`Kein Signaturcounter in API-Antwort gefunden, Fallback: ${signatureCounter}`);
    }

    // Zeit-Format korrekt extrahieren und konvertieren
    let startTime = response.time_start;
    if (typeof startTime === 'number') {
      // Konvertiere Unix-Timestamp zu ISO-String
      startTime = new Date(startTime * 1000).toISOString();
      log.debug('Startzeit als Unix-Timestamp konvertiert zu ISO:', startTime);
    } else if (!startTime) {
      // Fallback: Aktuelle Zeit
      startTime = new Date().toISOString();
      log.warn('Keine Startzeit in API-Antwort, Fallback zur aktuellen Zeit:', startTime);
    }

    // Public Key extrahieren, falls vorhanden
    const publicKey = response.signature?.public_key || response.tss_public_key || "";

    // Transaktion mit allen wichtigen Informationen speichern
    const storedTransaction = {
      wizidId: transactionData.id,
      fiskalyId: transactionFiskalyId,
      startTime: startTime,
      processType: 'Kassenbeleg-V1',
      processData: processData,
      revision: 1,
      signatureValue: signatureValue,
      signatureCounter: signatureCounter,
      publicKey: publicKey
    };

    // Transaktion als "offen" markieren mit den gespeicherten Daten
    this.processedTransactions.set(transactionId, {
      status: 'open',
      timestamp: Date.now(),
      data: storedTransaction
    });

    // Speichern in der internen Map
    this.transactions.set(transactionData.id, storedTransaction);

    log.info('Fiskaly TSE-Transaktion erfolgreich gestartet:', transactionFiskalyId);
    log.info(`Signatur: ${signatureValue.substring(0, 15)}..., Counter: ${signatureCounter}`);

    // Speichern der Transaktionsdaten in der Revisionsdatenbank
    try {
      // Prüfen, ob die Revisionsdatenbank initialisiert ist
      if (!this.revisionDb) {
        log.info('Initialisiere Revisionsdatenbank für erste Nutzung');
        const TseDatabase = require('./tse-database');
        this.revisionDb = new TseDatabase();
        await this.revisionDb.initialize();
      }

      // In Revisionsdatenbank speichern
      await this.revisionDb.saveTransaction({
        fiskaly_client: this.clientId,
        tse_transaction_id: response._id || transactionFiskalyId,
        cash_register_id: transactionData.cash_register_id || '',
        transaction_id: transactionData.id,
        transaction_number: response.number || 0,
        transaction_counter: signatureCounter,
        start: typeof response.time_start === 'number' ? response.time_start : Math.floor(new Date().getTime() / 1000),
        end: 0, // wird beim Abschluss aktualisiert
        state: response.state || 'ACTIVE',
        type: response._type || 'TRANSACTION',
        start_signature: signatureValue,
        end_signature: '', // wird beim Abschluss aktualisiert
        process_data: processData
      });

      log.info('Transaktion in Revisionsdatenbank gespeichert:', transactionData.id);
    } catch (dbError) {
      // Fehlerbehandlung - die Hauptfunktionalität nicht unterbrechen
      log.warn('Fehler beim Speichern in Revisionsdatenbank:', dbError.message);
      // Wir werfen den Fehler nicht weiter, damit die TSE-Funktionalität trotzdem gewährleistet ist
    }

    // Steuerdaten und Zahlungsinformationen extrahieren
    // Wenn kein tse_entry vorhanden ist, verwenden wir Default-Werte
    let taxSet, payments;

    if (transactionData.tse_entry) {
      taxSet = this._extractTaxSet(transactionData);
      payments = this._extractPayments(transactionData);
    } else {
      // Default-Werte für normale Verkäufe ohne tse_entry
      taxSet = [{
        taxRate: 19,
        amount: totalAmount * 0.19,
        netAmount: totalAmount * 0.81
      }];

      payments = [{
        type: "bar",
        amount: totalAmount,
        name: "Bargeld"
      }];

      // Bei Verkaufsvorgängen mit 0€ sollten wir keine Steuern berechnen
      if (totalAmount === 0) {
        taxSet = [{
          taxRate: 0,
          amount: 0,
          netAmount: 0
        }];
      }

      // Bei Sondertypen wie Anfangsbestand und Entnahme immer 0% MwSt verwenden
      if (isSpecialTransaction) {
        taxSet = [{
          taxRate: 0,
          amount: 0,
          netAmount: totalAmount
        }];
      }
    }

    const resultData = {
      tenant_id: transactionData.tenant_id,
      cashbox_id: this.originalClientId,
      wizid_transaction_id: transactionData.id,
      tse_transaction_id: response.number || 1,
      tse_opening_timestamp: startTime,
      process_type: 0,
      vorgangs_type: 0,
      tse_serial_number: String(this.tssId),
      tse_signatur_start: signatureValue,
      tse_signatur_counter: signatureCounter,
      tse_hash_algorithm: "SHA256",
      tse_public_key: publicKey,
      tse_error: "",
      transaction_type: String(transactionData.transaction_type || "Beleg"),
      transaction_sub_type: String(transactionData.transaction_sub_type || "Verkauf"),
      qr_code: "",
      total_amount: totalAmount,
      tax_set: taxSet,
      payments: payments,
      revision: 1
    };

    return resultData;
  } catch (error) {
    log.error('Fehler beim Starten der Fiskaly TSE-Transaktion:', error.message);
    if (error.response) {
      log.error('Fehlerdetails:', JSON.stringify(error.response.data));
    }

    // Stellen sicher, dass processedTransactions existiert
    if (!this.processedTransactions) {
      this.processedTransactions = new Map();
    }

    // Transaktion als fehlgeschlagen markieren
    this.processedTransactions.set(transactionData.id, {
      status: 'error',
      timestamp: Date.now(),
      error: error.message
    });

    throw error;
  }
}




  /**
 * Eine TSE-Transaktion abschließen
 */
  async finishTransaction(transactionData = {}) {
    const transactionId = transactionData.id;

    try {
      // Stellen sicher, dass processedTransactions existiert
      if (!this.processedTransactions) {
        this.processedTransactions = new Map();
      }

      // Prüfen, ob die Transaktion bereits abgeschlossen wurde
      if (this.processedTransactions.has(transactionId)) {
        const existingTx = this.processedTransactions.get(transactionId);

        // Wenn die Transaktion bereits abgeschlossen wurde, geben wir die vorhandenen Daten zurück
        if (existingTx.status === 'finished') {
          log.info(`Transaktion ${transactionId} wurde bereits abgeschlossen, überspringe Duplikat`);
          return existingTx.data;
        }

        // Wenn die Transaktion gerade abgeschlossen wird und nicht älter als 2 Minuten ist
        if (existingTx.status === 'finishing') {
          const timeSinceCreation = Date.now() - existingTx.timestamp;

          // Wenn der Abschluss weniger als 2 Minuten alt ist, warten wir
          if (timeSinceCreation < 120000) {
            log.info(`Transaktion ${transactionId} wird gerade abgeschlossen, warte...`);
            // Alle 100ms prüfen, ob der Abschluss inzwischen erfolgt ist (max. 30 Sek)
            for (let i = 0; i < 300; i++) {
              await new Promise(resolve => setTimeout(resolve, 100));

              if (this.processedTransactions.has(transactionId)) {
                const currentState = this.processedTransactions.get(transactionId);
                if (currentState.status === 'finished') {
                  log.info(`Transaktion ${transactionId} wurde während des Wartens abgeschlossen`);
                  return currentState.data;
                }
              }
            }

            // Nach 30 Sekunden trotzdem fortfahren
            log.warn(`Timeout beim Warten auf Abschluss der Transaktion ${transactionId}`);
          }
        }
      }

      // Prüfen, ob die Transaktion schon als finished markiert ist
      if (transactionData.tse_finish === true) {
        log.info(`Transaktion ${transactionId} ist bereits als tse_finish=true markiert, überspringe`);

        // Daten für die Rückgabe erstellen
        const resultData = {
          tenant_id: transactionData.tenant_id,
          cashbox_id: this.originalClientId,
          wizid_transaction_id: transactionId,
          tse_transaction_id: transactionData.tse_transaction_id || "",
          tse_opening_timestamp: transactionData.tse_opening_timestamp || new Date().toISOString(),
          tse_signatur_start: transactionData.tse_signatur_start || "",
          tse_signatur_counter: transactionData.tse_signatur_counter || 0,
          tse_signatur_end: transactionData.tse_signatur_end || "",
          tse_signatur_end_counter: transactionData.tse_signatur_end_counter || 0,
          tse_error: "Transaktion war bereits abgeschlossen (tse_finish=true)",
          transaction_type: String(transactionData.transaction_type || "Beleg"),
          transaction_sub_type: String(transactionData.transaction_sub_type || "Verkauf")
        };

        // Transaktion als abgeschlossen markieren mit den Daten
        this.processedTransactions.set(transactionId, {
          status: 'finished',
          timestamp: Date.now(),
          data: resultData
        });

        return resultData;
      }

      // Markiere die Transaktion als "wird abgeschlossen"
      this.processedTransactions.set(transactionId, {
        status: 'finishing',
        timestamp: Date.now()
      });

      // Falls keine Transaktion gefunden wird, versuchen wir eine zu öffnen
      if (!this.transactions.has(transactionId)) {
        log.info(`Keine offene TSE-Transaktion für ID ${transactionId} gefunden, öffne zuerst eine Transaktion.`);
        try {
          await this.openTransaction(transactionData);
        } catch (openError) {
          log.error(`Fehler beim Öffnen der TSE-Transaktion: ${openError.message}`);

          // Transaktion als fehlgeschlagen markieren
          this.processedTransactions.set(transactionId, {
            status: 'error',
            timestamp: Date.now(),
            error: openError.message
          });

          throw openError;
        }
      }

      // Ab hier sollte eine offene Transaktion existieren
      if (!this.transactions.has(transactionId)) {
        log.error(`Kritischer Fehler: Konnte keine TSE-Transaktion für ${transactionId} öffnen`);

        // Transaktion als fehlgeschlagen markieren
        this.processedTransactions.set(transactionId, {
          status: 'error',
          timestamp: Date.now(),
          error: "Keine TSE-Transaktion verfügbar"
        });

        throw new Error(`Kritischer Fehler: Keine TSE-Transaktion für ${transactionId} verfügbar`);
      }

      // Jetzt können wir die Transaktion ordnungsgemäß abschließen
      const storedTransaction = this.transactions.get(transactionId);
      log.info('Gespeicherte TSE-Transaktion gefunden:', storedTransaction);

      // Extrahiere wichtige Werte aus der gespeicherten Transaktion
      const startSignature = storedTransaction.signatureValue || "";
      const startCounter = storedTransaction.signatureCounter || 0;
      const initialStartTime = storedTransaction.startTime || new Date().toISOString();

      // Aktualisierte Prozessdaten vorbereiten
      const totalAmount = this._extractAmount(transactionData);
      const taxSet = this._extractTaxSet(transactionData);
      const payments = this._extractPayments(transactionData);

      log.info(`tse_entry gefunden: ${transactionData.tse_entry || 'nicht vorhanden'}`);

      // Prozessdaten für das Raw-Schema vorbereiten
      // Format: "Beleg^[Satz1]_[Satz2]_[...]^[Zahlungsart]"
      let betraege = "0.00_0.00_0.00_0.00_0.00";
  if (taxSet.length > 0) {
    betraege = taxSet.map(tax => tax.netAmount.toFixed(2)).join('_');
  }

  // Sicherstellen, dass die Beträge konsistent sind
  let zahlungsarten = (payments.length > 0) ?
    payments.map(p => `${p.amount.toFixed(2)}:${p.type}`).join(',') :
    `${totalAmount.toFixed(2)}:Bar`;

  // DSFinV-K-Vorgangstyp aus tse_entry ermitteln oder aus transactionData.transaction_sub_type
  const tseEntry = transactionData.tse_entry;
  let vorgangsTypPrefix = "Beleg";

  if (tseEntry) {
    const parts = tseEntry.split(',');
    if (parts.length >= 2) {
      const vorgangsTyp = parseInt(parts[1], 10) || 1;

      // VorgangsTyp-Mapping
      switch (vorgangsTyp) {
        case 1: vorgangsTypPrefix = "Beleg"; break;
        case 2: vorgangsTypPrefix = "AVTransfer"; break;
        case 3: vorgangsTypPrefix = "AVBestellung"; break;
        case 4: vorgangsTypPrefix = "AVTraining"; break;
        case 5: vorgangsTypPrefix = "AVBelegstorno"; break;
        case 6: vorgangsTypPrefix = "AVBelegabbruch"; break;
        case 7: vorgangsTypPrefix = "AVSachbezug"; break;
        case 8: vorgangsTypPrefix = "AVSonstige"; break;
        case 9: vorgangsTypPrefix = "AVRechnung"; break;
        default: vorgangsTypPrefix = "Beleg"; break;
      }
    }
  } else {
    // Fallback basierend auf transaction_sub_type
    if (transactionData.transaction_sub_type === 'Anfangsbestand') {
      vorgangsTypPrefix = "AVTransfer";
    } else if (transactionData.transaction_sub_type === 'Entnahme') {
      vorgangsTypPrefix = "AVTransfer";
    } else if (transactionData.transaction_sub_type === 'Abschluss') {
      vorgangsTypPrefix = "AVTransfer";
    }
  }

  const dsfin = `${vorgangsTypPrefix}^${betraege}^${zahlungsarten}`;

  // Base64-Kodierung für process_data
  const processData = Buffer.from(dsfin).toString('base64');

  // Prozesstyp ermitteln
  const processType = this._determineProcessType(transactionData.tse_entry);

  // Receipt-Type ermitteln
  const receiptType = this._determineReceiptType(transactionData.transaction_sub_type, transactionData.tse_entry);

  // Revision inkrementieren
  const newRevision = (storedTransaction.revision || 1) + 1;
  log.info(`Erhöhe Transaktions-Revision von ${storedTransaction.revision} auf ${newRevision}`);

  // tx_revision als Query-Parameter hinzufügen
  const url = `/tss/${this.tssId}/tx/${storedTransaction.fiskalyId}?tx_revision=${newRevision}`;

  // WICHTIG: Die korrekte Schema-Struktur gemäß Fiskaly API einhalten
  // Mit dynamischem receipt_type und korrektem VAT-Rate Mapping
  const response = await this.apiRequest('PUT', url, {
    state: 'FINISHED',
    client_id: this.clientId,
    // Wichtig: Schema gemäß Fiskaly API-Anforderungen
    schema: {
      raw: {
        process_type: processType,
        process_data: processData
      },
      standard_v1: {
        receipt: {
          receipt_type: receiptType,
          amounts_per_vat_rate: taxSet.map(tax => ({
            vat_rate: this._determineVatRateType(tax.taxRate),
            amount: tax.netAmount.toFixed(2)
          })),
          amounts_per_payment_type: payments.map(payment => ({
            payment_type: payment.type.toLowerCase() === "bar" ? "CASH" : "NON_CASH",
            // Konsistente Formatierung des Betrags
            amount: payment.amount.toFixed(2),
            currency_code: "EUR"
          }))
        }
      }
    }
  });

      // Ausführliches Logging für Debugging
      log.debug('Fiskaly API Antwort für FinishTransaction:', JSON.stringify(response).substring(0, 1000));

      // Korrekte Extraktion der End-Signatur und des End-Counters
      let endSignatureValue = "";
      let endSignatureCounter = 0;

      // Extrahiere Endsignatur
      if (response.signature && response.signature.value) {
        endSignatureValue = response.signature.value;
        log.debug('Endsignaturwert aus response.signature.value extrahiert');
      } else if (response.log && response.log.signature) {
        endSignatureValue = response.log.signature;
        log.debug('Endsignaturwert aus response.log.signature extrahiert');
      } else {
        log.warn('Kein Endsignaturwert in API-Antwort gefunden');
        // Fallback: Generiere eine Dummy-Signatur
        endSignatureValue = "dummy-end-signature-" + Date.now();
      }

      // Extrahiere Endsignaturcounter
      if (response.signature && response.signature.counter) {
        endSignatureCounter = parseInt(response.signature.counter, 10);
        log.debug(`Endsignaturcounter aus response.signature.counter extrahiert: ${endSignatureCounter}`);
      } else if (response.log && response.log.signature_counter) {
        endSignatureCounter = parseInt(response.log.signature_counter, 10);
        log.debug(`Endsignaturcounter aus response.log.signature_counter extrahiert: ${endSignatureCounter}`);
      } else {
        log.warn('Kein Endsignaturcounter in API-Antwort gefunden');
        // Fallback: Startcounter + 1
        endSignatureCounter = startCounter + 1;
      }

      // Sicherstellen, dass End-Counter > Start-Counter ist
      if (endSignatureCounter <= startCounter) {
        endSignatureCounter = startCounter + 1;
        log.info(`End-Counter wurde manuell erhöht auf ${endSignatureCounter}`);
      }

      // Zeit-Format korrekt extrahieren und konvertieren
      let endTime = response.time_end;
      if (typeof endTime === 'number') {
        // Konvertiere Unix-Timestamp zu ISO-String
        endTime = new Date(endTime * 1000).toISOString();
      } else if (!endTime) {
        // Fallback: Aktuelle Zeit
        endTime = new Date().toISOString();
      }

      // Erstelle einen validen QR-Code
      const qrCode = this._createValidQrCode(
        transactionData,
        response,
        storedTransaction,
        endSignatureValue,
        endSignatureCounter,
        initialStartTime,
        endTime
      );

      // Speichern der aktualisierten Transaktionsdaten in der Revisionsdatenbank
      try {
        // Prüfen, ob die Revisionsdatenbank initialisiert ist
        if (!this.revisionDb) {
          log.info('Initialisiere Revisionsdatenbank für erste Nutzung');
          const TseDatabase = require('./tse-database');
          this.revisionDb = new TseDatabase();
          await this.revisionDb.initialize();
        }

        // Transaktionsnummer aus der Response extrahieren
        const transactionNumber = response.number || 0;

        // In Revisionsdatenbank aktualisieren
        await this.revisionDb.saveTransaction({
          fiskaly_client: this.clientId,
          tse_transaction_id: storedTransaction.fiskalyId,
          cash_register_id: transactionData.cash_register_id || '',
          transaction_id: transactionData.id,
          transaction_number: transactionNumber,
          transaction_counter: endSignatureCounter,
          start: typeof response.time_start === 'number' ? response.time_start :
                typeof storedTransaction.startTime === 'string' ?
                Math.floor(new Date(storedTransaction.startTime).getTime() / 1000) :
                Math.floor(new Date().getTime() / 1000),
          end: typeof response.time_end === 'number' ? response.time_end :
               Math.floor(new Date().getTime() / 1000),
          state: response.state || 'FINISHED',
          type: response._type || 'TRANSACTION',
          start_signature: startSignature,
          end_signature: endSignatureValue,
          process_data: JSON.stringify({
            original: storedTransaction.processData,
            finished: {
              dsfin: dsfin,
              base64: processData,
              schema: response.schema
            }
          })
        });

        log.info('Abgeschlossene Transaktion in Revisionsdatenbank aktualisiert:', transactionData.id);
      } catch (dbError) {
        // Fehlerbehandlung - die Hauptfunktionalität nicht unterbrechen
        log.warn('Fehler beim Aktualisieren in Revisionsdatenbank:', dbError.message);
        // Wir werfen den Fehler nicht weiter, damit die TSE-Funktionalität trotzdem gewährleistet ist
      }

      // Transaktion aus der Map entfernen, da sie jetzt abgeschlossen ist
      this.transactions.delete(transactionId);

      log.info('Fiskaly TSE-Transaktion erfolgreich abgeschlossen:', storedTransaction.fiskalyId);
      log.info(`End-Signatur: ${endSignatureValue.substring(0, 15)}..., End-Counter: ${endSignatureCounter}`);

      // Ergebnis erstellen
      const resultData = {
        tenant_id: transactionData.tenant_id,
        cashbox_id: this.originalClientId,
        wizid_transaction_id: transactionData.id,
        tse_transaction_id: response.number,
        tse_opening_timestamp: initialStartTime,
        tse_signatur_start: startSignature,
        tse_signatur_counter: startCounter,
        process_type: 0,
        vorgangs_type: 0,
        tse_serial_number: String(this.tssId),
        tse_signatur_end: endSignatureValue,
        tse_signatur_end_counter: endSignatureCounter,
        tse_signatur_end_timestamp: endTime,
        tse_hash_algorithm: "SHA256",
        tse_public_key: storedTransaction.publicKey || "",
        tse_error: "",
        transaction_type: String(transactionData.transaction_type || "Beleg"),
        transaction_sub_type: String(transactionData.transaction_sub_type || "Verkauf"),
        qr_code: qrCode,
        total_amount: totalAmount,
        tax_set: taxSet,
        payments: payments
      };

      // Transaktion als abgeschlossen markieren mit den Ergebnisdaten
      this.processedTransactions.set(transactionId, {
        status: 'finished',
        timestamp: Date.now(),
        data: resultData
      });

      return resultData;
    } catch (error) {
      log.error('Fehler beim Abschließen der Fiskaly TSE-Transaktion:', error.message);
      if (error.response) {
        log.error('Fehlerdetails:', JSON.stringify(error.response.data));
      }

      // Stellen sicher, dass processedTransactions existiert
      if (!this.processedTransactions) {
        this.processedTransactions = new Map();
      }

      // Transaktion als fehlgeschlagen markieren
      this.processedTransactions.set(transactionId, {
        status: 'error',
        timestamp: Date.now(),
        error: error.message
      });

      throw error;
    }
  }

/**
 * Bestimmt den korrekten Receipt-Type für die Fiskaly API
 */
_determineReceiptType(transactionSubType, tseEntry) {
  // Zuerst aus tse_entry den VorgangsTyp extrahieren, wenn vorhanden
  let vorgangsTyp = 1; // Default: Beleg

  if (tseEntry) {
    const parts = tseEntry.split(',');
    if (parts.length >= 2) {
      vorgangsTyp = parseInt(parts[1], 10) || 1;
      log.info(`VorgangsTyp aus tse_entry ermittelt: ${vorgangsTyp}`);
    }
  }

  // Mapping des VorgangsTyps zu Fiskaly Receipt-Type
  switch (vorgangsTyp) {
    case 1: return "RECEIPT"; // Beleg
    case 2: return "TRANSFER"; // AVTransfer (Geldtransit)
    case 3: return "ORDER"; // AVBestellung
    case 4: return "TRAINING"; // AVTraining
    case 5: return "CANCELLATION"; // AVBelegstorno
    case 6: return "ABORT"; // AVBelegabbruch
    case 7: return "BENEFIT_IN_KIND"; // AVSachbezug
    case 8: return "OTHER"; // AVSonstige
    case 9: return "INVOICE"; // AVRechnung
    default: break; // Unbekannter VorgangsTyp, fallback auf transactionSubType
  }

  // Sekundäres Mapping basierend auf dem transaction_sub_type
  switch (transactionSubType) {
    case 'Anfangsbestand': return "CASH_DEPOSIT";
    case 'Entnahme': return "TRANSFER";
    case 'Abschluss': return "TRANSFER";
    case 'Verkauf': return "RECEIPT";
    case 'Storno': return "CANCELLATION";
    case 'Erstattung': return "REFUND";
    case 'Gutschein': return "VOUCHER";
    case 'Anzahlung': return "DEPOSIT";
    case 'Rechnung': return "INVOICE";
    case 'Training':
    case 'Test': return "TRAINING";
    case 'Pfand': return "CONTAINER_DEPOSIT";
    default: return "RECEIPT"; // Fallback
  }
}

/**
 * Bestimmt den korrekten Process-Type für die Fiskaly API
 */
_determineProcessType(tseEntry) {
  if (!tseEntry) return "Kassenbeleg-V1"; // Default

  const parts = tseEntry.split(',');
  if (parts.length < 1) return "Kassenbeleg-V1";

  const processTypeNumber = parseInt(parts[0], 10) || 1;

  switch (processTypeNumber) {
    case 1: return "Kassenbeleg-V1";
    case 2: return "BestellungV1";
    case 3: return "SonstigerVorgang";
    default: return "Kassenbeleg-V1";
  }
}

/**
 * Bestimmt den korrekten VAT-Rate-Type für die Fiskaly API
 */
_determineVatRateType(taxRate) {
  switch (taxRate) {
    case 19: return "NORMAL";
    case 16: return "NORMAL"; // Temporär während COVID
    case 7: return "REDUCED_1";
    case 5: return "REDUCED_1"; // Temporär während COVID
    case 10.7: return "SPECIAL_RATE_1";
    case 5.5: return "SPECIAL_RATE_2";
    case 0: return "NULL";
    default: return "NULL"; // Fallback
  }
}

/**
 * Erstellt einen validen QR-Code ohne "undefined"-Werte
 */
/**
 * Erstellt einen konformen QR-Code nach KassenSichV-Vorgaben
 */
_createValidQrCode(transactionData, response, storedTransaction, endSignature, endCounter, startTime, endTime) {
  try {
    log.info('Erstelle konformen KassenSichV QR-Code');

    // 1. Version des QR-Codes
    const version = "V0";

    // 2. Seriennummer des elektronischen Aufzeichnungssystems (Client-ID)
    const serialNumber = this.originalClientId || "K001";

    // 3. Vorgangsart (processType)
    const processType = "Kassenbeleg-V1";

    // 4. Vorgangsdaten (im DSFinV-K-Format, NICHT Base64-codiert)
    // Extrahiere korrekte Prozessdaten aus der Transaktion
    let processData = "";
    if (transactionData.dsfin_entry) {
      // Verwende direkt den dsfin_entry wenn vorhanden
      processData = transactionData.dsfin_entry;
      log.info(`Verwende dsfin_entry aus Transaktion: ${processData}`);
    } else if (transactionData.tse_entry) {
      // Konvertiere tse_entry in ein DSFinV-K-Format
      const tseEntryParts = transactionData.tse_entry.split(',');
      if (tseEntryParts.length >= 9) {
        // Format tse_entry in DSFinV-K um
        const betraege = `${tseEntryParts[3]}_${tseEntryParts[4]}_${tseEntryParts[5]}_${tseEntryParts[6]}_${tseEntryParts[7]}`;
        const zahlungsart = `${parseFloat(tseEntryParts[2]).toFixed(2)}:Bar`;
        processData = `Beleg^${betraege}^${zahlungsart}`;
        log.info(`Konvertierter tse_entry zu DSFinV-K: ${processData}`);
      }
    } else {
      // Fallback: Erstelle Prozessdaten aus den verfügbaren Transaktionsdaten
      const totalAmount = parseFloat(transactionData.total_amount || 0) / 100;
      processData = `Beleg^0.00_0.00_0.00_0.00_${totalAmount.toFixed(2)}^${totalAmount.toFixed(2)}:Bar`;
      log.info(`Generierte DSFinV-K Prozessdaten: ${processData}`);
    }

    // 5. Fortlaufende Transaktionsnummer (NICHT die UUID)
    const transactionNumber = response.number || 1;
    log.info(`Verwende fortlaufende Transaktionsnummer: ${transactionNumber}`);

    // 6. Signaturzähler
    const signatureCounter = endCounter;

    // 7 & 8. Start- und Endzeitpunkt (behalte ISO8601-Format bei)
    const startTimestamp = startTime;
    const endTimestamp = endTime;

    // 9. Signaturalgorithmus
    const signatureAlgorithm = "ecdsa-plain-SHA256";

    // 10. Format der Zeitstempel (KORRIGIERT zu unixTime)
    const timestampFormat = "unixTime";

    // 11. Signaturwert
    const signatureValue = endSignature;

    // 12. Öffentlicher Schlüssel der TSE
    let publicKey = "";
    if (response.signature && response.signature.public_key) {
      publicKey = response.signature.public_key;
    } else if (storedTransaction.publicKey) {
      publicKey = storedTransaction.publicKey;
    } else if (response.tss_public_key) {
      publicKey = response.tss_public_key;
    }

    // QR-Code im KassenSichV-Format zusammensetzen
    const qrCode = `${version};${serialNumber};${processType};${processData};${transactionNumber};${signatureCounter};${startTimestamp};${endTimestamp};${signatureAlgorithm};${timestampFormat};${signatureValue};${publicKey}`;

    log.info('Konformer QR-Code im KassenSichV-Format erstellt');
    return qrCode;
  } catch (error) {
    log.error('Fehler bei der QR-Code-Erstellung:', error.message);

    // Fallback: Minimaler QR-Code mit bekannten Daten
    const now = new Date().toISOString();
    const fallbackQRCode = `V0;${this.originalClientId || "K001"};Kassenbeleg-V1;Beleg^0.00_0.00_0.00_0.00_0.00^0.00:Bar;1;1;${now};${now};ecdsa-plain-SHA256;unixTime;dummy-signature;`;

    return fallbackQRCode;
  }
}

/**
 * Eine TSE-Transaktion abbrechen
 */
async cancelTransaction(transactionData = {}) {
  try {
    const transactionId = transactionData.id;

    // Wenn keine Transaktion existiert, einen öffnen
    if (!this.transactions.has(transactionId)) {
      log.info(`Keine offene TSE-Transaktion für ID ${transactionId} gefunden, öffne zuerst eine Transaktion.`);
      try {
        await this.openTransaction(transactionData);
      } catch (openError) {
        log.error(`Fehler beim Öffnen der TSE-Transaktion: ${openError.message}`);
        throw openError;
      }
    }

    if (!this.transactions.has(transactionId)) {
      log.error(`Kritischer Fehler: Konnte keine TSE-Transaktion für ${transactionId} öffnen`);
      throw new Error(`Kritischer Fehler: Keine TSE-Transaktion für ${transactionId} verfügbar`);
    }

    const storedTransaction = this.transactions.get(transactionId);
    log.info('Gespeicherte TSE-Transaktion gefunden:', storedTransaction);

    // Extrahiere wichtige Werte aus der gespeicherten Transaktion
    const startSignature = storedTransaction.signatureValue || "";
    const startCounter = storedTransaction.signatureCounter || 0;
    const initialStartTime = storedTransaction.startTime || new Date().toISOString();

    // Prozessdaten dynamisch vorbereiten
    const totalAmount = this._extractAmount(transactionData);
    const taxSet = this._extractTaxSet(transactionData);
    const payments = this._extractPayments(transactionData);

    // DSFinV-K konforme Daten vorbereiten
    let betraege = "0.00_0.00_0.00_0.00_0.00";
    if (taxSet.length > 0) {
      betraege = taxSet.map(tax => tax.netAmount.toFixed(2)).join('_');
    }

    let zahlungsarten = (payments.length > 0) ?
      payments.map(p => `${p.amount.toFixed(2)}:${p.type}`).join(',') :
      `${totalAmount.toFixed(2)}:Bar`;

    // Original-Transaktionstyp ermitteln
    const parsedProcessData = JSON.parse(storedTransaction.processData);
    const transactionSubType = transactionData.transaction_sub_type ||
                              parsedProcessData.type || "Verkauf";
    const processType = transactionSubType === 'Anfangsbestand' ?
                        'Bestand' : 'Kassenbeleg-V1';

    // DSFinV-K Format erstellen
    const dsfin = `${transactionSubType === 'Anfangsbestand' ? 'Bestand' : 'Beleg'}^${betraege}^${zahlungsarten}`;
    const processDataB64 = Buffer.from(dsfin).toString('base64');

    // tx_revision als Query-Parameter hinzufügen
    const url = `/tss/${this.tssId}/tx/${storedTransaction.fiskalyId}?tx_revision=${newRevision}`;

    // Strukturell gleich zu finishTransaction
    const response = await this.apiRequest('PUT', url, {
      state: 'CANCELLED',
      client_id: this.clientId,
      schema: {
        raw: {
          process_type: processType,
          process_data: processDataB64
        },
        standard_v1: {
          receipt: {
            receipt_type: transactionSubType === 'Anfangsbestand' ? "CASH_DEPOSIT" : "RECEIPT",
            amounts_per_vat_rate: taxSet.map(tax => ({
              vat_rate: tax.taxRate === 19 ? "NORMAL" :
                        tax.taxRate === 7 ? "REDUCED" : "NONE",
              amount: tax.netAmount.toFixed(2)
            })),
            amounts_per_payment_type: payments.map(payment => ({
              payment_type: payment.type.toUpperCase() === "BAR" ? "CASH" : "NON_CASH",
              amount: payment.amount.toFixed(2),
              currency_code: "EUR"
            }))
          }
        }
      }
    });

    // Transaktion aus Map entfernen
    this.transactions.delete(transactionId);

    log.info('Fiskaly TSE-Transaktion erfolgreich abgebrochen:', storedTransaction.fiskalyId);

    // Extrahiere Signatur-Werte aus der Antwort
    let endSignatureValue = "";
    let endSignatureCounter = 0;

    // Signaturwerte extrahieren (wie bei finishTransaction)
    if (response.signature && response.signature.value) {
      endSignatureValue = response.signature.value;
    }

    if (response.signature && response.signature.counter) {
      endSignatureCounter = parseInt(response.signature.counter, 10);
    }

    // ISO-Zeitformat für Endzeitpunkt
    let endTime = response.time_end;
    if (typeof endTime === 'number') {
      endTime = new Date(endTime * 1000).toISOString();
    } else if (!endTime) {
      endTime = new Date().toISOString();
    }

    // Rückgabeobjekt mit allen wichtigen Informationen
    return {
      tenant_id: transactionData.tenant_id,
      cashbox_id: this.originalClientId,
      wizid_transaction_id: transactionData.id,
      tse_transaction_id: response.number,
      tse_opening_timestamp: initialStartTime,
      tse_signatur_start: startSignature,
      tse_signatur_counter: startCounter,
      process_type: 0,
      vorgangs_type: 0,
      tse_serial_number: String(this.tssId),
      tse_signatur_end: endSignatureValue,
      tse_signatur_end_counter: endSignatureCounter,
      tse_signatur_end_timestamp: endTime,
      tse_hash_algorithm: "SHA256",
      tse_public_key: storedTransaction.publicKey || "",
      tse_error: "",
      transaction_type: String(transactionData.transaction_type || "Beleg"),
      transaction_sub_type: String(transactionData.transaction_sub_type || "Verkauf"),
      qr_code: "",
      status: "canceled",
      total_amount: totalAmount,
      tax_set: taxSet,
      payments: payments
    };
  } catch (error) {
    log.error('Fehler beim Abbrechen der Fiskaly TSE-Transaktion:', error.message);
    throw error;
  }
}

  /**
   * QR-Code für Transaktion erstellen
   */
  async createQrCode(transactionData, response, storedTransaction) {
    try {
      const qrCode = `V0;${this.tssId};${storedTransaction.fiskalyId};${storedTransaction.startTime};${response.time_end};${response.log.signature_counter};${response.log.signature_counter};ecdsa-plain-SHA256;unixTime;${response.log.signature};`;
      log.info('QR-Code im DSFinV-K-Format erstellt');
      return qrCode;
    } catch (error) {
      log.error('Fehler bei der QR-Code-Erstellung:', error.message);
      return `V0;${this.tssId};${storedTransaction.fiskalyId};${storedTransaction.startTime || new Date().toISOString()};${response.time_end || new Date().toISOString()};0;0;ecdsa-plain-SHA256;unixTime;;`;
    }
  }

  /**
   * Generiert eine UUID basierend auf der Transaktion
   */
  _generateUuid(transactionId) {
    if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(transactionId)) {
      return transactionId;
    }
    return crypto.randomUUID();
  }

  /**
   * Extrahiert den Gesamtbetrag aus den Transaktionsdaten
   */
  _extractAmount(transactionData) {
    try {
      let amount = 0;
      if (transactionData.total_amount !== undefined) {
        amount = parseFloat(transactionData.total_amount);
        // Verwende Math.abs, um negative Beträge korrekt zu behandeln
        if (Math.abs(amount) > 10000 || typeof transactionData.total_amount === 'string' && !transactionData.total_amount.includes('.')) {
          amount = amount / 100;
        }
      } else if (transactionData.total !== undefined) {
        amount = parseFloat(transactionData.total);
        // Verwende Math.abs, um negative Beträge korrekt zu behandeln
        if (Math.abs(amount) > 10000 || typeof transactionData.total === 'string' && !transactionData.total.includes('.')) {
          amount = amount / 100;
        }
      }
      return parseFloat(amount.toFixed(2));
    } catch (error) {
      log.error('Fehler beim Extrahieren des Betrags:', error.message);
      return 0;
    }
  }

  /**
   * Extrahiert die Steuerinformationen aus den Transaktionsdaten
   */
  _extractTaxSet(transactionData) {
    try {
      // Prüfen, ob ein tse_entry vorhanden ist und diesen für die Steuerberechnung verwenden
      if (transactionData.tse_entry) {
        log.info('Extrahiere Steuerdaten aus tse_entry:', transactionData.tse_entry);

        const taxSet = [];
        const tseEntryParts = transactionData.tse_entry.split(',');

        // Überprüfe, ob das Format korrekt ist
        if (tseEntryParts.length >= 9) {
          // Position 3: MwSt 19% oder 16%
          const mwst1 = parseFloat(tseEntryParts[2] || '0.00');
          if (mwst1 > 0) {
            taxSet.push({
              taxRate: 19, // Standardsatz (könnte auch 16% sein, je nach Zeitraum)
              amount: mwst1 * 0.19,  // Steueranteil berechnen
              netAmount: mwst1
            });
          }

          // Position 4: MwSt 7% oder 5%
          const mwst2 = parseFloat(tseEntryParts[3] || '0.00');
          if (mwst2 > 0) {
            taxSet.push({
              taxRate: 7, // Ermäßigter Satz (könnte auch 5% sein, je nach Zeitraum)
              amount: mwst2 * 0.07,  // Steueranteil berechnen
              netAmount: mwst2
            });
          }

          // Position 5: MwSt Durchschnittssatz 10.7%
          const mwst3 = parseFloat(tseEntryParts[4] || '0.00');
          if (mwst3 > 0) {
            taxSet.push({
              taxRate: 10.7, // Durchschnittssatz nach §24(1)Nr.3 UStG
              amount: mwst3 * 0.107,  // Steueranteil berechnen
              netAmount: mwst3
            });
          }

          // Position 6: MwSt Durchschnittssatz 5.5%
          const mwst4 = parseFloat(tseEntryParts[5] || '0.00');
          if (mwst4 > 0) {
            taxSet.push({
              taxRate: 5.5, // Durchschnittssatz nach §24(1)Nr.3 UStG
              amount: mwst4 * 0.055,  // Steueranteil berechnen
              netAmount: mwst4
            });
          }

          // Position 7: Umsatz ohne MwSt (0%)
          const mwst0 = parseFloat(tseEntryParts[6] || '0.00');
          if (mwst0 !== 0) {
            taxSet.push({
              taxRate: 0,
              amount: 0,
              netAmount: mwst0
            });
          }

          // Wenn bis hier keine Steuerinformationen gefunden wurden,
          // nehmen wir an, dass der Gesamtbetrag ohne MwSt ist
          if (taxSet.length === 0) {
            // Ermittle den Gesamtbetrag als Fallback
            const totalBar = parseFloat(tseEntryParts[7] || '0.00');
            const totalUnbar = parseFloat(tseEntryParts[8] || '0.00');
            const totalAmount = totalBar + totalUnbar;

            taxSet.push({
              taxRate: 0,
              amount: 0,
              netAmount: totalAmount
            });
          }

          log.info('Steuerdaten erfolgreich aus tse_entry extrahiert:', taxSet);
          return taxSet;
        }
      }

      // Fallback auf bestehende Logik, wenn kein tse_entry vorhanden ist
      const taxSet = [];
      if (transactionData.total_vat_19 !== undefined) {
        taxSet.push({
          taxRate: 19,
          amount: parseFloat(transactionData.total_vat_19) / 100,
          netAmount: parseFloat(transactionData.total_net_19 || 0) / 100
        });
      }
      if (transactionData.total_vat_7 !== undefined) {
        taxSet.push({
          taxRate: 7,
          amount: parseFloat(transactionData.total_vat_7) / 100,
          netAmount: parseFloat(transactionData.total_net_7 || 0) / 100
        });
      }
      if (transactionData.total_vat_0 !== undefined || transactionData.total_net_0 !== undefined) {
        taxSet.push({
          taxRate: 0,
          amount: 0,
          netAmount: parseFloat(transactionData.total_net_0 || 0) / 100
        });
      }

      // Wenn keine Steuerinformationen vorhanden sind und es sich um einen Anfangsbestand handelt,
      // dann setzen wir 0% MwSt
      if (taxSet.length === 0) {
        const totalAmount = this._extractAmount(transactionData);

        // Bei Anfangsbestand oder anderen speziellen Transaktionstypen keine MwSt berechnen
        if (transactionData.transaction_sub_type === 'Anfangsbestand' ||
            transactionData.transaction_sub_type === 'Entnahme') {
          taxSet.push({
            taxRate: 0,
            amount: 0,
            netAmount: totalAmount
          });
          log.info('Keine MwSt für Transaktion vom Typ', transactionData.transaction_sub_type);
        } else {
          // Nur reguläre Verkäufe mit Standard-MwSt belasten
          const netAmount = totalAmount / 1.19;
          const vatAmount = totalAmount - netAmount;
          taxSet.push({
            taxRate: 19,
            amount: parseFloat(vatAmount.toFixed(2)),
            netAmount: parseFloat(netAmount.toFixed(2))
          });
        }
      }

      return taxSet;
    } catch (error) {
      log.error('Fehler beim Extrahieren der Steuerinformationen:', error.message);
      // Fallback mit 0% MwSt
      return [{
        taxRate: 0,
        amount: 0,
        netAmount: this._extractAmount(transactionData)
      }];
    }
  }

  /**
   * Extrahiert die Zahlungsinformationen aus den Transaktionsdaten
   */
  _extractPayments(transactionData) {
    try {
      const payments = [];
      const totalAmount = this._extractAmount(transactionData);

      // Wenn ein tse_entry vorhanden ist, extrahiere die Zahlungsdaten daraus
      if (transactionData.tse_entry) {
        const tseEntryParts = transactionData.tse_entry.split(',');

        if (tseEntryParts.length >= 9) {
          const barBetrag = parseFloat(tseEntryParts[7] || '0.00');
          const unbarBetrag = parseFloat(tseEntryParts[8] || '0.00');

          // Füge Barzahlung hinzu, wenn vorhanden (auch für negative Beträge)
          if (barBetrag !== 0) {
            payments.push({
              type: "bar",
              amount: barBetrag,
              name: "Bargeld"
            });
          }

          // Füge unbare Zahlung hinzu, wenn vorhanden
          if (unbarBetrag !== 0) {
            payments.push({
              type: "unbar",
              amount: unbarBetrag,
              name: "Kartenzahlung"
            });
          }

          // Wenn beide Werte 0 sind, aber die Gesamtsumme nicht 0 ist,
          // dann verwende die Gesamtsumme als Barzahlung
          if (barBetrag === 0 && unbarBetrag === 0 && totalAmount !== 0) {
            payments.push({
              type: "bar",
              amount: totalAmount,
              name: "Bargeld"
            });
          }

          log.info('Zahlungsdaten erfolgreich aus tse_entry extrahiert');
          if (payments.length > 0) {
            return payments;
          }
        }
      }

      // Bestehende Logik als Fallback
      if (transactionData.payments && transactionData.payments.length > 0) {
        transactionData.payments.forEach(payment => {
          let amount = parseFloat(payment.amount || 0);
          if (amount > 10000 || typeof payment.amount === 'string' && !payment.amount.includes('.')) {
            amount = amount / 100;
          }
          payments.push({
            type: payment.type.toLowerCase(),
            amount: parseFloat(amount.toFixed(2)),
            name: payment.name || payment.type
          });
        });
      } else {
        payments.push({
          type: "bar",
          amount: totalAmount,
          name: "Bargeld"
        });
      }

      return payments;
    } catch (error) {
      log.error('Fehler beim Extrahieren der Zahlungsinformationen:', error.message);
      return [{
        type: "bar",
        amount: this._extractAmount(transactionData),
        name: "Bargeld"
      }];
    }
  }


  /**
   * Aktualisiert die ProcessData für den Transaktionsabschluss
   */
  _updateProcessData(transactionData, storedTransaction) {
    try {
      const processData = JSON.parse(storedTransaction.processData);
      processData.amount = this._extractAmount(transactionData);
      processData.taxes = this._extractTaxSet(transactionData);
      processData.payments = this._extractPayments(transactionData);
      return JSON.stringify(processData);
    } catch (error) {
      log.error('Fehler beim Aktualisieren der ProcessData:', error.message);
      return storedTransaction.processData;
    }
  }

  /**
   * Erstellt benutzerdefinierte Daten für die ProcessData
   */
  _getCustomData(transactionData) {
    const customData = {};
    if (transactionData.tenant_id) customData.tenant_id = transactionData.tenant_id;
    if (transactionData.subject_id) customData.subject_id = transactionData.subject_id;
    if (transactionData.cash_register_id) customData.cash_register_id = transactionData.cash_register_id;
    return customData;
  }
}

module.exports = FiskalyClient;