<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Kundendisplay</title>
  <style>
    body, html {
      margin: 0;
      padding: 0;
      height: 100%;
      width: 100%;
      font-family: NunitoSansWF, sans-serif;
      overflow: hidden;
      background-color: #f0f0f0;
    }
    
    .container {
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
    }
    
    .header {
      background-color: #475569;
      color: #78FA8D;
      padding: 18px;
      text-align: center;
      font-size: 2.5em;
      font-weight: regular;
    }
    
    .content {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 20px;
      overflow-y: auto;
    }
    
    .cart-items {
      flex: 1;
      overflow-y: auto;
    }
    
    .cart-item {
      display: flex;
      justify-content: space-between;
      padding: 15px 10px;
      border-bottom: 1px solid #ddd;
      font-size: 1.8em;
    }
    
    .item-info {
      flex: 1;
    }
    
    .item-name {
      font-weight: bold;
    }
    
    .item-price {
      margin-left: 26px;
      font-weight: bold;
      min-width: 120px;
      text-align: right;
    }
    
    .footer {
      background-color: #475569;
      color: white;
      padding: 20px;
      font-size: 2.2em;
      display: flex;
      justify-content: space-between;
    }
    
    .total-label {
      font-weight: bold;
    }
    
    .total-amount {
      color: #78FA8D;
      font-weight: bold;
    }
    
    .empty-cart {
      text-align: center;
      font-size: 2em;
      color: #666;
      margin-top: 100px;
    }
    
    .quantity {
      margin-right: 15px;
      background-color: #475569;
      color: white;
      border-radius: 50%;
      min-width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8em;
    }
    
    .item-row {
      display: flex;
      align-items: center;
    }
    
    .item-details {
      font-size: 0.7em;
      color: #666;
      margin-top: 5px;
    }

    /* Debug-Bereich für Logging */
    .debug-area {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: rgba(0, 0, 0, 0.7);
      color: #fff;
      padding: 5px;
      font-size: 12px;
      max-height: 150px;
      overflow-y: auto;
      display: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      IHR WARENKORB
    </div>
    <div class="content">
      <div class="cart-items" id="cart-items">
        <div class="empty-cart" id="empty-cart">
          Der Warenkorb ist leer
        </div>
        <!-- Hier werden die Warenkorb-Elemente dynamisch eingefügt -->
      </div>
    </div>
    <div class="footer">
      <div class="total-label">Gesamtsumme:</div>
      <div class="total-amount" id="total-amount">0,00 €</div>
    </div>
  </div>
  
  <!-- Debug-Bereich -->
  <div class="debug-area" id="debug-area"></div>

  <script>
    // Debugging-Hilfsfunktion
    function debugLog(message, obj) {
      const debugArea = document.getElementById('debug-area');
      
      // Nur im Debug-Modus anzeigen (kann durch Tastenkombination aktiviert werden)
      if (debugArea.style.display === 'block') {
        const timestamp = new Date().toLocaleTimeString();
        let text = `[${timestamp}] ${message}`;
        
        if (obj) {
          try {
            text += ': ' + JSON.stringify(obj);
          } catch (e) {
            text += ': [Nicht serialisierbares Objekt]';
          }
        }
        
        const logEntry = document.createElement('div');
        logEntry.textContent = text;
        debugArea.appendChild(logEntry);
        
        // Scrollt zum neuesten Eintrag
        debugArea.scrollTop = debugArea.scrollHeight;
      }
    }

    // Aktiviert Debug-Modus mit Tastenkombination (Strg+Alt+D)
    document.addEventListener('keydown', function(event) {
      if (event.ctrlKey && event.altKey && event.key === 'd') {
        const debugArea = document.getElementById('debug-area');
        if (debugArea.style.display === 'block') {
          debugArea.style.display = 'none';
        } else {
          debugArea.style.display = 'block';
          debugLog('Debug-Modus aktiviert');
        }
      }
    });

    // Kommunikation mit dem Hauptprozess
    const { ipcRenderer } = require('electron');
    
    // Event-Listener für Warenkorb-Updates
    ipcRenderer.on('cart-update', (event, cartData) => {
      debugLog('Cart-Update empfangen', cartData);
      updateCartDisplay(cartData);
    });
    
    // Bereinigt Text von problematischen Zeichen
    function cleanText(text) {
      if (!text) return '';
      
      // Ersetze bekannte problematische Zeichen
      return text
        .replace(/┬á/g, ' ')        // Ersetze seltsame Leerzeichen
        .replace(/Ôé¼/g, '€')       // Korrigiere Euro-Symbol
        .replace(/Â/g, '')          // Entferne überflüssige Zeichen
        .normalize('NFC');          // Normalisiere Unicode-Zeichen
    }
    
    // Funktion zum Aktualisieren der Anzeige
    function updateCartDisplay(cartData) {
      debugLog('Aktualisiere Display mit Daten', cartData);
      
      const cartItemsContainer = document.getElementById('cart-items');
      const emptyCartMessage = document.getElementById('empty-cart');
      const totalAmountElement = document.getElementById('total-amount');
      
      if (!cartItemsContainer) {
        debugLog('FEHLER: cart-items Container nicht gefunden!');
        return;
      }
      
      // Warenkorb leeren
      cartItemsContainer.innerHTML = '';
      
      // Wenn der Warenkorb leer ist, Nachricht anzeigen
      if (!cartData || !cartData.items || cartData.items.length === 0) {
        debugLog('Warenkorb ist leer oder ungültig');
        if (emptyCartMessage) emptyCartMessage.style.display = 'block';
        if (totalAmountElement) totalAmountElement.textContent = '0,00 €';
        return;
      }
      
      // Im Checkout-Modus?
      const isCheckoutMode = cartData.isCheckout === true;
      debugLog(isCheckoutMode ? 'Checkout-Modus erkannt' : 'Standard-Shop-Modus');
      
      // Leere-Warenkorb-Nachricht ausblenden
      if (emptyCartMessage) emptyCartMessage.style.display = 'none';
      
      // Für jede Position im Log dokumentieren
      debugLog(`Erstelle ${cartData.items.length} Warenkorbpositionen`);
      
      // Warenkorb-Elemente anzeigen
      cartData.items.forEach((item, index) => {
        try {
          debugLog(`Verarbeite Position ${index}`, item);
          
          const cartItemElement = document.createElement('div');
          cartItemElement.className = 'cart-item';
          
          // Bereinige Texte von Kodierungsproblemen
          const name = cleanText(item.name);
          const price = cleanText(item.price);
          const details = cleanText(item.details);
          
          let detailsHtml = '';
          if (details) {
            detailsHtml = `<div class="item-details">${details}</div>`;
          }
          const quantityHtml = (item.quantity < 0) ? 
            `<div class="quantity">${item.quantity}</div>` : '';
          
          cartItemElement.innerHTML = `
            <div class="item-row">
              ${quantityHtml}
              <div class="item-info">
                <div class="item-name">${name}</div>
                ${detailsHtml}
              </div>
            </div>
            <div class="item-price">${price}</div>
          `;
          
          cartItemsContainer.appendChild(cartItemElement);
          debugLog(`Position ${index} hinzugefügt:`, { name, price, details });
        } catch (error) {
          debugLog(`Fehler bei Position ${index}:`, error.message);
        }
      });
      
      // Gesamtsumme aktualisieren
      if (totalAmountElement) {
        totalAmountElement.textContent = cleanText(cartData.totalAmount || '0,00 €');
        debugLog('Gesamtsumme aktualisiert:', totalAmountElement.textContent);
      }
    }
    
    // Bei Initialisierung Debug-Info ausgeben
    document.addEventListener('DOMContentLoaded', () => {
      debugLog('Kundendisplay initialisiert und bereit');
    });
  </script>
</body>
</html>