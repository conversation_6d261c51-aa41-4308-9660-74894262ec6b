// Zentraler Logging-Service für die gesamte Anwendung
const log = require('electron-log');
const path = require('path');
const fs = require('fs');
const axios = require('axios');

class LoggerService {
  constructor() {
    this.isConfigured = false;
    this.defaultConfig = {
      enabled: true,
      logToConsole: true,
      logToFile: true,
      logLevel: 'info',
      maxLogFileSize: 10 * 1024 * 1024, // 10 MB
      maxLogFiles: 5,
      webhookEnabled: false,
      webhookUrl: '',
      webhookSenderName: 'POS Fehlerprotokoll'
    };

    this.apiConfig = null;

    // Standardkonfiguration anwenden
    this.configure(this.defaultConfig);
  }

  /**
   * Konfiguriert den Logger mit den angegebenen Einstellungen
   * @param {Object} config Logging-Konfiguration
   */
  configure(config = {}) {
    // Konfiguration mit Standardwerten zusammenführen
    const loggingConfig = { ...this.defaultConfig, ...config };

    const {
      enabled = true,
      logToConsole = true,
      logToFile = true,
      logLevel = 'info',
      maxLogFileSize = 10 * 1024 * 1024,
      maxLogFiles = 5,
      webhookEnabled = false,
      webhookUrl = '',
      webhookSenderName = 'POS Fehlerprotokoll'
    } = loggingConfig;

    // Elektronisches Log-Level setzen
    log.transports.file.level = enabled && logToFile ? logLevel : false;
    log.transports.console.level = enabled && logToConsole ? logLevel : false;

    // Wenn Logging deaktiviert ist, alle Transports deaktivieren
    if (!enabled) {
      log.transports.file.level = false;
      log.transports.console.level = false;
      console.log('Logging ist vollständig deaktiviert');
      return;
    }

    // Maximale Dateigröße und Rotation konfigurieren
    if (logToFile) {
      log.transports.file.maxSize = maxLogFileSize;
      log.transports.file.maxFiles = maxLogFiles;
    }

    // Webhook-Konfiguration speichern
    this.webhookEnabled = webhookEnabled;
    this.webhookUrl = webhookUrl;
    this.webhookSenderName = webhookSenderName;

    // Wenn Webhook aktiviert ist, überschreibe die error-Methode, um Logs an den Webhook zu senden
    if (webhookEnabled && webhookUrl) {
      const originalError = log.error;
      log.error = (...args) => {
        // Original-Logging-Funktion aufrufen
        originalError(...args);

        // Zusätzlich an Webhook senden
        this.sendErrorToWebhook(...args);
      };
    }

    this.isConfigured = true;
    this.currentConfig = loggingConfig;

    // Logging-Informationen ausgeben
    if (logToFile) {
      const logFilePath = log.transports.file.getFile().path;
      log.info('Datei-Logging konfiguriert:', {
        level: logLevel,
        maxSize: `${Math.round(maxLogFileSize / 1024 / 1024)} MB`,
        maxFiles: maxLogFiles,
        path: logFilePath
      });
    }
  }

  /**
   * Gibt den konfigurierten Logger zurück (electron-log)
   * @returns {Object} Der konfigurierte electron-log
   */
  getLogger() {
    return log;
  }

  /**
   * Erstellt einen benannten Logger für ein bestimmtes Modul
   * @param {string} moduleName Name des Moduls
   * @returns {Object} Logger-Objekt mit zusätzlichem Modulnamen
   */
  getModuleLogger(moduleName) {
    // Erstellt eine Wrapper-Funktion um den Standard-Logger
    const moduleLogger = {
      info: (message, ...args) => log.info(`[${moduleName}]`, message, ...args),
      warn: (message, ...args) => log.warn(`[${moduleName}]`, message, ...args),
      error: (message, ...args) => log.error(`[${moduleName}]`, message, ...args),
      debug: (message, ...args) => log.debug(`[${moduleName}]`, message, ...args),
      verbose: (message, ...args) => log.verbose(`[${moduleName}]`, message, ...args),
      silly: (message, ...args) => log.silly(`[${moduleName}]`, message, ...args),
      log: (level, message, ...args) => log.log(level, `[${moduleName}]`, message, ...args)
    };

    return moduleLogger;
  }

  /**
   * Bereinigt alte Log-Dateien, die älter als das angegebene Alter sind
   * @param {number} maxAgeDays Maximales Alter der Log-Dateien in Tagen
   */
  cleanupOldLogs(maxAgeDays = 30) {
    try {
      if (!this.currentConfig.logToFile || !this.currentConfig.enabled) {
        return; // Keine Bereinigung, wenn Datei-Logging deaktiviert ist
      }

      const logDir = path.dirname(log.transports.file.getFile().path);
      const files = fs.readdirSync(logDir);
      const now = new Date();

      for (const file of files) {
        if (file.endsWith('.log')) {
          const filePath = path.join(logDir, file);
          const stats = fs.statSync(filePath);
          const fileAgeDays = (now - stats.mtime) / (1000 * 60 * 60 * 24);

          if (fileAgeDays > maxAgeDays) {
            fs.unlinkSync(filePath);
            console.log(`Alte Log-Datei gelöscht: ${file} (${Math.round(fileAgeDays)} Tage alt)`);
          }
        }
      }

      log.info(`Log-Bereinigung abgeschlossen: Dateien älter als ${maxAgeDays} Tage wurden entfernt`);
    } catch (error) {
      console.error('Fehler bei der Log-Bereinigung:', error);
    }
  }

  /**
   * Setzt die API-Konfiguration für den Logger
   * @param {Object} apiConfig Die API-Konfiguration
   */
  setApiConfig(apiConfig) {
    this.apiConfig = apiConfig;
  }

  /**
   * Sendet eine Fehlermeldung an den konfigurierten Microsoft Teams Webhook
   * @param  {...any} args Die Argumente der error-Methode
   */
  async sendErrorToWebhook(...args) {
    try {
      if (!this.webhookEnabled || !this.webhookUrl) {
        return;
      }

      // Extrahiere die Fehlermeldung aus den Argumenten
      let errorMessage = args.map(arg => {
        if (arg instanceof Error) {
          return arg.stack || arg.message;
        } else if (typeof arg === 'object') {
          try {
            return JSON.stringify(arg);
          } catch (e) {
            return String(arg);
          }
        }
        return String(arg);
      }).join(' ');

      // Extrahiere tenant_id und client_id aus der API-Konfiguration
      let tenantId = 'unbekannt';
      let clientId = 'unbekannt';

      if (this.apiConfig) {
        if (this.apiConfig.system_config && this.apiConfig.system_config.tenant) {
          tenantId = this.apiConfig.system_config.tenant;
        }

        if (this.apiConfig.tse_config && this.apiConfig.tse_config.client_id) {
          clientId = this.apiConfig.tse_config.client_id;
        }
      }

      // Formatiere die Nachricht für Microsoft Teams mit Adaptive Cards
      const senderName = this.webhookSenderName || "POS Fehlerprotokoll";
      const message = {
        type: "message",
        summary: senderName,
        title: senderName,
        attachments: [
          {
            contentType: "application/vnd.microsoft.card.adaptive",
            contentUrl: null,
            content: {
              "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
              "type": "AdaptiveCard",
              "version": "1.2",
              "msteams": {
                "width": "full"
              },
              "body": [
                {
                  "type": "TextBlock",
                  "size": "medium",
                  "weight": "bolder",
                  "text": senderName,
                  "color": "attention"
                },
                {
                  "type": "FactSet",
                  "facts": [
                    {
                      "title": "Kunde:",
                      "value": tenantId
                    },
                    {
                      "title": "Kasse:",
                      "value": clientId
                    },
                    {
                      "title": "Zeitpunkt:",
                      "value": new Date().toLocaleString('de-DE')
                    }
                  ]
                },
                {
                  "type": "TextBlock",
                  "text": "Fehlermeldung:",
                  "weight": "bolder",
                  "wrap": true
                },
                {
                  "type": "TextBlock",
                  "text": errorMessage,
                  "wrap": true
                }
              ]
            }
          }
        ]
      };

      // Sende die Nachricht an den Webhook
      await axios.post(this.webhookUrl, message, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 5000 // 5 Sekunden Timeout
      });

      console.log('Fehlermeldung erfolgreich an Webhook gesendet');
    } catch (error) {
      console.error('Fehler beim Senden an Webhook:', error.message);
    }
  }
}

// Singleton-Instanz erstellen
const loggerService = new LoggerService();

// Exportiere den Logger-Service
module.exports = loggerService;