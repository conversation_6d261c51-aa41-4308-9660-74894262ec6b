if (typeof window === "undefined") {
  global.window = {};
}
if (!window.XMLHttpRequest) {
  window.XMLHttpRequest = require("xhr2");
}

const {
  app,
  BrowserWindow,
  ipcMain,
  BrowserView,
  Menu,
  dialog,
  shell,
  session
} = require("electron");
const path = require("path");
const fs = require("fs");
const https = require("https");
const { exec, spawn } = require("child_process");
const { autoUpdater } = require("electron-updater");
const loggerService = require("./logger-service");
const log = loggerService.getLogger();
const EpsonPrinter = require("./epson-printer");
const ImprovedMQTTClient = require("./mqtt-client");
const FiskalyClient = require("./fiskaly-client");
const ZVTClient = require("./zvt-client");
const cartParser = require("./cart-parser");
const TseDatabase = require("./tse-database");

autoUpdater.setFeedURL({
  provider: 'github',
  owner: 'programmierbude',
  repo: 'pos-rahmenanwendung-electron',
  private: true,
  token: '****************************************'
});

// Auto-Updater Konfiguration
autoUpdater.logger = log;
autoUpdater.logger.transports.file.level = 'debug';
// Die autoDownload und autoInstallOnAppQuit Einstellungen werden später basierend auf der Konfiguration gesetzt
autoUpdater.signature = null;

// --- Funktion zum Ermitteln des Pfads zur Tastatur-EXE (wie zuvor) ---
function getKeyboardExePath() {
  // Priorität 1: Pfad aus config.json (absoluter Pfad)
  const configPath = config.keyboard?.executablePath;
  if (configPath && fs.existsSync(configPath)) {
      log.info(`Tastatur-EXE über config.keyboard.executablePath gefunden: ${configPath}`);
      return configPath;
  }

  // Priorität 2: Pfad aus config.json (relativ zu resources) ODER Standardwert
  // Passe den Standardwert hier genau an deinen Ordner und Dateinamen an:
  const resourceSubPath = config.keyboard?.resourcePath || 'Keyboard/OnScreenKeyboard.exe'; // <-- ANGEPASST

  const resourcePath = app.isPackaged
      ? path.join(process.resourcesPath, resourceSubPath) // Installierte App (sucht in resources/Keyboard/OnScreenKeyboard.exe)
      : path.join(__dirname, 'resources', resourceSubPath); // Entwicklung (sucht in YourProject/resources/Keyboard/OnScreenKeyboard.exe)

  if (fs.existsSync(resourcePath)) {
      log.info(`Tastatur-EXE über Ressourcenpfad gefunden: ${resourcePath}`);
      return resourcePath;
  }

  log.error(`Pfad zur Bildschirmtastatur konnte nicht ermittelt werden. Geprüft: Config->executablePath, Config->resourcePath, Standard->'${resourceSubPath}'`);
  dialog.showErrorBox('Fehler', `Die Bildschirmtastatur-Datei wurde nicht gefunden unter: ${resourcePath}`); // Zusätzliche Info für den User
  return null;
}

// --- Stelle sicher, dass launchOnScreenKeyboard diese Funktion verwendet ---
function launchOnScreenKeyboard() {
const keyboardExePath = getKeyboardExePath();
if (!keyboardExePath) {
  // Fehler wurde schon in getKeyboardExePath geloggt/angezeigt
  return;
}
// ... (Rest der launchOnScreenKeyboard Funktion bleibt gleich) ...
log.info(`Versuche Tastatur zu starten: ${keyboardExePath}`);
try {
    const keyboardProcess = spawn(keyboardExePath, [], {
        detached: true,
        stdio: 'ignore'
    });
    keyboardProcess.unref();
    keyboardProcess.on('error', (err) => {
        log.error(`Fehler beim Starten der Tastatur-EXE: ${err.message}`);
        dialog.showErrorBox('Fehler Tastatur', `Konnte die Bildschirmtastatur nicht starten: ${err.message}`);
    });
    log.info('Bildschirmtastatur-Prozess gestartet.');
} catch (error) {
    log.error(`Fehler beim Ausführen von spawn für Tastatur: ${error.message}`);
    dialog.showErrorBox('Fehler Tastatur', `Fehler beim Ausführen der Bildschirmtastatur: ${error.message}`);
}
}

// Funktion zum Prüfen auf Updates
function checkForUpdates() {
  // Prüfe, ob automatische Updates aktiviert sind
  const autoUpdateEnabled = config.autoUpdate !== false;

  // Konfiguriere den autoUpdater basierend auf der Konfiguration
  autoUpdater.autoDownload = autoUpdateEnabled;
  autoUpdater.autoInstallOnAppQuit = autoUpdateEnabled;

  if (app.isPackaged) {
    if (autoUpdateEnabled) {
      log.info('Automatische Updates sind aktiviert - Prüfe auf Updates...');
      autoUpdater.checkForUpdatesAndNotify().catch(err => {
        log.error('Fehler bei der Update-Prüfung:', err);
      });
    } else {
      log.info('Automatische Updates sind deaktiviert - Update-Prüfung übersprungen');
    }
  } else {
    log.info('Im Entwicklungsmodus - Update-Prüfung übersprungen');
  }
}

// Auto-Updater Event-Handler einrichten
function setupAutoUpdater() {
  // Prüfe, ob automatische Updates aktiviert sind
  const autoUpdateEnabled = config.autoUpdate !== false;

  // Konfiguriere den autoUpdater basierend auf der Konfiguration
  autoUpdater.autoDownload = autoUpdateEnabled;
  autoUpdater.autoInstallOnAppQuit = autoUpdateEnabled;

  autoUpdater.on('checking-for-update', () => {
    log.info('Prüfe auf Updates...');
  });

  autoUpdater.on('update-not-available', () => {
    log.info('Keine Updates verfügbar');
  });

  autoUpdater.on('update-available', (info) => {
    log.info('Update verfügbar', info);

    // Benutzer über verfügbares Update informieren
    if (mainWindow && !mainWindow.isDestroyed()) {
      if (autoUpdateEnabled) {
        dialog.showMessageBox(mainWindow, {
          type: 'info',
          title: 'Update verfügbar',
          message: `Eine neue Version (${info.version}) ist verfügbar und wird heruntergeladen.`,
          buttons: ['OK']
        });
      } else {
        // Wenn automatische Updates deaktiviert sind, frage den Benutzer, ob er das Update herunterladen möchte
        dialog.showMessageBox(mainWindow, {
          type: 'info',
          title: 'Update verfügbar',
          message: `Eine neue Version (${info.version}) ist verfügbar. Möchten Sie das Update herunterladen?`,
          buttons: ['Ja', 'Nein'],
          defaultId: 0
        }).then(result => {
          if (result.response === 0) {
            // Benutzer möchte das Update herunterladen
            log.info('Benutzer hat manuelles Update angefordert');
            autoUpdater.downloadUpdate().catch(err => {
              log.error('Fehler beim manuellen Download des Updates:', err);
              dialog.showErrorBox('Update-Fehler', `Fehler beim Herunterladen des Updates: ${err.message}`);
            });
          }
        });
      }
    }
  });

  autoUpdater.on('download-progress', (progressObj) => {
    log.info(`Download-Fortschritt: ${progressObj.percent.toFixed(2)}%`);

    // Optional: Fortschritt im Hauptfenster anzeigen
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('update-progress', progressObj);
    }
  });

  autoUpdater.on('update-downloaded', (info) => {
    log.info('Update heruntergeladen', info);

    // Benutzer über Installation informieren
    if (mainWindow && !mainWindow.isDestroyed()) {
      dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'Update bereit',
        message: `Version ${info.version} wurde heruntergeladen und wird beim nächsten Neustart installiert.`,
        buttons: ['Jetzt neu starten', 'Später'],
        defaultId: 0
      }).then(result => {
        if (result.response === 0) {
          autoUpdater.quitAndInstall(false, true);
        }
      });
    }
  });

  autoUpdater.on('error', (err) => {
    log.error('Fehler beim Auto-Update:', err);

    // Bei Entwicklungsversionen oder wenn der Benutzer manuell ein Update angefordert hat, Fehler anzeigen
    if (!app.isPackaged || !autoUpdateEnabled) {
      dialog.showErrorBox('Update-Fehler', err.message);
    }
  });
}

let componentsStatus = {
  config: { status: "pending", required: true },
  display: { status: "pending", required: false },
  tse: { status: "pending", required: true },
  payment: { status: "pending", required: false },
  printer: { status: "pending", required: true }, // Drucker als erforderlich markieren
  mqtt: { status: "pending", required: true },
  cart: { status: "pending", required: false },
};

// Variable für das Lade-Fenster
let loadingWindow = null;

// Variable für das Modal-Fenster
let paymentModalWindow = null;

//  Variable für das Kundendisplay
let customerDisplayWindow = null;

// Variablen für das Passwortfenster
let passwordWindow = null;
let pendingConfigAction = null;

// Variable für das Passwortänderungsfenster
let changePasswordWindow = null;

function getConfigPath() {
  return path.join(app.getPath('userData'), "config.json");
}

function getBackupPath() {
  return path.join(app.getPath('userData'), `config-backup-${Date.now()}.json`);
}

// Config Datei laden
let config;
try {
  // Verwende app.getPath('userData') für einen beschreibbaren Speicherort
  const userDataPath = app.getPath('userData');
  const userConfigPath = path.join(userDataPath, "config.json");
  const appConfigPath = path.join(__dirname, "config.json");

  // Zuerst im userData-Verzeichnis suchen
  if (fs.existsSync(userConfigPath)) {
    config = JSON.parse(fs.readFileSync(userConfigPath, "utf8"));
    log.info("Konfiguration aus Benutzerverzeichnis geladen");
  } else {
    // Fallback zur Originaldatei im Anwendungsverzeichnis
    config = JSON.parse(fs.readFileSync(appConfigPath, "utf8"));
    log.info("Konfiguration aus Anwendungsverzeichnis geladen");

    // Kopiere die Konfiguration ins userData-Verzeichnis für künftige Änderungen
    try {
      fs.writeFileSync(userConfigPath, JSON.stringify(config, null, 2), "utf8");
      log.info("Konfiguration ins Benutzerverzeichnis kopiert");
    } catch (copyError) {
      log.warn("Konnte Konfiguration nicht ins Benutzerverzeichnis kopieren:", copyError);
    }
  }

  // Logging konfigurieren
  configureLogging(config);
  log.info("Lokale Konfiguration geladen");
} catch (error) {
  // Temporäres Logging für den Fall, dass die Konfiguration nicht geladen werden kann
  log.error("Config Datei konnte nicht geladen werden:", error);
  config = {
    api: {
      configUrl: "https://api.dev-wizid.com/pos-manage/config",
      authKey: "ItY5wqVso-XUkjHPlgB0xPgR9tpHcGNaTytijqA3lTzntD93",
    },
    windowSettings: {
      width: 1024,
      height: 768,
      fullscreen: false,
      kiosk: false,
      icon: path.join(__dirname, "resources/favicon.ico"),
    },
    fallbackUrl: "https://example-shop.com",
    mqtt: {
      broker: "wss://iot-wss.wizid.com:443/mqtt",
      options: {
        clientId: "shop-receiver-" + Math.random().toString(16).substring(2, 8),
        username: "",
        password: "",
      },
    },
    // Fiskaly-Konfiguration
    fiskaly_config: {
      api_url: "https://kassensichv-middleware.fiskaly.com/api/v2",
      api_key: "YOUR_FISKALY_API_KEY",
      api_secret: "YOUR_FISKALY_API_SECRET",
      tss_id: "YOUR_FISKALY_TSS_ID",
      client_id: "K001",
    },
    // Standard-Logging-Konfiguration
    logging: {
      enabled: true,
      logToConsole: true,
      logToFile: true,
      logLevel: "info",
    },
  };

  // Versuche die Standardkonfiguration im Benutzerverzeichnis zu speichern
  try {
    const userDataPath = app.getPath('userData');
    const userConfigPath = path.join(userDataPath, "config.json");
    fs.writeFileSync(userConfigPath, JSON.stringify(config, null, 2), "utf8");
    log.info("Standardkonfiguration im Benutzerverzeichnis gespeichert");
  } catch (saveError) {
    log.warn("Konnte Standardkonfiguration nicht speichern:", saveError);
  }

  // Logging mit der Standard-Konfiguration konfigurieren
  configureLogging(config);
}

// Globale Variable für den EasyTSE-Service-Prozess
let easyTseProcess = null;

// Funktion zum Starten des EasyTseService
function startEasyTseService() {
  return new Promise((resolve, reject) => {
    try {
      const exeName = "EasyTseService.exe";
      const exeName2 = "EasyTseService2.exe";
      const exePath = app.isPackaged
        ? path.join(process.resourcesPath, "EasyTseService", exeName)
        : path.join(__dirname, "resources", "EasyTseService", exeName);

      log.info(`Versuche EasyTseService aus folgendem Pfad zu starten: ${exePath}`);

      if (!fs.existsSync(exePath)) {
        const errMsg = `${exeName} wurde nicht gefunden: ${exePath}`;
        log.error(errMsg);
        dialog.showErrorBox("Fehler EasyTSE-Service", `Die Datei ${exeName} wurde nicht gefunden: ${exePath}`);
        if (componentsStatus.tse.required) { // componentsStatus muss im Scope sein
          updateComponentStatus("tse", "error", `${exeName} nicht gefunden`);
        }
        return reject(new Error(errMsg));
      }

      const killCommand = process.platform === 'win32' ? `taskkill /F /IM ${exeName2}` : `pkill -f ${exeName2}`;
      log.info(`Versuche, laufende Instanzen von ${exeName} zu beenden mit: ${killCommand}`);

      exec(killCommand, (error, stdout, stderr) => {
        if (error && !error.message.includes('nicht gefunden') && !error.message.includes('not found') && !error.message.includes('No running instances')) {
          log.warn(`Fehler beim Versuch, ${exeName} zu beenden: ${error.message}`);
          if (stderr) log.warn(`Fehler-Output beim Beenden: ${stderr.trim()}`);
        } else {
          if (stdout) log.info(`Output vom Beenden-Befehl für ${exeName}: ${stdout.trim()}`);
          log.info(`Vorherige Instanzen von ${exeName} sollten beendet sein oder waren nicht aktiv.`);
        }

        setTimeout(() => {
          log.info(`Starte neue Instanz von ${exeName} aus: ${exePath}`);
          easyTseProcess = spawn(exePath, [], { detached: false, windowsHide: true });

          let serviceStartedSuccessfully = false;
          const serviceStartTimeout = setTimeout(() => {
            if (!serviceStartedSuccessfully && easyTseProcess) {
              log.error(`${exeName} hat nicht innerhalb des Zeitlimits (10s) erfolgreich gestartet oder geantwortet.`);
              if (componentsStatus.tse.required) {
                updateComponentStatus("tse", "error", `${exeName} Start-Timeout`);
              }
              try { easyTseProcess.kill(); } catch (killError) { log.warn(`Fehler beim Killen des ${exeName} nach Timeout: ${killError.message}`); }
              reject(new Error(`${exeName} Start-Timeout`));
            }
          }, 10000); // 10 Sekunden Timeout für den Start des Dienstes

          easyTseProcess.stdout.on("data", (data) => {
            const output = data.toString().trim();
            log.info(`EasyTseService: ${output}`);
            // Hier könnte eine spezifischere Prüfung auf eine "Ready"-Nachricht erfolgen.
            // Z.B. if (output.includes("Service listening on port 8765"))
            if (!serviceStartedSuccessfully) {
              serviceStartedSuccessfully = true;
              clearTimeout(serviceStartTimeout);
              log.info(`${exeName} stdout-Daten empfangen, Annahme: erfolgreich gestartet.`);
              resolve(true);
            }
          });

          easyTseProcess.stderr.on("data", (data) => {
            const errorMessageText = data.toString().trim();
            log.error(`EasyTseService Fehler: ${errorMessageText}`);
            if (!serviceStartedSuccessfully && (errorMessageText.includes("address already in use") || errorMessageText.includes("EADDRINUSE"))) {
              log.error(`${exeName} konnte nicht starten, da der Port (vermutlich 8765) bereits verwendet wird.`);
              if (componentsStatus.tse.required) {
                updateComponentStatus("tse", "error", "EasyTSE-Port bereits belegt");
              }
              dialog.showErrorBox("Fehler EasyTSE-Service", `Der ${exeName} konnte nicht gestartet werden, da der Port (vermutlich 8765) bereits verwendet wird. Bitte beenden Sie alle laufenden Instanzen von ${exeName} manuell (z.B. über den Task-Manager) und starten Sie die Anwendung neu.`);
              clearTimeout(serviceStartTimeout);
              reject(new Error("EasyTSE-Port bereits belegt"));
            }
            // Andere Fehler könnten hier ebenfalls zum reject führen, wenn sie den Start verhindern
          });

          easyTseProcess.on("close", (code) => {
            log.info(`${exeName} beendet mit Code: ${code}`);
            if (!serviceStartedSuccessfully && code !== 0 && code !== null) {
              const closeErrorMsg = `${exeName} unerwartet beendet mit Code ${code} bevor erfolgreicher Start bestätigt wurde.`;
              log.error(closeErrorMsg);
              clearTimeout(serviceStartTimeout);
              if (componentsStatus.tse.required) {
                 updateComponentStatus("tse", "error", `EasyTSE Service Fehler (Code: ${code})`);
              }
              reject(new Error(closeErrorMsg));
            }
            easyTseProcess = null;
          });

          easyTseProcess.on('error', (spawnError) => {
            const spawnErrorMsg = `Fehler beim Spawnen von ${exeName}: ${spawnError.message}`;
            log.error(spawnErrorMsg);
            clearTimeout(serviceStartTimeout);
            if (componentsStatus.tse.required) {
                 updateComponentStatus("tse", "error", `EasyTSE Startfehler: ${spawnError.message}`);
            }
            dialog.showErrorBox("Fehler EasyTSE-Service", `${exeName} konnte nicht gestartet werden: ${spawnError.message}`);
            reject(spawnError);
          });

        }, 1000); // 1 Sekunde Verzögerung
      });
    } catch (error) {
      const criticalErrorMsg = `Kritischer Fehler in startEasyTseService (synchroner Teil): ${error.message}`;
      log.error(criticalErrorMsg);
      if (componentsStatus.tse.required) { // componentsStatus muss im Scope sein
          updateComponentStatus("tse", "error", `EasyTSE krit. Startfehler: ${error.message}`);
      }
      dialog.showErrorBox("Fehler EasyTSE-Service", `Kritischer Fehler: ${error.message}`);
      reject(error);
    }
  });
}

/**
 * Konfiguriert das Logging basierend auf den Einstellungen in der config.json
 * @param {Object} config Die Anwendungskonfiguration
 */
function configureLogging(config) {
  const loggingConfig = config.logging || { enabled: true, logLevel: "info" };

  // Logger-Service mit der Konfiguration konfigurieren
  loggerService.configure(loggingConfig);

  log.info("Logging-Konfiguration angewendet");

  // Wenn Webhook aktiviert ist, gib eine Info aus
  if (loggingConfig.webhookEnabled && loggingConfig.webhookUrl) {
    log.info("ERROR-Level Logs werden an Microsoft Teams Webhook gesendet");
  }

  // Alte Logs bereinigen
  if (loggingConfig.cleanupOldLogs !== false) {
    loggerService.cleanupOldLogs(loggingConfig.maxLogAgeDays || 30);
  }
}

log.info("Anwendung gestartet");

// Globale Variablen
let apiConfig = null;
let mainWindow;
let shopView;
let mqttClient = null;
let epsonPrinter = null;
let tseClient = null;
let zvtClient = null;
let tseSelfTestPerformed = false; // Variable, um zu verfolgen, ob der TSE-Selbsttest bereits durchgeführt wurde
let ecDayEndInProgress = false; // Variable, um zu verfolgen, ob ein EC-Abschluss bereits läuft

const gotTheLock = app.requestSingleInstanceLock(); // Wichtig: Diese Variable merken

if (!gotTheLock) {
  // Dies ist eine zweite Instanz
  log.warn('Eine weitere Instanz der Anwendung läuft bereits. Diese Instanz wird beendet.');
  // Beende die Anwendung sofort, ohne weitere Initialisierungen durchzuführen
  app.exit(0); // Verwende app.exit() statt app.quit() für sofortiges Beenden
} else {
  // Dies ist die erste Instanz
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    // Wenn eine zweite Instanz gestartet wird, fokussiere das Fenster der ersten Instanz.
    log.info('Versuch, eine zweite Instanz zu starten. Fokussiere das Hauptfenster der ersten Instanz.');
    if (mainWindow) { // mainWindow ist deine globale Variable für das Hauptfenster
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}

// Funktion zum Erstellen des Ladefensters
function createLoadingWindow() {
  loadingWindow = new BrowserWindow({
    width: 600,
    height: 500,
    frame: false,
    transparent: false,
    resizable: false,
    center: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, "loading-preload.js"),
    },
  });

  loadingWindow.loadFile(path.join(__dirname, "loading.html"));

  // Für Debugging in der Entwicklung
  if (process.env.NODE_ENV === "development") {
    loadingWindow.webContents.openDevTools({mode: 'detach'});
  }

  // Fenster-Schließen verhindern, falls noch nicht alle Komponenten bereit sind
  loadingWindow.on("close", (event) => {
    if (!allRequiredComponentsReady() && !updateComponentStatus.autoStartInitiated && !userOverrideRequirements) {
      log.warn('Schließen des Ladebildschirms verhindert, da noch nicht alle Komponenten bereit sind');
      event.preventDefault();
    }
  });

  // Wenn die Seite geladen ist, initiale Status-Updates senden
  loadingWindow.webContents.on('did-finish-load', () => {
    log.info('Ladebildschirm geladen, sende initiale Status-Updates');

    // Initiale Status-Updates für alle Komponenten senden
    for (const component in componentsStatus) {
      loadingWindow.webContents.send('update-status', {
        component: component,
        status: componentsStatus[component].status,
        message: `${component} wird initialisiert...`,
        time: new Date().toLocaleTimeString()
      });
    }
  });
}

// Funktion zum Aktualisieren des Status einer Komponente
function updateComponentStatus(component, status, message = null) {
  if (!componentsStatus[component]) return;

  // Aktuelle Zeit für das Logging
  const now = new Date();
  const timeString = now.toLocaleTimeString();

  componentsStatus[component].status = status;
  log.info(`[${timeString}] Komponenten-Status: ${component} => ${status}${message ? ": " + message : ""}`);

  // Status an Ladefenster übermitteln
  if (loadingWindow && !loadingWindow.isDestroyed()) {
    try {
      log.debug(`Sende Status-Update an Ladebildschirm: ${component} => ${status}`);
      loadingWindow.webContents.send('update-status', {
        component: component,
        status: status,
        message: message,
        time: timeString
      });
    } catch (error) {
      log.error(`Fehler beim Senden des Status-Updates an Ladebildschirm: ${error.message}`);
    }
  }

  // Prüfen, ob alle Komponenten initialisiert wurden (unabhängig vom Status)
  let allComponentsInitialized = true;
  let hasErrors = false;

  for (const component in componentsStatus) {
    // Prüfen, ob alle Komponenten einen Status haben (nicht mehr "pending")
    if (componentsStatus[component].status === "pending" || componentsStatus[component].status === "loading") {
      allComponentsInitialized = false;
    }

    // Prüfen, ob Fehler aufgetreten sind
    if (componentsStatus[component].status === "error") {
      hasErrors = true;
    }
  }

  // Wenn alle Komponenten initialisiert wurden und Fehler aufgetreten sind, zeige den "Trotzdem fortfahren"-Button
  if (allComponentsInitialized && hasErrors && loadingWindow && !loadingWindow.isDestroyed()) {
    try {
      log.info('Alle Komponenten initialisiert, aber Fehler aufgetreten. Zeige "Trotzdem fortfahren"-Button');
      loadingWindow.webContents.send('show-continue-button');
    } catch (error) {
      log.error(`Fehler beim Anzeigen des "Trotzdem fortfahren"-Buttons: ${error.message}`);
    }
  }

  // Nach jeder Statusaktualisierung prüfen, ob alle Komponenten bereit sind
  if (allRequiredComponentsReady() && !updateComponentStatus.autoStartInitiated) {
    // Prüfen, ob alle kritischen Komponenten vollständig initialisiert wurden
    let criticalComponentsReady = true;
    const criticalComponents = ['config', 'tse', 'printer']; // MQTT ist nicht kritisch für den Start

    for (const component of criticalComponents) {
      if (componentsStatus[component].status === "loading" || componentsStatus[component].status === "pending") {
        criticalComponentsReady = false;
        break;
      }
    }

    // Fortfahren, wenn alle kritischen Komponenten bereit sind
    if (criticalComponentsReady) {
      updateComponentStatus.autoStartInitiated = true;
      log.info('Alle kritischen Komponenten bereit, starte Anwendung automatisch...');

      // Kurze Verzögerung für bessere Benutzerwahrnehmung der Statusänderungen
      setTimeout(() => {
        createWindow();  // Hauptfenster mit Webview erstellen

        if (config.customer_display) {
          setTimeout(() => {
            try {
              createCustomerDisplay();
              setTimeout(() => {
                setupCartMonitoring();
              }, 1000);
            } catch (error) {
              log.error('Fehler bei der Kundendisplay-Initialisierung:', error);
            }
          }, 1500);
        }

        // Ladefenster verzögert schließen
        setTimeout(() => {
          if (loadingWindow && !loadingWindow.isDestroyed()) {
            loadingWindow.close();
          }
        }, 2000);
      }, 1500);
    } else {
      log.info('Alle erforderlichen Komponenten bereit, aber kritische Komponenten sind noch in Initialisierung. Warte auf Abschluss...');
    }
  }
}

// Statische Variable initialisieren
updateComponentStatus.autoStartInitiated = false;

// Funktion zum Prüfen, ob alle erforderlichen Komponenten bereit sind
// Variable, um zu speichern, ob der Benutzer "Trotzdem fortfahren" geklickt hat
let userOverrideRequirements = false;

function allRequiredComponentsReady() {
  // TSE-Provider bestimmen
  const tseProvider = config.tse_provider || "fiskaly";
  const isEpsonTse = tseProvider.toLowerCase() === "epson";
  const isFiskalyTse = tseProvider.toLowerCase() === "fiskaly";

  // Prüfen, ob TSE erforderlich ist (basierend auf Konfiguration)
  const tseRequired = config.tse_required === true || isEpsonTse;

  // Wenn der Benutzer "Trotzdem fortfahren" geklickt hat, ignoriere alle Anforderungen
  if (userOverrideRequirements) {
    log.info("Komponentenanforderungen werden aufgrund von Benutzerüberschreibung ignoriert");
    return true; // Alle Anforderungen ignorieren und sofort true zurückgeben
  }

  // Für Epson TSE ist die TSE-Komponente absolut erforderlich
  if (isEpsonTse && componentsStatus.tse.status !== "success") {
    log.warn("Epson TSE ist erforderlich, aber nicht verfügbar");
    return false;
  }

  // Für Fiskaly TSE prüfen, ob sie erforderlich ist
  if (isFiskalyTse && tseRequired && componentsStatus.tse.status === "error") {
    log.warn("Fiskaly TSE ist erforderlich, aber nicht verfügbar");
    return false;
  }

  // MQTT ist immer erforderlich
  if (componentsStatus.mqtt.status !== "success" &&
      componentsStatus.mqtt.status !== "warning") {
    log.warn("MQTT ist erforderlich, aber nicht verfügbar");
    return false;
  }

  // Überprüfung der anderen erforderlichen Komponenten
  for (const component in componentsStatus) {
    if (component !== "tse" &&
        componentsStatus[component].required &&
        componentsStatus[component].status !== "success" &&
        componentsStatus[component].status !== "warning") {
      log.warn(`Komponente ${component} ist erforderlich, aber nicht verfügbar`);
      return false;
    }
  }

  return true;
}


// Funktion zum Erstellen des Passwortänderungsfensters
function createChangePasswordWindow() {
  // Falls bereits ein Fenster existiert, dieses schließen
  if (changePasswordWindow && !changePasswordWindow.isDestroyed()) {
    changePasswordWindow.close();
  }

  // Passwortänderungsfenster erstellen
  changePasswordWindow = new BrowserWindow({
    width: 450,
    height: 450,
    resizable: false,
    modal: true,
    parent: mainWindow,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, "change-password-preload.js"),
    },
    autoHideMenuBar: !config.windowSettings.showMenuBar,
    frame: false,
  });

  // HTML-Seite laden
  changePasswordWindow.loadFile(path.join(__dirname, "change-password.html"));

  // Event-Handler wenn das Fenster geschlossen wird
  changePasswordWindow.on("closed", () => {
    changePasswordWindow = null;
  });
}

// IPC-Handler für Passwortänderung
ipcMain.on("change-password", (event, data) => {
  if (!data || !data.currentPassword || !data.newPassword) {
    event.reply("change-password-result", {
      success: false,
      message: "Bitte füllen Sie alle Felder aus.",
    });
    return;
  }

  // Überprüfen des aktuellen Passworts
  if (data.currentPassword !== getAdminPassword()) {
    event.reply("change-password-result", {
      success: false,
      message: "Das aktuelle Passwort ist nicht korrekt.",
    });
    return;
  }

  try {
    // Konfigurationsdatei laden
    const configPath = path.join(__dirname, "config.json");
    const configData = JSON.parse(fs.readFileSync(configPath, "utf8"));

    // Passwort aktualisieren
    configData.adminPassword = data.newPassword;

    // Konfigurationsdatei speichern
    fs.writeFileSync(configPath, JSON.stringify(configData, null, 2), "utf8");

    // Konfiguration neu laden
    config = configData;

    // Erfolg zurückmelden
    event.reply("change-password-result", {
      success: true,
      message: "Passwort erfolgreich geändert.",
    });

    // Fenster nach kurzer Verzögerung schließen
    setTimeout(() => {
      if (changePasswordWindow && !changePasswordWindow.isDestroyed()) {
        changePasswordWindow.close();
      }
    }, 2000);

    log.info("Administratorpasswort erfolgreich geändert");
  } catch (error) {
    log.error("Fehler beim Ändern des Passworts:", error);
    event.reply("change-password-result", {
      success: false,
      message: `Fehler beim Ändern des Passworts: ${error.message}`,
    });
  }
});

// Globaler Event-Handler für ZVT-Zahlungsergebnisse
ipcMain.on("zvt-payment-result", async (event, result) => {
  if (paymentModalWindow && !paymentModalWindow.isDestroyed()) {
    paymentModalWindow.webContents.send("payment-result", result);

    // Bei Fehlern und speziell bei Abbrüchen auch das Modal aktualisieren
    if (!result.success) {
      let statusText = "Fehlgeschlagen";
      let message = result.error || "Unbekannter Fehler";

      // Explizite Prüfung auf Abbruch-Statuscodes
      if (result.statusCode === "0C" ||
          (result.error && result.error.toLowerCase().includes("abgebrochen"))) {
        statusText = "Abgebrochen";
        message = "Vorgang wurde am Terminal abgebrochen";
      }

      updatePaymentModal({
        status: "error",
        message: message,
        statusText: statusText,
        ...result
      });

      // Transaktion in Datenbank als abgebrochen/fehlgeschlagen markieren
      if (result.transactionId && tseClient && tseClient.revisionDb) {
        log.info(`Markiere Transaktion ${result.transactionId} als fehlgeschlagen in Datenbank`);
        try {
          await tseClient.revisionDb.updatePaymentTransaction(result.transactionId, {
            status: result.statusCode === "0C" ? 'ABORTED' : 'FAILED',
            completed_at: Math.floor(Date.now() / 1000)
          });
          log.info(`Transaktion ${result.transactionId} erfolgreich als fehlgeschlagen markiert`);
        } catch (dbError) {
          log.error(`Fehler beim Markieren der Transaktion ${result.transactionId} als fehlgeschlagen:`, dbError.message);
        }
      }
    }
  }
});

ipcMain.on("cancel-change-password", () => {
  if (changePasswordWindow && !changePasswordWindow.isDestroyed()) {
    changePasswordWindow.close();
  }
});

// Funktion zum Erstellen des Passwortfensters
function createPasswordWindow(actionToPerform) {
  // Falls bereits ein Fenster existiert, dieses schließen
  if (passwordWindow && !passwordWindow.isDestroyed()) {
    passwordWindow.close();
  }

  // Speichern der angeforderten Aktion
  pendingConfigAction = actionToPerform;

  // Passwortfenster erstellen
  passwordWindow = new BrowserWindow({
    width: 400,
    height: 320,
    resizable: false,
    modal: true,
    parent: mainWindow,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, "password-preload.js"),
    },
    autoHideMenuBar: !config.windowSettings.showMenuBar,
    frame: false,
  });

  // HTML-Seite laden
  passwordWindow.loadFile(path.join(__dirname, "password-dialog.html"));

  // Event-Handler wenn das Fenster geschlossen wird
  passwordWindow.on("closed", () => {
    passwordWindow = null;
    pendingConfigAction = null;
  });
}

// Passwort aus der Konfiguration laden
function getAdminPassword() {
  return config.adminPassword || "#TicketPAY2017!#";
}

// IPC-Handler für Passwortüberprüfung
ipcMain.on("check-password", (event, password) => {
  if (password === getAdminPassword()) {
    // Passwort korrekt
    if (passwordWindow && !passwordWindow.isDestroyed()) {
      passwordWindow.close();
    }

    // Ausführen der angeforderten Aktion
    if (pendingConfigAction && typeof pendingConfigAction === "function") {
      pendingConfigAction();
    }

    // Zurücksetzen der ausstehenden Aktion
    pendingConfigAction = null;
  } else {
    // Passwort falsch
    if (passwordWindow && !passwordWindow.isDestroyed()) {
      passwordWindow.webContents.send("password-error", "Falsches Passwort.");
    }
  }
});

// IPC-Handler für Abbrechen des Passwortdialogs
ipcMain.on("cancel-password-dialog", () => {
  if (passwordWindow && !passwordWindow.isDestroyed()) {
    passwordWindow.close();
  }
  pendingConfigAction = null;
});

function createAppMenu() {
  const configSubmenu = [
    {
      label: "Konfiguration bearbeiten",
      click: () => {
        // Anstatt direkt die Aktion auszuführen, jetzt den Passwortdialog anzeigen
        createPasswordWindow(() => {
          createConfigWindow();
        });
      },
    },
    {
      label: "Konfiguration neu laden",
      click: () => {
        // Passwortdialog anzeigen
        createPasswordWindow(() => {
          // Konfiguration von der Festplatte neu laden
          try {
            config = JSON.parse(
              fs.readFileSync(path.join(__dirname, "config.json"), "utf8")
            );
            configureLogging(config);

            // Benachrichtigung an den Renderer-Prozess senden
            if (mainWindow && !mainWindow.isDestroyed()) {
              mainWindow.webContents.send("config-reloaded", { success: true });
            }

            dialog.showMessageBox({
              type: "info",
              title: "Konfiguration neu geladen",
              message: "Die Konfiguration wurde erfolgreich neu geladen.",
              buttons: ["OK"],
            });

            log.info("Konfiguration manuell neu geladen");
          } catch (error) {
            log.error("Fehler beim Neuladen der Konfiguration:", error);

            dialog.showMessageBox({
              type: "error",
              title: "Konfigurationsfehler",
              message: `Fehler beim Neuladen der Konfiguration: ${error.message}`,
              buttons: ["OK"],
            });
          }
        });
      },
    },
    {
      label: "Passwort ändern",
      click: () => {
        createPasswordWindow(() => {
          createChangePasswordWindow();
        });
      },
    },
    // Monitoring removed from here
    { type: "separator" },
    {
      label: "EasyTSE",
      click: () => {
        // Passwortdialog anzeigen, bevor EasyTSE gestartet wird
        createPasswordWindow(() => {
          const easyTSEPath = "C:\\EasyTSE\\EasyTSEGUI.exe";
          log.info(`Starte externe Anwendung: ${easyTSEPath}`);

          exec(easyTSEPath, (error) => {
            if (error) {
              log.error(`Fehler beim Starten von EasyTSE: ${error.message}`);
              dialog.showMessageBox({
                type: "error",
                title: "EasyTSE Fehler",
                message: `Fehler beim Starten von EasyTSE: ${error.message}`,
                buttons: ["OK"],
              });
            }
          });
        });
      },
    },
  ];

  const template = [
    {
      label: "Datei",
      submenu: [
        {
          label: "Anwendung neu starten",
          click: () => {
            createPasswordWindow(() => {
              app.relaunch();
              app.exit();
            });
          },
        },
        { type: "separator" },
        { role: "quit", label: "Beenden" }
      ],
    },
    {
      label: "Ansicht",
      submenu: [
        { role: "reload", label: "Neu laden" },
        { role: "forceReload", label: "Neu laden erzwingen" },
        { role: "toggleDevTools", label: "Entwicklertools" },
        { type: "separator" },
        { role: "resetZoom", label: "Zoom zurücksetzen" },
        { role: "zoomIn", label: "Vergrößern" },
        { role: "zoomOut", label: "Verkleinern" },
        { type: "separator" },
        { role: "togglefullscreen", label: "Vollbild" },
      ],
    },
    {
      label: "Support",
      submenu: configSubmenu,
    },
    // New Monitoring menu item
    {
      label: "Monitoring",
      click: () => {
        // No password protection, directly open the monitoring window
        createMonitoringWindow();
      },
    },
    {
      label: "EC-Abschluss",
      click: () => {
        performECDayEnd();
      },
    },
    {
      label: "Tastatur",
      click: () => {
        launchOnScreenKeyboard();
      },
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

let configWindow = null;
let monitoringWindow = null;

function createConfigWindow() {
  if (configWindow) {
    configWindow.focus();
    return;
  }

  configWindow = new BrowserWindow({
    width: 900,
    height: 600,
    autoHideMenuBar: !config.windowSettings.showMenuBar,
    parent: mainWindow,
    modal: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
    },
  });

  configWindow.loadFile(path.join(__dirname, "config-editor.html"));

  configWindow.on("closed", () => {
    configWindow = null;
  });

  configWindow.webContents.on("did-finish-load", () => {
    configWindow.webContents.send("load-config", config);
  });
}

function createMonitoringWindow() {
  if (monitoringWindow) {
    monitoringWindow.focus();
    return;
  }

  monitoringWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    autoHideMenuBar: !config.windowSettings.showMenuBar,
    parent: mainWindow,
    modal: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, "monitoring-preload.js"),
    },
  });

  monitoringWindow.loadFile(path.join(__dirname, "monitoring.html"));

  monitoringWindow.on("closed", () => {
    monitoringWindow = null;
  });

  // Für Debugging in der Entwicklung
  if (process.env.NODE_ENV === "development") {
    monitoringWindow.webContents.openDevTools({mode: 'detach'});
  }

  // Initialisiere die Datenbank und lade die ersten Transaktionen
  monitoringWindow.webContents.once("did-finish-load", async () => {
    try {
      // Stelle sicher, dass die TSE-Datenbank initialisiert ist
      if (!tseClient || !tseClient.revisionDb) {
        log.info('Initialisiere Revisionsdatenbank für Monitoring');
        const TseDatabase = require('./tse-database');
        const db = new TseDatabase();
        await db.initialize();

        // Wenn tseClient existiert, setze die Datenbank
        if (tseClient) {
          tseClient.revisionDb = db;
        } else {
          // Erstelle einen temporären Datenbank-Zugriff
          global.monitoringDb = db;
        }
      }

      // Verwende die Datenbank vom tseClient oder die temporäre
      const db = tseClient?.revisionDb || global.monitoringDb;

      // Lade die ersten Transaktionen
      const transactions = await db.getTransactionsView(1, 50);
      const totalCount = await db.countTransactionsView();

      // Sende die Daten an das Monitoring-Fenster
      monitoringWindow.webContents.send('transactions-loaded', {
        transactions,
        totalCount,
        page: 1,
        pageSize: 50
      });
    } catch (error) {
      log.error('Fehler beim Laden der Transaktionen für das Monitoring:', error);
      monitoringWindow.webContents.send('transactions-error', {
        error: error.message
      });
    }
  });
}

ipcMain.on("save-config", (event, newConfig) => {
  try {
    // Verwende app.getPath('userData') für einen beschreibbaren Speicherort
    const userDataPath = app.getPath('userData');
    const backupPath = path.join(userDataPath, `config-backup-${Date.now()}.json`);
    const configPath = path.join(userDataPath, "config.json");

    // Backup erstellen
    fs.writeFileSync(backupPath, JSON.stringify(config, null, 2), "utf8");

    // Neue Konfiguration speichern
    fs.writeFileSync(configPath, JSON.stringify(newConfig, null, 2), "utf8");

    config = newConfig;
    configureLogging(config);

    event.reply("save-config-result", { success: true });

    log.info("Configuration saved successfully");
    log.info(`Speichere Backup nach: ${backupPath}`);
    log.info(`Speichere Konfiguration nach: ${configPath}`);

    dialog.showMessageBox({
      type: "info",
      title: "Configuration Saved",
      message:
        "The configuration has been saved successfully. Some changes may require application restart.",
      buttons: ["OK"],
    });
  } catch (error) {
    log.error("Failed to save configuration:", error);

    event.reply("save-config-result", {
      success: false,
      error: error.message,
    });

    dialog.showMessageBox({
      type: "error",
      title: "Configuration Error",
      message: `Failed to save configuration: ${error.message}`,
      buttons: ["OK"],
    });
  }
});

// IPC-Handler für den Anwendungsstart
ipcMain.on("start-application", () => {
  log.info("Benutzer hat Kassensystem gestartet.");

  // Hauptfenster erstellen
  createWindow();

  // Ladefenster verzögert schließen
  setTimeout(() => {
    if (loadingWindow && !loadingWindow.isDestroyed()) {
      loadingWindow.close();
    }
  }, 500);
});

// Funktion zum Speichern der API-Konfiguration lokal
function saveApiConfig(config) {
  try {
    const configPath = path.join(__dirname, "api-config.json");
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2), "utf8");
    log.info("API-Konfiguration lokal gespeichert");
  } catch (error) {
    log.error("Fehler beim Speichern der API-Konfiguration:", error);
  }
}

// Funktion zum Laden der gespeicherten API-Konfiguration
function loadSavedApiConfig() {
  try {
    const configPath = path.join(__dirname, "api-config.json");
    if (fs.existsSync(configPath)) {
      const savedConfig = JSON.parse(fs.readFileSync(configPath, "utf8"));
      log.info("Gespeicherte API-Konfiguration geladen");
      return savedConfig;
    }
  } catch (error) {
    log.error("Fehler beim Laden der gespeicherten API-Konfiguration:", error);
  }
  return null;
}

// Funktion zum Laden der API-Konfiguration
function loadApiConfig() {
  return new Promise((resolve, reject) => {
    const options = {
      method: "GET",
      headers: {
        Authorization: config.api.authKey,
      },
    };

    log.info("Starte API-Anfrage an:", config.api.configUrl);
    const req = https.request(config.api.configUrl, options, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        if (res.statusCode === 200) {
          try {
            const jsonData = JSON.parse(data);
            log.info("API-Konfiguration erfolgreich geladen");

            if (jsonData.SystemConfig) {
              // PascalCase Format
              log.info("API-Antwort im PascalCase-Format");
              saveApiConfig(jsonData);

              // API-Konfiguration an den Logger-Service übergeben
              loggerService.setApiConfig(jsonData);

              // Wenn die API-Konfiguration auch Logging-Einstellungen enthält, diese anwenden
              if (jsonData.Logging) {
                config.logging = { ...config.logging, ...jsonData.Logging };
                configureLogging(config);
                log.info(
                  "Logging-Konfiguration nach API-Laden aktualisiert (PascalCase)"
                );
              }

              resolve(jsonData);
            } else if (jsonData.system_config) {
              // snake_case Format
              log.info("API-Antwort im snake_case-Format");
              saveApiConfig(jsonData);

              // API-Konfiguration an den Logger-Service übergeben
              loggerService.setApiConfig(jsonData);

              // Wenn die API-Konfiguration auch Logging-Einstellungen enthält, diese anwenden
              if (jsonData.logging) {
                config.logging = { ...config.logging, ...jsonData.logging };
                configureLogging(config);
                log.info(
                  "Logging-Konfiguration nach API-Laden aktualisiert (snake_case)"
                );
              }

              resolve(jsonData);
            } else {
              log.error(
                "Unbekanntes API-Antwortformat:",
                Object.keys(jsonData)
              );
              reject(new Error("Unbekanntes API-Antwortformat"));
            }
          } catch (error) {
            log.error("Fehler beim Parsen der API-Antwort:", error);
            reject(error);
          }
        } else {
          log.error("API-Anfrage fehlgeschlagen mit Status:", res.statusCode);
          reject(
            new Error(
              `API-Anfrage fehlgeschlagen mit Status: ${res.statusCode}`
            )
          );
        }
      });
    });

    req.on("error", (error) => {
      log.error("Fehler bei der API-Anfrage:", error);
      reject(error);
    });

    req.end();
  });
}

//  Funktion zum Erstellen des Kundendisplays
function createCustomerDisplay() {
  // Wenn die Kundendisplay-Option nicht aktiviert ist, nichts tun
  if (!config.customer_display) {
    log.info("Kundendisplay deaktiviert, wird nicht erstellt");
    return;
  }

  // Prüfen, ob Hauptfenster verfügbar ist
  if (!mainWindow || mainWindow.isDestroyed()) {
    log.error("Hauptfenster nicht verfügbar, Kundendisplay kann nicht erstellt werden");
    return;
  }

  log.info("Erstelle Kundendisplay");

  // Bildschirminformationen abrufen
  const displays = require("electron").screen.getAllDisplays();
  let externalDisplay = null;

  // Überprüfen, ob mehr als ein Bildschirm vorhanden ist
  if (displays.length > 1) {
    externalDisplay = displays[1]; // Zweiter Bildschirm
    log.info("Zweiter Bildschirm gefunden:", {
      width: externalDisplay.bounds.width,
      height: externalDisplay.bounds.height,
    });
  } else {
    log.warn(
      "Kein zweiter Bildschirm gefunden, Kundendisplay wird auf Hauptbildschirm angezeigt"
    );
  }

  // Fenster erstellen
  customerDisplayWindow = new BrowserWindow({
    width: externalDisplay ? externalDisplay.bounds.width : 800,
    height: externalDisplay ? externalDisplay.bounds.height : 600,
    x: externalDisplay ? externalDisplay.bounds.x : undefined,
    y: externalDisplay ? externalDisplay.bounds.y : undefined,
    fullscreen: config.windowSettings.fullscreen,
    kiosk: config.windowSettings.kiosk,
    autoHideMenuBar: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
    },
  });

  // Kundendisplay-HTML laden
  customerDisplayWindow.loadFile(path.join(__dirname, "customer-display.html"));

  // Event-Handler für Fenster-Schließung
  customerDisplayWindow.on("closed", () => {
    customerDisplayWindow = null;
  });

  // Warte auf DOM-Ready und sende initiale leere Daten
  customerDisplayWindow.webContents.on("dom-ready", () => {
    log.info("Kundendisplay DOM ist bereit");

    // Kurze Verzögerung, dann sende leere Daten, um zu prüfen, ob IPC funktioniert
    setTimeout(() => {
      try {
        log.info("Sende Test-Daten an Kundendisplay");
        customerDisplayWindow.webContents.send("cart-update", {
          items: [],
          totalAmount: "0,00 €",
        });
      } catch (error) {
        log.error(
          "Fehler beim Senden der Test-Daten an das Kundendisplay:",
          error
        );
      }
    }, 1000);

    // Für Debugging: DevTools im Kundendisplay-Fenster öffnen
    if (process.env.NODE_ENV === "development") {
      customerDisplayWindow.webContents.openDevTools();
    }
  });

  log.info("Kundendisplay erfolgreich erstellt");
}

//  Funktion zum Aktualisieren des Kundendisplays
function updateCustomerDisplay(cartData) {
  if (customerDisplayWindow && !customerDisplayWindow.isDestroyed()) {
    // Verzögerung für die Datenübertragung hinzufügen
    setTimeout(() => {
      try {
        customerDisplayWindow.webContents.send("cart-update", cartData);
      } catch (error) {
        log.error(
          "Fehler beim Senden der Warenkorbdaten an das Kundendisplay:",
          error
        );
      }
    }, 50); // Kurze Verzögerung für bessere IPC-Übertragung
  }
}

//  Funktion zur regelmäßigen Überwachung des Warenkorbs
function setupCartMonitoring() {
  if (!config.customer_display || !mainWindow || !shopView) {
    log.warn("Warenkorb-Überwachung konnte nicht gestartet werden: Voraussetzungen nicht erfüllt");
    log.info(`- Kundendisplay aktiviert: ${config.customer_display ? "Ja" : "Nein"}`);
    log.info(`- Hauptfenster verfügbar: ${mainWindow ? "Ja" : "Nein"}`);
    log.info(`- Webview verfügbar: ${shopView ? "Ja" : "Nein"}`);
    return;
  }

  log.info("Starte Überwachung des Warenkorbs");

  // Log-Level aus der Konfiguration lesen
  const cartLogLevel = config.logging?.cartLogging || "minimal";

  // Konfiguration für den CartParser setzen
  if (cartParser.setConfig) {
    cartParser.setConfig({
      logLevel: cartLogLevel,
    });

    // Log-Level in Worte übersetzen (für die Benutzerausgabe)
    const logLevelText =
      {
        false: "Deaktiviert",
        minimal: "Minimal",
        full: "Vollständig",
      }[cartLogLevel] || "Minimal";

    log.info(`Warenkorb-Logging: ${logLevelText}`);
  } else {
    log.warn(
      "CartParser hat keine setConfig-Funktion, Logging-Konfiguration nicht möglich"
    );
  }

  // Polling-Intervall aus der Konfiguration oder Standard (2 Sekunden)
  const pollingInterval =
    config.customer_display_options?.polling_interval || 2000;

  // Initialisierung mit Verzögerung, um sicherzustellen, dass alles richtig geladen ist
  setTimeout(async () => {
    try {
      // Webview erneut prüfen, falls sie sich in der Zwischenzeit geändert hat
      if (!shopView || !shopView.webContents) {
        log.error("Webview nicht verfügbar für initiale Warenkorb-Abfrage");
        return;
      }

      // Initialer Abruf der Warenkorbdaten
      const initialCartData = await cartParser.extractCartFromWebContents(
        shopView.webContents
      );
      updateCustomerDisplay(initialCartData);

      log.info("Initiale Warenkorbdaten geladen und an Kundendisplay gesendet");
    } catch (error) {
      log.error("Fehler beim initialen Abruf der Warenkorbdaten:", error);
    }

    // Monitoring-Intervall starten
    const monitoringInterval = setInterval(async () => {
      if (!mainWindow || mainWindow.isDestroyed() || !shopView || !shopView.webContents) {
        clearInterval(monitoringInterval);
        log.warn("Warenkorbüberwachung beendet - Fenster nicht mehr verfügbar");
        return;
      }

      try {
        // Warenkorb extrahieren
        const cartData = await cartParser.extractCartFromWebContents(
          shopView.webContents
        );

        // Kundendisplay aktualisieren
        updateCustomerDisplay(cartData);
      } catch (error) {
        log.error("Fehler bei der Warenkorb-Überwachung:", error);
      }
    }, pollingInterval);

    // Intervall speichern, um es später ggf. beenden zu können
    global.cartMonitoringInterval = monitoringInterval;

    log.info(
      `Warenkorbüberwachung gestartet mit Intervall: ${pollingInterval}ms`
    );
  }, 2000); // 2 Sekunden Verzögerung für die initiale Überwachung
}

/**
 * Funktion zum Erstellen und Anzeigen des Modal-Fensters
 * @param {Object} paymentData Zahlungsdaten mit Betrag und Transaktions-ID
 * @returns {BrowserWindow} Das erstellte Modal-Fenster
 */
function createPaymentModalWindow(paymentData) {
  // Falls bereits ein Modal-Fenster existiert, schließen
  if (paymentModalWindow && !paymentModalWindow.isDestroyed()) {
    paymentModalWindow.close();
  }

  // Hauptfenster-Position und -Größe ermitteln
  const mainBounds = mainWindow.getBounds();

  // Modal-Größe festlegen
  const modalWidth = 500;
  const modalHeight = 450;

  // Sicherstellen, dass der Betrag korrekt formatiert ist
  let safePaymentData = { ...paymentData };

  // Betrag explizit speichern, wenn vorhanden
  if (safePaymentData.amount !== undefined) {
    // Sicherstellen, dass der Betrag als Zahl gespeichert ist
    if (typeof safePaymentData.amount === "string") {
      safePaymentData.amount = parseFloat(
        safePaymentData.amount.replace(",", ".")
      );
    }

    // Logging für Debugging
    log.info(
      `Modal-Fenster zeigt Betrag: ${
        safePaymentData.amount
      } (Typ: ${typeof safePaymentData.amount})`
    );
  } else {
    log.warn("Keine Betragsangabe in paymentData gefunden!");
  }

  // Modal-Fenster erstellen
  paymentModalWindow = new BrowserWindow({
    width: modalWidth,
    height: modalHeight,
    x: Math.floor(mainBounds.x + (mainBounds.width - modalWidth) / 2),
    y: Math.floor(mainBounds.y + (mainBounds.height - modalHeight) / 2),
    parent: mainWindow,
    modal: true,
    frame: false, // Keine Fensterrahmen
    transparent: false,
    resizable: false,
    movable: false,
    skipTaskbar: true,
    alwaysOnTop: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, "modal-preload.js"),
    },
  });

  // Modal-HTML laden
  paymentModalWindow.loadFile("payment-modal.html");

  // Nach dem Laden die Daten an das Modal senden
  paymentModalWindow.webContents.once("did-finish-load", () => {
    log.info(
      "Modal-Fenster geladen, sende Zahlungsdaten:",
      JSON.stringify(safePaymentData)
    );
    paymentModalWindow.webContents.send("payment-data", safePaymentData);
  });

  // Event-Handler für Fenster-Schließung
  paymentModalWindow.on("closed", () => {
    paymentModalWindow = null;
  });

  return paymentModalWindow;
}

// Funktion zum Aktualisieren des Modals
function updatePaymentModal(data) {
  if (!paymentModalWindow || paymentModalWindow.isDestroyed()) {
    // Falls das Modal nicht existiert, erstellen
    createPaymentModalWindow(data);
  } else {
    try {
      // Wenn ZVT-Client vorhanden ist, prüfe ob Belege verfügbar sind
      if (zvtClient && zvtClient.lastReceipts) {
        data.hasCustomerReceipt =
          zvtClient.lastReceipts.customer !== null &&
          zvtClient.lastReceipts.customer.length > 0;
        data.hasMerchantReceipt =
          zvtClient.lastReceipts.merchant !== null &&
          zvtClient.lastReceipts.merchant.length > 0;
      }

      // Daten an bestehendes Modal senden
      paymentModalWindow.webContents.send("payment-update", data);

      // Modal in den Vordergrund bringen
      if (!paymentModalWindow.isVisible()) {
        paymentModalWindow.show();
      }
      paymentModalWindow.focus();
    } catch (error) {
      log.error("Fehler beim Aktualisieren des Modals:", error.message);
    }
  }
}

// Funktion zum Schließen des Modals
function closePaymentModal() {
  if (paymentModalWindow && !paymentModalWindow.isDestroyed()) {
    paymentModalWindow.close();
    paymentModalWindow = null;
  }
}

function createUnifiedTsePayload(transactionDetail, tseData, eventType) {
  try {
    // Standardwerte für fehlende Parameter
    tseData = tseData || {};
    const tenantId = transactionDetail.tenant_id;
    const transactionId = transactionDetail.id;

    // Unix-Timestamp in ISO-String umwandeln, falls es ein Number ist
    let openingTimestamp =
      tseData.tse_opening_timestamp || new Date().toISOString();
    if (typeof openingTimestamp === "number") {
      openingTimestamp = new Date(openingTimestamp * 1000).toISOString();
    }

    // Für Transaktions-ID Revision verwenden oder einen Standardwert
    let txRevision = 1;
    if (tseData.revision) {
      txRevision = tseData.revision;
    } else if (
      eventType === "finish" ||
      eventType === "finished" ||
      eventType === "canceled"
    ) {
      txRevision = 2;
    }

    // Standard-Signaturzähler (nie null/leer lassen)
    const sigCounter =
      parseInt(String(tseData.tse_signatur_counter || "1"), 10) || 1;
    const sigEndCounter =
      parseInt(String(tseData.tse_signatur_end_counter || sigCounter), 10) ||
      sigCounter;

    // Standard-Signaturwerte (nie leere Strings)
    const sigStart = String(tseData.tse_signatur_start || "SignatureInit");
    const sigEnd = String(
      tseData.tse_signatur_end ||
        (eventType === "finished" ? "dummy-sig-end" : "")
    );

    // Standard TSE-Seriennummer
    const serialNumber = String(
      tseData.tse_serial_number || tseClient?.tssId || ""
    );

    // Standardwerte für QR-Code
    const qrCode = String(tseData.qr_code || "");

    // Betrag aus TransactionDetail extrahieren und korrekt formatieren
    let totalAmount = parseFloat(transactionDetail.total_amount || 0);
    if (
      Math.abs(totalAmount) > 10000 ||
      (typeof transactionDetail.total_amount === "string" &&
        !transactionDetail.total_amount.includes("."))
    ) {
      totalAmount = totalAmount / 100;
    }

    // Mehrwertsteuer und Zahlungsdaten extrahieren
    let taxSet = tseData.tax_set;
    let payments = tseData.payments;

    // Wenn keine Steuerdaten vorhanden sind, aber ein tse_entry, dann extrahieren
    if ((!taxSet || taxSet.length === 0) && transactionDetail.tse_entry) {
      try {
        // Verwende die verbesserte Methode, falls verfügbar
        if (tseClient && typeof tseClient._extractTaxSet === "function") {
          taxSet = tseClient._extractTaxSet(transactionDetail);
        } else {
          // Fallback: Einfache Extraktion aus tse_entry
          const tseEntryParts = transactionDetail.tse_entry.split(",");
          if (tseEntryParts.length >= 9) {
            const netAmount = parseFloat(tseEntryParts[6] || "0.00");
            taxSet = [
              {
                taxRate: 0,
                amount: 0,
                netAmount: netAmount,
              },
            ];
          } else {
            taxSet = [{ taxRate: 0, amount: 0, netAmount: totalAmount }];
          }
        }
      } catch (taxError) {
        log.warn(
          "Fehler beim Extrahieren der Steuerdaten aus tse_entry:",
          taxError.message
        );
        taxSet = [{ taxRate: 0, amount: 0, netAmount: totalAmount }]; // Fallback
      }
    } else if (!taxSet || taxSet.length === 0) {
      taxSet = [{ taxRate: 0, amount: 0, netAmount: totalAmount }]; // Standard-Fallback
    }

    // Wenn keine Zahlungsdaten vorhanden sind, aber ein tse_entry, dann extrahieren
    if ((!payments || payments.length === 0) && transactionDetail.tse_entry) {
      try {
        // Verwende die verbesserte Methode, falls verfügbar
        if (tseClient && typeof tseClient._extractPayments === "function") {
          payments = tseClient._extractPayments(transactionDetail);
        } else {
          // Fallback: Einfache Extraktion aus tse_entry
          const tseEntryParts = transactionDetail.tse_entry.split(",");
          if (tseEntryParts.length >= 9) {
            const barBetrag = parseFloat(tseEntryParts[7] || "0.00");
            const unbarBetrag = parseFloat(tseEntryParts[8] || "0.00");

            payments = [];

            if (barBetrag !== 0) {
              payments.push({
                type: "bar",
                amount: barBetrag,
                name: "Bargeld",
              });
            }

            if (unbarBetrag !== 0) {
              payments.push({
                type: "unbar",
                amount: unbarBetrag,
                name: "Kartenzahlung",
              });
            }

            if (payments.length === 0) {
              payments = [
                { type: "bar", amount: totalAmount, name: "Bargeld" },
              ];
            }
          } else {
            payments = [{ type: "bar", amount: totalAmount, name: "Bargeld" }];
          }
        }
      } catch (paymentError) {
        log.warn(
          "Fehler beim Extrahieren der Zahlungsdaten aus tse_entry:",
          paymentError.message
        );
        payments = [{ type: "bar", amount: totalAmount, name: "Bargeld" }]; // Fallback
      }
    } else if (!payments || payments.length === 0) {
      payments = [{ type: "bar", amount: totalAmount, name: "Bargeld" }]; // Standard-Fallback
    }

    // Vereinheitlichtes Datenformat mit sinnvollen Standardwerten
    return {
      tenant_id: tenantId,
      cashbox_id: tseClient?.config?.fiskaly_config?.client_id || "K001",
      wizid_transaction_id: transactionId,
      tse_transaction_id: txRevision,
      tse_opening_timestamp: openingTimestamp,
      tse_signatur_start: sigStart,
      tse_signatur_end: sigEnd,
      tse_signatur_counter: sigCounter,
      tse_signatur_end_counter: sigEndCounter,
      tse_signatur_end_timestamp:
        tseData.tse_signatur_end_timestamp || openingTimestamp,
      tse_serial_number: serialNumber,
      tse_hash_algorithm: String(tseData.tse_hash_algorithm || "SHA256"),
      tse_public_key: String(tseData.tse_public_key || "BB5aEWBs8d2yzFImhvNkENNMoPSA4wClcg32xpPW5L8uYBXma5cfoiTIqoPbZlpTVwA4rnNjOireTmEA7LxZuiW/PY8ZEmbBaqbRGv1ncmWFclpymYrHflELR9FPj0IWjA=="),
      tse_error: String(tseData.tse_error || ""),
      transaction_type: String(transactionDetail.transaction_type || "Beleg"),
      transaction_sub_type: String(
        transactionDetail.transaction_sub_type || "Verkauf"
      ),
      qr_code: qrCode,
      total_amount: parseFloat(totalAmount.toFixed(2)),
      tax_set: taxSet,
      payments: payments,
      process_type: parseInt(String(tseData.process_type || "0"), 10) || 0,
      vorgangs_type: parseInt(String(tseData.vorgangs_type || "0"), 10) || 0,
    };
  } catch (error) {
    log.error("Fehler beim Erstellen des TSE-Payloads:", error.message);
    // Minimaler Fallback für Fehlerfall
    return {
      tenant_id: transactionDetail.tenant_id,
      cashbox_id: tseClient?.config?.fiskaly_config?.client_id || "K001",
      wizid_transaction_id: transactionDetail.id,
      tse_transaction_id: 0,
      tse_opening_timestamp: new Date().toISOString(),
      tse_signatur_start: "dummy-sig-start",
      tse_signatur_end: eventType === "finished" ? "dummy-sig-end" : "",
      tse_signatur_counter: 1,
      tse_signatur_end_counter: 1,
      tse_public_key: "BB5aEWBs8d2yzFImhvNkENNMoPSA4wClcg32xpPW5L8uYBXma5cfoiTIqoPbZlpTVwA4rnNjOireTmEA7LxZuiW/PY8ZEmbBaqbRGv1ncmWFclpymYrHflELR9FPj0IWjA==",
      tse_error: `Fehler beim Erstellen des Payloads: ${error.message}`,
      transaction_type: String(transactionDetail.transaction_type || "Beleg"),
      transaction_sub_type: String(
        transactionDetail.transaction_sub_type || "Verkauf"
      ),
    };
  }
}

// Senden einer vereinheitlichten TSE-Nachricht
function publishUnifiedTseMessage(transactionDetail, tseData, eventType) {
  if (!mqttClient || !mqttClient.connected) {
    log.error("MQTT-Client nicht verbunden, kann TSE-Nachricht nicht senden");
    return false;
  }

  try {
    const tenantId = transactionDetail.tenant_id;

    // Topic bestimmen basierend auf Event-Typ
    let topic;
    if (eventType === "created" || eventType === "data") {
      topic =
        apiConfig.mqtt_config?.topics?.tse_data ||
        `cmd/tenant/${tenantId}/cash_register_framework/tse/created`;
    } else if (eventType === "finished" || eventType === "finish") {
      topic =
        apiConfig.mqtt_config?.topics?.tse_finish ||
        `cmd/tenant/${tenantId}/cash_register_framework/tse/finished`;
    } else if (eventType === "canceled") {
      topic =
        apiConfig.mqtt_config?.topics?.tse_finish ||
        `cmd/tenant/${tenantId}/cash_register_framework/tse/canceled`;
    } else {
      topic = `cmd/tenant/${tenantId}/cash_register_framework/tse/${eventType}`;
    }

    // Einheitliches Payload erstellen
    const payload = createUnifiedTsePayload(
      transactionDetail,
      tseData,
      eventType
    );

    // Ausführliches Logging der TSE-Nachricht
    log.debug(
      `TSE: Vollständiger Nachrichteninhalt für ${eventType}:`,
      JSON.stringify(payload, null, 2)
    );

    // Nachricht veröffentlichen
    log.info(
      `Sende vereinheitlichte TSE-Nachricht (${eventType}) an Topic ${topic}`
    );
    mqttClient.publish(topic, JSON.stringify(payload), { qos: 1 });
    return true;
  } catch (error) {
    log.error(
      `Fehler beim Senden der TSE-Nachricht (${eventType}):`,
      error.message
    );
    return false;
  }
}

//  MQTT-Verbindungsfunktion mit dem verbesserten Client
async function setupMqttConnection() {
  try {
    if (!apiConfig) {
      log.warn("Keine API-Konfiguration für MQTT vorhanden");
      return;
    }

    let connectionTimeout = setTimeout(() => {
      log.warn(
        "MQTT: Globales Timeout für MQTT-Verbindung erreicht, fahre trotzdem fort"
      );
      if (!mainWindow || mainWindow.isDestroyed()) {
        createWindow();
      }
    }, 15000);

    // Initialisiere den Epson-Drucker mit kombinierter Konfiguration
    const combinedConfig = {
      ...apiConfig,
      api: config.api,
      printer: config.printer,
      fiskaly_config: config.fiskaly_config,
    };
    epsonPrinter = new EpsonPrinter(combinedConfig);

    // TSE-Client wurde bereits in startApp initialisiert
    // Hier keine erneute Initialisierung, um Doppelverbindungen zu vermeiden
    log.info("TSE-Client wurde bereits in startApp initialisiert, keine erneute Initialisierung");

    //  ZVT-Client initialisieren mit kombinierter Konfiguration
    // Kombiniere die API-Konfiguration mit den lokalen Händlerdaten
    // WICHTIG: Die Verbindungsdaten kommen ausschließlich aus der API-Konfiguration
    const combinedZvtConfig = {
      ...apiConfig
    };

    // Stelle sicher, dass die Verbindungsdaten aus der API-Konfiguration kommen
    if (apiConfig.zvt_config) {
      // Extrahiere die Händlerdaten aus der lokalen Konfiguration, falls vorhanden
      const merchantData = {};
      if (config.zvt_config) {
        if (config.zvt_config.merchant_name) merchantData.merchant_name = config.zvt_config.merchant_name;
        if (config.zvt_config.merchant_address) merchantData.merchant_address = config.zvt_config.merchant_address;
        if (config.zvt_config.merchant_zip_city) merchantData.merchant_zip_city = config.zvt_config.merchant_zip_city;
        if (config.zvt_config.merchant_line1) merchantData.merchant_line1 = config.zvt_config.merchant_line1;
        if (config.zvt_config.merchant_line2) merchantData.merchant_line2 = config.zvt_config.merchant_line2;
      }

      // Kombiniere die Verbindungsdaten aus der API-Konfiguration mit den Händlerdaten und Automatisierungseinstellungen
      combinedZvtConfig.zvt_config = {
        ...apiConfig.zvt_config,  // Verbindungsdaten aus der API-Konfiguration
        ...merchantData,          // Händlerdaten aus der lokalen Konfiguration
        // Automatisierungseinstellungen aus der lokalen Konfiguration
        auto_day_end: config.zvt_config?.auto_day_end === true,
        auto_merchant_receipt: config.zvt_config?.auto_merchant_receipt === true
      };

      log.info("ZVT-Konfiguration erstellt: Verbindungsdaten aus API, Händlerdaten aus lokaler Konfiguration");
      log.info(`Verwende Terminal-IP: ${combinedZvtConfig.zvt_config.zvt_ip}, Port: ${combinedZvtConfig.zvt_config.zvt_port}`);

      // Logging der Händlerdaten
      if (Object.keys(merchantData).length > 0) {
        log.info("Händlerdaten aus lokaler Konfiguration:");
        if (merchantData.merchant_name) log.info(`- Firmenname: ${merchantData.merchant_name}`);
        if (merchantData.merchant_address) log.info(`- Adresse: ${merchantData.merchant_address}`);
        if (merchantData.merchant_zip_city) log.info(`- PLZ/Ort: ${merchantData.merchant_zip_city}`);
        if (merchantData.merchant_line1) log.info(`- Zusatzzeile 1: ${merchantData.merchant_line1}`);
        if (merchantData.merchant_line2) log.info(`- Zusatzzeile 2: ${merchantData.merchant_line2}`);
      } else {
        log.info("Keine Händlerdaten in der lokalen Konfiguration gefunden");
      }
    } else {
      log.warn("Keine ZVT-Konfiguration in der API-Konfiguration gefunden");
    }

    zvtClient = new ZVTClient(combinedZvtConfig);

    // Epson-Drucker mit ZVT-Client verbinden
    if (epsonPrinter && zvtClient) {
      log.info("Setze Epson-Drucker für ZVT-Client");

      // Drucker-Adapter erstellen, falls nötig
      // ZVT-Drucker Integration wird später in setupZvtPrinterIntegration konfiguriert
      log.info(
        "Epson-Drucker für ZVT-Client wird in setupZvtPrinterIntegration konfiguriert"
      );

      // Drucker an ZVT-Client übergeben
      zvtClient.setEpsonPrinter(epsonPrinter);
      log.info("Epson-Drucker erfolgreich an ZVT-Client übergeben");
    }

    // WICHTIG: Hauptfenster explizit setzen
    if (mainWindow && !mainWindow.isDestroyed()) {
      log.info("Setze Hauptfenster-Referenz für ZVT-Client");
      zvtClient.setMainWindow(mainWindow);
    } else {
      log.warn("Hauptfenster nicht verfügbar für ZVT-Client");
    }

    // TSE-Verbindung ist bereits in startApp hergestellt worden
    // Hier nur noch die geplanten Zeitaktualisierungen einrichten
    try {
      log.info("TSE-Verbindung bereits hergestellt, richte geplante Zeitaktualisierungen ein");

      // Selbsttest nur durchführen, wenn er noch nicht gemacht wurde
      if (tseClient && tseClient.connected && !tseSelfTestPerformed) {
        // Führe den Selbsttest durch
        const selfTestResult = await tseClient.runSelfTest();
        tseSelfTestPerformed = true;

        if (selfTestResult) {
          log.info("TSE-Selbsttest erfolgreich");
        } else {
          log.warn("TSE-Selbsttest fehlgeschlagen");
        }
      }

      // Geplante Zeitaktualisierungen einrichten
      if (tseClient && tseClient.connected) {
        setupScheduledTimeUpdates(tseClient);
      }
    } catch (tseError) {
      log.error("Fehler bei TSE-Initialisierung:", tseError);
      log.warn("Anwendung wird ohne TSE-Unterstützung fortgesetzt");
    }

    //  Versuche ZVT-Terminal zu verbinden
    try {
      // Prüfe, ob die API-ZVT-Konfiguration vorhanden ist
      if (
        apiConfig.zvt_config &&
        apiConfig.zvt_config.zvt_ip &&
        apiConfig.zvt_config.zvt_port
      ) {
        log.info("Stelle Verbindung zum ZVT-EC-Terminal her...");
        log.info(`Verwende Terminal-IP aus API-Konfiguration: ${apiConfig.zvt_config.zvt_ip}, Port: ${apiConfig.zvt_config.zvt_port}`);

        const zvtConnected = await zvtClient.connect();
        if (zvtConnected) {
          log.info("ZVT-EC-Terminal erfolgreich verbunden");
        } else {
          log.warn(
            "ZVT-EC-Terminal konnte nicht verbunden werden, fahre trotzdem fort"
          );
        }
      } else {
        log.info(
          "Keine ZVT-Konfiguration in der API-Konfiguration gefunden, EC-Terminal wird nicht verwendet"
        );
      }
    } catch (zvtError) {
      log.error("Fehler bei ZVT-Terminal-Initialisierung:", zvtError);
      log.warn("Anwendung wird ohne ZVT-Terminal-Unterstützung fortgesetzt");
    }

    // Erstelle einen n verbesserten MQTT-Client
    const improvedClient = new ImprovedMQTTClient();

    try {
      // Initialisiere den Client mit der Konfiguration
      await improvedClient.initialize(config, apiConfig);
      log.info("MQTT: Verbindung erfolgreich initialisiert");

      // Timeout löschen, da die Verbindung erfolgreich hergestellt wurde
      if (connectionTimeout) {
        clearTimeout(connectionTimeout);
        connectionTimeout = null;
        log.info("MQTT: Verbindungs-Timeout gelöscht, da Verbindung erfolgreich");
      }

      // Registriere Handler für verschiedene Nachrichtentypen
      if (apiConfig.mqtt_config && apiConfig.mqtt_config.topics) {
        // Verwende die snake_case Schlüssel
        improvedClient.registerHandler("print_job_finished", (topic, data) => {
          handlePrintJobMessage(data);
        });

        // Handler für TSE-relevante Nachrichten
        improvedClient.registerHandler("transaction_created", (topic, data) => {
          handleTransactionCreated(data);
        });

        improvedClient.registerHandler("transaction_updated", (topic, data) => {
          handleTransactionUpdated(data);
        });

// Handler für Zahlungsnachrichten mit ZVT-Integration und separatem Modal-Fenster
improvedClient.registerHandler("payment_created", async (topic, data) => {
  handlePaymentMessage(data);

  // Extrahiere Zahlungsmethode aus den Daten
  let paymentMethod = data.params?.detail?.payment_method ||
                      data.params?.detail?.type ||
                      "UNKNOWN";

  // Detailliertes Logging für die empfangene Zahlungsmethode
  log.info(`Empfangene Zahlungsmethode: "${paymentMethod}" (Typ: ${typeof paymentMethod})`);

  // Prüfen, ob die Zahlungsmethode in den Payments-Array vorhanden ist
  if (data.params?.detail?.payments && Array.isArray(data.params.detail.payments)) {
    log.info(`Payments-Array gefunden mit ${data.params.detail.payments.length} Einträgen`);

    // Durchsuche den Payments-Array nach Barzahlungen
    const barPayment = data.params.detail.payments.find(p =>
      p.type === 'bar' ||
      (typeof p.type === 'string' && p.type.toLowerCase().includes('bar')) ||
      (p.name && typeof p.name === 'string' && p.name.toLowerCase().includes('bargeld'))
    );

    if (barPayment) {
      log.info(`Bargeldzahlung im Payments-Array gefunden: ${JSON.stringify(barPayment)}`);
      paymentMethod = 'CASH';
    }
  }

  // Transaktionsdaten extrahieren
  const transactionDetail = data.params?.detail || {};
  const transactionId = transactionDetail.id || "unknown";

  // Betrag korrekt extrahieren und als Zahl formatieren
  let amount = 0;
  if (transactionDetail.total !== undefined) {
    // Sicherstellen, dass amount eine Zahl ist
    amount =
      typeof transactionDetail.total === "string"
        ? parseFloat(transactionDetail.total.replace(",", "."))
        : transactionDetail.total;
  } else if (transactionDetail.given !== undefined) {
    amount =
      typeof transactionDetail.given === "string"
        ? parseFloat(transactionDetail.given.replace(",", "."))
        : transactionDetail.given;
  }

  // Debugging-Info ausgeben
  log.info(`Zahlungseingang erkannt: ${paymentMethod}, Betrag: ${amount}, Transaktion: ${transactionId}`);

  // TSE-Datenbank initialisieren, falls noch nicht geschehen
  try {
    if (tseClient && !tseClient.revisionDb) {
      log.info('Initialisiere Revisionsdatenbank für Zahlungsverfolgung');
      tseClient.revisionDb = new TseDatabase();
      await tseClient.revisionDb.initialize();
      log.info('Revisionsdatenbank erfolgreich initialisiert');
    }
  } catch (dbInitError) {
    log.error('Fehler bei der Initialisierung der Revisionsdatenbank:', dbInitError.message);
    // Wir setzen trotz des Fehlers fort, da wir Fallback-Logik haben
  }

  // Prüfen, ob eine Transaktion bereits existiert
  let existingTransaction = null;
  if (tseClient && tseClient.revisionDb) {
    try {
      const paymentStatus = await tseClient.revisionDb.checkPaymentTransaction(transactionId);
      if (paymentStatus.exists) {
        existingTransaction = paymentStatus;

        if (paymentStatus.completed) {
          log.info(`Zahlungstransaktion ${transactionId} wurde bereits erfolgreich abgeschlossen, wird übersprungen`);
          return; // Früher Ausstieg, wenn Transaktion bereits abgeschlossen
        } else {
          log.warn(`Zahlungstransaktion ${transactionId} existiert bereits, ist aber nicht abgeschlossen`);

          // Prüfen, ob die Transaktion zu alt ist
          const createdTime = paymentStatus.data.created_at;
          const currentTime = Math.floor(Date.now() / 1000);
          const timeSinceCreation = currentTime - createdTime;

          // Wenn die Transaktion älter als 10 Minuten ist, gilt sie als veraltet
          if (timeSinceCreation > 600) {
            log.warn(`Zahlungstransaktion ${transactionId} ist veraltet (${timeSinceCreation} Sekunden alt), wird als abgebrochen markiert`);
            await tseClient.revisionDb.updatePaymentTransaction(transactionId, {
              status: 'ABORTED',
              completed_at: currentTime
            });
          } else {
            // Die Transaktion ist noch aktiv, wir sollten sie nicht erneut starten
            log.info(`Zahlungstransaktion ${transactionId} ist noch aktiv (${timeSinceCreation} Sekunden alt), wird nicht erneut gestartet`);
            return;
          }
        }
      }
    } catch (dbError) {
      log.error("Fehler bei Datenbank-Prüfung der Zahlungstransaktion:", dbError.message);
      // Wir setzen trotz des Fehlers fort, mit einem Fallback
    }
  }

  // FALL 1: BARGELD-ZAHLUNG
  // Erweiterte Erkennung für verschiedene Bargeld-Bezeichnungen
if (paymentMethod.toUpperCase() === "CASH" ||
    paymentMethod.toLowerCase() === "bar" ||
    paymentMethod.toLowerCase().includes("bargeld")) {

  // Detailliertes Logging für die Zahlungsmethode
  log.info(`Bargeldzahlung erkannt mit Methode: "${paymentMethod}" (Typ: ${typeof paymentMethod})`);
  log.info(`Bargeldzahlung für Transaktion ${transactionId} mit Betrag ${amount} erkannt`);

  // Bargeldzahlung in Datenbank speichern
  if (tseClient && tseClient.revisionDb) {
    try {
      // Bei Bargeld-/alternativen Zahlungen prüfen, ob andere PENDING-Zahlungen für diese Transaktion existieren
      const parentTransactionId = transactionDetail.transaction_id;
      if (parentTransactionId) {
        log.info(`Prüfe auf offene Zahlungsversuche für Transaktion ${parentTransactionId}`);

        // Suche nach allen PENDING-Zahlungen für die übergeordnete Transaktion
        const pendingPayments = await tseClient.revisionDb.findPendingPayments(parentTransactionId);

        if (pendingPayments && pendingPayments.length > 0) {
          log.info(`${pendingPayments.length} offene Zahlungsversuche für Transaktion ${parentTransactionId} gefunden, markiere als abgebrochen`);

          // Markiere alle als abgebrochen
          for (const payment of pendingPayments) {
            if (payment.id !== transactionId) { // Nicht die aktuelle Zahlung markieren
              await tseClient.revisionDb.updatePaymentTransaction(payment.id, {
                status: 'ABORTED',
                completed_at: Math.floor(Date.now() / 1000)
              });
              log.info(`Zahlungsversuch ${payment.id} als abgebrochen markiert`);
            }
          }
        }
      }

      if (!existingTransaction || !existingTransaction.exists) {
        // Neue Transaktion speichern
        const paymentResult = await tseClient.revisionDb.savePaymentTransaction({
          transaction_id: transactionId,
          amount: amount,
          payment_method: 'CASH',
          status: 'COMPLETED', // Bargeld wird direkt als abgeschlossen markiert
          completed_at: Math.floor(Date.now() / 1000)
        });
        log.info(`Bargeldzahlung ${transactionId} in Datenbank gespeichert und als abgeschlossen markiert`);

        // Prüfen, ob es eine zugehörige TSE-Transaktion gibt
        try {
          // Suche nach TSE-Transaktion mit ähnlicher ID
          const tseTransaction = await tseClient.revisionDb.findTransactionByWizidId(transactionId);

          if (tseTransaction) {
            // Referenz zwischen TSE-Transaktion und Zahlungstransaktion speichern
            await tseClient.revisionDb.saveTransactionReference({
              tse_transaction_id: tseTransaction.tse_transaction_id,
              payment_transaction_id: transactionId,
              checkout_id: transactionDetail.checkout_id || transactionDetail.transaction_id || transactionId,
              reference_type: 'CASH_PAYMENT'
            });

            log.info(`Referenz zwischen TSE-Transaktion ${tseTransaction.tse_transaction_id} und Bargeldzahlung ${transactionId} gespeichert`);
          } else {
            log.info(`Keine passende TSE-Transaktion für Bargeldzahlung ${transactionId} gefunden`);
          }
        } catch (refError) {
          log.warn(`Fehler beim Speichern der Transaktionsreferenz für Bargeldzahlung: ${refError.message}`);
        }
      } else {
        // Bestehende Transaktion aktualisieren
        await tseClient.revisionDb.completePaymentTransaction(transactionId, {});
        log.info(`Bestehende Bargeldzahlung ${transactionId} als abgeschlossen markiert`);
      }
    } catch (dbError) {
      log.error(`Fehler beim Speichern der Bargeldzahlung ${transactionId}:`, dbError.message);
      log.error(`Details zur fehlgeschlagenen Bargeldzahlung: Methode=${paymentMethod}, Betrag=${amount}, TransaktionsID=${transactionId}`);

      // Versuche es erneut mit einem anderen payment_method Wert
      try {
        if (!existingTransaction || !existingTransaction.exists) {
          log.info(`Versuche erneut, Bargeldzahlung mit payment_method='BAR' zu speichern`);
          await tseClient.revisionDb.savePaymentTransaction({
            transaction_id: transactionId,
            amount: amount,
            payment_method: 'BAR',
            status: 'COMPLETED',
            completed_at: Math.floor(Date.now() / 1000)
          });
          log.info(`Bargeldzahlung ${transactionId} mit payment_method='BAR' erfolgreich gespeichert`);
        }
      } catch (retryError) {
        log.error(`Auch zweiter Versuch zum Speichern der Bargeldzahlung fehlgeschlagen:`, retryError.message);
      }
    }
  } else {
    log.warn(`TSE-Client oder Revisionsdatenbank nicht verfügbar, Bargeldzahlung ${transactionId} kann nicht gespeichert werden`);
  }
}
// FALL 2: KARTENZAHLUNG
else if (paymentMethod.toUpperCase() === "CARD" && zvtClient) {
  log.info(`Kartenzahlung für Transaktion ${transactionId} mit Betrag ${amount} erkannt`);

  try {
    // Neue Zahlungstransaktion speichern, wenn nicht bereits existiert
    if (tseClient && tseClient.revisionDb && (!existingTransaction || !existingTransaction.exists)) {
      // Extrahiere checkout_id aus den Transaktionsdetails
      const checkoutId = transactionDetail.checkout_id || transactionDetail.transaction_id || transactionId;
      log.info(`Verwende checkout_id "${checkoutId}" für Kartenzahlung ${transactionId}`);

      await tseClient.revisionDb.savePaymentTransaction({
        transaction_id: transactionId,
        amount: amount,
        payment_method: 'CARD',
        status: 'PENDING',
        checkout_id: checkoutId // Explizit checkout_id setzen
      });
      log.info(`Kartenzahlung ${transactionId} als 'pending' in Datenbank gespeichert mit checkout_id: "${checkoutId}"`);
    }

    // Modales Fenster anzeigen mit korrektem Betrag
    const paymentData = {
      amount: amount,
      transactionId: transactionId,
    };

    // Modales Fenster anzeigen
    createPaymentModalWindow(paymentData);

    // Kurze Verzögerung, dann Zahlung starten
    setTimeout(() => {
      // Dann die Zahlung starten
      zvtClient
        .handleMqttMessage(data)
        .then(async (result) => {
          if (result) {
            log.info(
              "ZVT-Zahlungsergebnis:",
              result.success ? "Erfolgreich" : "Fehlgeschlagen"
            );

            // Bei Erfolg die Quittungen im Log ausgeben und Transaktion abschließen
            if (result.success) {
              log.info("ZVT-Transaktions-ID:", result.transactionId);
              log.info("ZVT-Status-Code:", result.statusCode);
              log.info("ZVT-Belegnummer:", result.receiptNumber);

              // Zahlungstransaktion als abgeschlossen markieren
              if (tseClient && tseClient.revisionDb) {
                try {
                  // Stelle sicher, dass die checkout_id auch beim Abschließen der Transaktion gesetzt wird
                  const checkoutId = transactionDetail.checkout_id || transactionDetail.transaction_id || transactionId;
                  log.info(`Verwende checkout_id "${checkoutId}" beim Abschließen der Kartenzahlung ${transactionId}`);

                  await tseClient.revisionDb.completePaymentTransaction(transactionId, {
                    customer_receipt: JSON.stringify(result.customerReceipt || []),
                    merchant_receipt: JSON.stringify(result.merchantReceipt || []),
                    card_type: result.cardType || '',
                    card_number: result.cardNumber || '',
                    auth_code: result.authCode || '',
                    receipt_number: result.receiptNumber || '',
                    trace_number: result.traceNumber || '',
                    terminal_id: result.terminalId || '',
                    checkout_id: checkoutId // Explizit checkout_id setzen
                  });
                  log.info(`Kartenzahlung ${transactionId} als 'completed' in Datenbank markiert`);

                  // Prüfen, ob es eine zugehörige TSE-Transaktion gibt
                  try {
                    // Suche nach TSE-Transaktion mit ähnlicher ID
                    const tseTransaction = await tseClient.revisionDb.findTransactionByWizidId(transactionId);

                    if (tseTransaction) {
                      // Referenz zwischen TSE-Transaktion und Zahlungstransaktion speichern
                      await tseClient.revisionDb.saveTransactionReference({
                        tse_transaction_id: tseTransaction.tse_transaction_id,
                        payment_transaction_id: transactionId,
                        checkout_id: transactionDetail.checkout_id || transactionDetail.transaction_id || transactionId,
                        reference_type: 'CARD_PAYMENT'
                      });

                      log.info(`Referenz zwischen TSE-Transaktion ${tseTransaction.tse_transaction_id} und Kartenzahlung ${transactionId} gespeichert`);
                    } else {
                      log.info(`Keine passende TSE-Transaktion für Kartenzahlung ${transactionId} gefunden`);
                    }
                  } catch (refError) {
                    log.warn(`Fehler beim Speichern der Transaktionsreferenz für Kartenzahlung: ${refError.message}`);
                  }
                } catch (dbError) {
                  log.error(`Fehler beim Abschließen der Kartenzahlung ${transactionId} in Datenbank:`, dbError.message);
                }
              }

              // Modal aktualisieren - mit korrektem Betrag
              updatePaymentModal({
                status: "success",
                message: "Zahlung erfolgreich!",
                amount: amount,
                ...result,
              });
            } else {
              log.warn("ZVT-Zahlungsfehler:", result.error);

              // Zahlungstransaktion als fehlgeschlagen markieren
              if (tseClient && tseClient.revisionDb) {
                try {
                  const failureStatus = result.statusCode === "0C" ? 'ABORTED' : 'FAILED';
                  await tseClient.revisionDb.updatePaymentTransaction(transactionId, {
                    status: failureStatus,
                    completed_at: Math.floor(Date.now() / 1000)
                  });
                  log.info(`Kartenzahlung ${transactionId} als '${failureStatus}' in Datenbank markiert`);
                } catch (dbError) {
                  log.error(`Fehler beim Markieren der fehlgeschlagenen Kartenzahlung ${transactionId} in Datenbank:`, dbError.message);
                }
              }

              // Modal mit Fehler aktualisieren - mit korrektem Betrag
              updatePaymentModal({
                status: "error",
                message:
                  "Fehler: " + (result.error || "Unbekannter Fehler"),
                amount: amount,
                ...result,
              });
            }
          }
        })
        .catch(async (error) => {
          log.error(
            "Fehler bei ZVT-Zahlungsverarbeitung:",
            error.message
          );

          // Zahlungstransaktion als fehlgeschlagen markieren
          if (tseClient && tseClient.revisionDb) {
            try {
              await tseClient.revisionDb.updatePaymentTransaction(transactionId, {
                status: 'ERROR',
                completed_at: Math.floor(Date.now() / 1000)
              });
              log.info(`Kartenzahlung ${transactionId} wegen Fehler als 'ERROR' in Datenbank markiert`);
            } catch (dbError) {
              log.error(`Fehler beim Markieren der fehlerhaften Kartenzahlung ${transactionId} in Datenbank:`, dbError.message);
            }
          }

          // Fehler im Modal anzeigen
          updatePaymentModal({
            status: "error",
            message: "Fehler: " + error.message,
            amount: amount,
            transactionId: transactionId,
          });
        });
    }, 100);
  } catch (error) {
    log.error(`Kritischer Fehler bei Kartenzahlung ${transactionId}:`, error.message);

    // Bei kritischen Fehlern Payment-Modal trotzdem anzeigen mit Fehlermeldung
    const paymentData = {
      amount: amount,
      transactionId: transactionId,
    };

    createPaymentModalWindow(paymentData);

    updatePaymentModal({
      status: "error",
      message: "Kritischer Fehler: " + error.message,
      amount: amount,
      transactionId: transactionId,
    });
  }
}
// FALL 3: ANDERE ZAHLUNGSMETHODEN
else {
  // Detailliertes Logging für die Zahlungsmethode
  log.info(`Zahlung mit Methode "${paymentMethod}" (Typ: ${typeof paymentMethod}) für Transaktion ${transactionId} mit Betrag ${amount} erkannt`);

  // Prüfen, ob es sich um eine Barzahlung handeln könnte, die nicht als solche erkannt wurde
  const paymentMethodLower = String(paymentMethod).toLowerCase();
  if (paymentMethodLower.includes('bar') || paymentMethodLower.includes('cash') || paymentMethodLower.includes('geld')) {
    log.info(`Mögliche Bargeldzahlung erkannt in Methode "${paymentMethod}", behandle als Bargeld`);
    paymentMethod = 'CASH';
  }

  // Andere Zahlungsmethoden auch in Datenbank speichern
  if (tseClient && tseClient.revisionDb) {
    try {
      // Bei alternativen Zahlungen prüfen, ob andere PENDING-Zahlungen für diese Transaktion existieren
      const parentTransactionId = transactionDetail.transaction_id;
      if (parentTransactionId) {
        log.info(`Prüfe auf offene Zahlungsversuche für Transaktion ${parentTransactionId}`);

        // Suche nach allen PENDING-Zahlungen für die übergeordnete Transaktion
        const pendingPayments = await tseClient.revisionDb.findPendingPayments(parentTransactionId);

        if (pendingPayments && pendingPayments.length > 0) {
          log.info(`${pendingPayments.length} offene Zahlungsversuche für Transaktion ${parentTransactionId} gefunden, markiere als abgebrochen`);

          // Markiere alle als abgebrochen
          for (const payment of pendingPayments) {
            if (payment.id !== transactionId) { // Nicht die aktuelle Zahlung markieren
              await tseClient.revisionDb.updatePaymentTransaction(payment.id, {
                status: 'ABORTED',
                completed_at: Math.floor(Date.now() / 1000)
              });
              log.info(`Zahlungsversuch ${payment.id} als abgebrochen markiert`);
            }
          }
        }
      }

      if (!existingTransaction || !existingTransaction.exists) {
        // Neue Transaktion speichern
        const paymentResult = await tseClient.revisionDb.savePaymentTransaction({
          transaction_id: transactionId,
          amount: amount,
          payment_method: paymentMethod || 'OTHER',
          status: 'COMPLETED', // Andere Zahlungsmethoden werden direkt als abgeschlossen markiert
          completed_at: Math.floor(Date.now() / 1000)
        });
        log.info(`Zahlung mit Methode ${paymentMethod} für ${transactionId} in Datenbank gespeichert und als abgeschlossen markiert`);

        // Prüfen, ob es eine zugehörige TSE-Transaktion gibt
        try {
          // Suche nach TSE-Transaktion mit ähnlicher ID
          const tseTransaction = await tseClient.revisionDb.findTransactionByWizidId(transactionId);

          if (tseTransaction) {
            // Referenz zwischen TSE-Transaktion und Zahlungstransaktion speichern
            await tseClient.revisionDb.saveTransactionReference({
              tse_transaction_id: tseTransaction.tse_transaction_id,
              payment_transaction_id: transactionId,
              checkout_id: transactionDetail.checkout_id || transactionDetail.transaction_id || transactionId,
              reference_type: 'OTHER_PAYMENT'
            });

            log.info(`Referenz zwischen TSE-Transaktion ${tseTransaction.tse_transaction_id} und Zahlung ${transactionId} gespeichert`);
          } else {
            log.info(`Keine passende TSE-Transaktion für Zahlung ${transactionId} gefunden`);
          }
        } catch (refError) {
          log.warn(`Fehler beim Speichern der Transaktionsreferenz für Zahlung: ${refError.message}`);
        }
      } else {
        // Bestehende Transaktion aktualisieren
        await tseClient.revisionDb.completePaymentTransaction(transactionId, {});
        log.info(`Bestehende Zahlung ${transactionId} mit Methode ${paymentMethod} als abgeschlossen markiert`);
      }
    } catch (dbError) {
      log.error(`Fehler beim Speichern der Zahlung ${transactionId} mit Methode ${paymentMethod}:`, dbError.message);
    }
  }
}
});

        // Handler für Zahlungsaktualisierungen
improvedClient.registerHandler("payment_updated", async (topic, data) => {
  handlePaymentMessage(data);

  // Wenn die Zahlungsmethode nicht explizit übergeben wird, ignorieren wir die Nachricht nicht
  let paymentMethod = data.params?.detail?.payment_method || data.params?.detail?.type || "UNKNOWN";

  // Transaktionsdaten extrahieren
  const transactionDetail = data.params?.detail || {};
  const transactionId = transactionDetail.id || "unknown";
  const paymentStatus = transactionDetail.status || "unknown";

  // Detailliertes Logging für die Zahlungsmethode
  log.info(`Zahlungsaktualisierung erkannt: Methode="${paymentMethod}" (Typ: ${typeof paymentMethod}), Status: ${paymentStatus}, Transaktion: ${transactionId}`);

  // Prüfen, ob es sich um eine Barzahlung handeln könnte, die nicht als solche erkannt wurde
  const paymentMethodLower = String(paymentMethod).toLowerCase();
  if (paymentMethodLower.includes('bar') || paymentMethodLower.includes('cash') || paymentMethodLower.includes('geld')) {
    log.info(`Mögliche Bargeldzahlung in Aktualisierung erkannt in Methode "${paymentMethod}", behandle als Bargeld`);
    paymentMethod = 'CASH';
  }

  try {
    // TSE-Datenbank initialisieren, falls noch nicht geschehen
    if (tseClient && !tseClient.revisionDb) {
      log.info('Initialisiere Revisionsdatenbank für Zahlungsverfolgung');
      tseClient.revisionDb = new TseDatabase();
      await tseClient.revisionDb.initialize();
    }

    if (tseClient && tseClient.revisionDb) {
      // Status der Zahlung in der Datenbank aktualisieren
      if (paymentStatus === "aborted" || paymentStatus === "failed") {
        log.info(`Zahlungstransaktion ${transactionId} wurde abgebrochen oder ist fehlgeschlagen, aktualisiere Datenbank`);

        await tseClient.revisionDb.updatePaymentTransaction(transactionId, {
          status: 'ABORTED',
          completed_at: Math.floor(Date.now() / 1000)
        });

        log.info(`Zahlungstransaktion ${transactionId} wurde als ABORTED markiert`);
      }
      else if (paymentStatus === "completed" || paymentStatus === "finished") {
        log.info(`Zahlungstransaktion ${transactionId} wurde abgeschlossen, aktualisiere Datenbank`);

        await tseClient.revisionDb.updatePaymentTransaction(transactionId, {
          status: 'COMPLETED',
          completed_at: Math.floor(Date.now() / 1000)
        });

        log.info(`Zahlungstransaktion ${transactionId} wurde als COMPLETED markiert`);
      }
    } else {
      log.warn(`TSE-Client oder Revisionsdatenbank nicht verfügbar, kann Zahlungsstatus nicht aktualisieren`);
    }
  } catch (error) {
    log.error(`Fehler bei der Aktualisierung des Zahlungsstatus in der Datenbank:`, error.message);
  }
});

        improvedClient.registerHandler("tse_data", (topic, data) => {
          handleTseMessage(data, topic);
        });

        improvedClient.registerHandler("tse_finish", (topic, data) => {
          handleTseMessage(data, topic);
        });
      } else if (apiConfig.MqttConfig && apiConfig.MqttConfig.Topics) {
        // Für PascalCase-Format würden wir hier ähnliche Handler registrieren
        // mit den entsprechenden PascalCase-Schlüsseln
        improvedClient.registerHandler("PrintJobFinished", (topic, data) => {
          handlePrintJobMessage(data);
        });
        // ...weitere Nachrichtentypen
      }

      // Alten Client ersetzen
      if (mqttClient) {
        try {
          // Prüfen, ob der alte Client eine .disconnect() oder .end() Methode hat
          if (typeof mqttClient.disconnect === "function") {
            mqttClient.disconnect();
          } else if (typeof mqttClient.end === "function") {
            mqttClient.end(true);
          }
        } catch (e) {
          log.warn("Fehler beim Beenden des alten MQTT-Clients:", e);
        }
      }

      // Client als globale Variable setzen, damit er in anderen Modulen verfügbar ist
      mqttClient = improvedClient;
      global.mqttClient = improvedClient;

      // Starte die Ping-Überwachung für den Client
      improvedClient.startPingMonitor(30000);
    } catch (connectionError) {
      log.error("MQTT: Verbindung fehlgeschlagen:", connectionError);

      // Benachrichtige das Hauptfenster über den Fehler
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send("mqtt-connection-error", {
          error: connectionError.message || "Verbindung fehlgeschlagen",
        });
      }
    }
    if (epsonPrinter) {
      log.info("Drucker-Diagnose:");
      log.info("- Drucker-Objekt vorhanden: " + (epsonPrinter !== null));
      log.info(
        "- Verfügbare Methoden: " + Object.keys(epsonPrinter).join(", ")
      );
    }

    if (zvtClient) {
      log.info("ZVT-Client Diagnose:");
      log.info("- ZVT-Client Objekt vorhanden: " + (zvtClient !== null));
      log.info("- Drucker gesetzt: " + (zvtClient.epsonPrinter !== undefined));
    }
    setupZvtPrinterIntegration();
  } catch (error) {
    log.error("Fehler beim Einrichten der MQTT-Verbindung:", error);
  }
}

/**
 * Richtet geplante TSE-Zeitaktualisierungen ein
 * @param {Object} tseClient - Der initialisierte TSE-Client
 */
function setupScheduledTimeUpdates(tseClient) {
  if (!tseClient) {
    log.error('TSE-Client nicht initialisiert, geplante Zeitaktualisierungen werden nicht eingerichtet');
    return;
  }

  log.info('Richte geplante TSE-Zeitaktualisierungen ein (8 Uhr morgens und 8 Uhr abends)');

  // Funktion zur Berechnung der Millisekunden bis zum nächsten Zeitpunkt
  function getMillisToNextTime(hour) {
    const now = new Date();
    const target = new Date(now);

    target.setHours(hour, 0, 0, 0); // Zielzeit auf 8:00:00 oder 20:00:00 setzen

    // Wenn die Zielzeit heute bereits vorbei ist, auf morgen setzen
    if (now > target) {
      target.setDate(target.getDate() + 1);
    }

    return target.getTime() - now.getTime();
  }

  // Funktion zur Durchführung der Zeitaktualisierung
  async function performTimeUpdate() {
    try {
      log.info('Führe geplante TSE-Zeitaktualisierung durch...');

      // Sicherstellen, dass der TSE-Client verbunden ist
      if (!tseClient.connected) {
        log.warn('TSE-Client nicht verbunden, versuche erneut zu verbinden...');
        await tseClient.connect();
      }

      // Zeitaktualisierung durchführen
      const updateResult = await tseClient.updateTime();

      if (updateResult) {
        log.info('Geplante TSE-Zeitaktualisierung erfolgreich durchgeführt');
      } else {
        log.error('Geplante TSE-Zeitaktualisierung fehlgeschlagen');
      }
    } catch (error) {
      log.error('Fehler bei geplanter TSE-Zeitaktualisierung:', error.message);
    }

    // Nächste Aktualisierung planen
    scheduleNextUpdate();
  }

  // Funktion zum Planen der nächsten Aktualisierung
  function scheduleNextUpdate() {
    // Bestimme, ob die Morgen- oder Abendaktualisierung als nächstes ansteht
    const millisToMorning = getMillisToNextTime(8);  // 8 Uhr morgens
    const millisToEvening = getMillisToNextTime(20); // 8 Uhr abends

    const nextUpdateTime = millisToMorning < millisToEvening ? millisToMorning : millisToEvening;
    const nextUpdateType = millisToMorning < millisToEvening ? "Morgen" : "Abend";

    // Berechne die Zeit in Stunden und Minuten für das Log
    const hours = Math.floor(nextUpdateTime / (1000 * 60 * 60));
    const minutes = Math.floor((nextUpdateTime % (1000 * 60 * 60)) / (1000 * 60));

    log.info(`Nächste TSE-Zeitaktualisierung (${nextUpdateType}) in ${hours} Stunden und ${minutes} Minuten geplant`);

    // Aktualisierungs-Timeout setzen
    global.nextTseTimeUpdateTimeout = setTimeout(performTimeUpdate, nextUpdateTime);
  }

  // Initialen Zeitplan starten
  scheduleNextUpdate();

  // Handler für Anwendungsende hinzufügen, um den Timeout zu bereinigen
  app.on('before-quit', () => {
    if (global.nextTseTimeUpdateTimeout) {
      clearTimeout(global.nextTseTimeUpdateTimeout);
      log.info('Geplante TSE-Zeitaktualisierung aufgeräumt');
    }
  });
}

function setupZvtPrinterIntegration() {
  if (!epsonPrinter || !zvtClient) {
    log.warn(
      "Epson-Drucker oder ZVT-Client nicht verfügbar, Druckerintegration nicht möglich"
    );
    return;
  }

  log.info("Konfiguriere ZVT-Client für Epson-Drucker Integration");

  // Adapter für die korrekte Druckfunktionalität
  epsonPrinter.sendToPrinter = async function (xmlData) {
    try {
      log.info("Sende ZVT-Beleg direkt an den Drucker");
      // Direkte Druckmethode verwenden, die für normale Kassenbons funktioniert
      return await this.printXML(xmlData);
    } catch (error) {
      log.error("Druckfehler:", error.message);
      return { success: false, error: error.message };
    }
  };

  zvtClient.setEpsonPrinter(epsonPrinter);
  log.info("ZVT-Drucker-Integration abgeschlossen");
}

setupZvtPrinterIntegration();

// Behandle Transaktionsnachrichten
function handleTransactionMessage(data) {
  log.info(
    "Transaktion erhalten:",
    data.id ||
      (data.params && data.params.detail && data.params.detail.id) ||
      "Keine ID"
  );

  // Sende die Nachricht an das Hauptfenster für eventuelle UI-Updates
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send("mqtt-transaction", data);
  }
}

function handlePaymentMessage(data) {
  log.info(
    "Zahlung erhalten:",
    data.id ||
      (data.params && data.params.detail && data.params.detail.id) ||
      "Keine ID"
  );

  // Sende die Nachricht an das Hauptfenster für eventuelle UI-Updates
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send("mqtt-payment", data);
  }
}

// Behandle Druckauftragsnachrichten
async function handlePrintJobMessage(data) {
  const printJobId = data.params ? data.params.print_job_id : "Keine ID";
  log.info("Druckauftrag erhalten:", printJobId);

  // Prüfe, ob der Epson-Drucker initialisiert ist
  if (!epsonPrinter) {
    log.warn("Epson-Drucker nicht initialisiert, initialisiere jetzt");
    // Kombiniere die lokale Konfiguration mit der API-Konfiguration für den Drucker
    const combinedConfig = {
      ...apiConfig,
      api: config.api,
      printer: config.printer,
    };
    epsonPrinter = new EpsonPrinter(combinedConfig);
  }

  try {
    // Prüfe die tatsächliche Verbindung zum Drucker mit Ping
    log.info("Prüfe Verbindung zum Drucker vor dem Drucken...");
    const printerConnected = await epsonPrinter.checkPrinterConnection();

    if (!printerConnected) {
      log.warn("Drucker nicht erreichbar, versuche trotzdem zu drucken");

      // Benachrichtige das Hauptfenster über den Verbindungsstatus
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send("printer-status", {
          connected: false,
          message: "Drucker nicht erreichbar"
        });
      }
    } else {
      log.info("Drucker erfolgreich verbunden und erreichbar");

      // Benachrichtige das Hauptfenster über den Verbindungsstatus
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send("printer-status", {
          connected: true,
          message: "Drucker verbunden"
        });
      }
    }

    // Verarbeite den Druckauftrag mit dem Epson-Drucker
    const result = await epsonPrinter.processPrintJobMessage(data);
    log.info("Druckauftrag verarbeitet:", result);

    // Sende die Benachrichtigung an das Hauptfenster
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send("print-completed", {
        success: result.success,
        printJobId,
        message: result.message || result.error,
      });
    }

    return result;
  } catch (error) {
    log.error("Fehler bei der Verarbeitung des Druckauftrags:", error);

    // Sende die Fehlermeldung an das Hauptfenster
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send("print-error", {
        printJobId,
        error: error.message,
      });
    }

    return { success: false, error: error.message };
  }
}

function determineReceiptType(transactionSubType, tseEntry) {
  // Zuerst aus tse_entry den VorgangsTyp extrahieren, wenn vorhanden
  let vorgangsTyp = 1; // Default: Beleg

  if (tseEntry) {
    const parts = tseEntry.split(",");
    if (parts.length >= 2) {
      vorgangsTyp = parseInt(parts[1], 10) || 1;
      log.info(`VorgangsTyp aus tse_entry ermittelt: ${vorgangsTyp}`);
    }
  }

  // Mapping des VorgangsTyps zu Fiskaly Receipt-Type
  switch (vorgangsTyp) {
    case 1: // Beleg
      // Standardverkauf
      return "RECEIPT";

    case 2: // AVTransfer
      // Geldtransit (Einnahmen/Ausgaben ohne Umsatz)
      return "TRANSFER";

    case 3: // AVBestellung
      // Bestellung die erst später abgerechnet wird
      return "ORDER";

    case 4: // AVTraining
      // Trainingsbeleg / Testmodus
      return "TRAINING";

    case 5: // AVBelegstorno (veraltet ab DSFinV-K 2.1)
      // Stornierung eines Belegs
      return "CANCELLATION";

    case 6: // AVBelegabbruch
      // Abbruch eines Vorgangs
      return "ABORT";

    case 7: // AVSachbezug
      // Sachbezüge (z.B. Mitarbeiteressen)
      return "BENEFIT_IN_KIND";

    case 8: // AVSonstige
      // Sonstige Vorgänge
      return "OTHER";

    case 9: // AVRechnung
      // Rechnung
      return "INVOICE";

    default:
      // Unbekannter VorgangsTyp, Fallback auf Basis des transactionSubType
      break;
  }

  // Sekundäres Mapping basierend auf dem transaction_sub_type
  // Falls der VorgangsTyp nicht erkannt wurde oder nicht im tse_entry vorhanden ist
  switch (transactionSubType) {
    case "Anfangsbestand":
      return "CASH_DEPOSIT"; // Bargeldzuführung

    case "Entnahme":
      return "TRANSFER"; // Geldtransit (negativ)

    case "Abschluss":
      return "TRANSFER"; // Geldtransit für Kassenabschluss

    case "Verkauf":
      return "RECEIPT"; // Standardverkauf

    case "Storno":
      return "CANCELLATION"; // Stornierung

    case "Erstattung":
      return "REFUND"; // Rückerstattung

    case "Gutschein":
      return "VOUCHER"; // Gutscheinausgabe/-einlösung

    case "Anzahlung":
      return "DEPOSIT"; // Anzahlung

    case "Rechnung":
      return "INVOICE"; // Rechnungsstellung

    case "Training":
    case "Test":
      return "TRAINING"; // Trainingsmodus

    case "Pfand":
      return "CONTAINER_DEPOSIT"; // Pfandbeleg

    default:
      // Fallback für unbekannte Typen
      return "RECEIPT";
  }
}

/**
 * Funktion zur Bestimmung des korrekten Process-Types für die Fiskaly API
 *
 * @param {string} tseEntry - Der tse_entry String mit den Prozessdaten
 * @returns {string} Der passende Process-Type für die Fiskaly API
 */
function determineProcessType(tseEntry) {
  // Default: Kassenbeleg-V1
  let processType = "Kassenbeleg-V1";

  if (tseEntry) {
    const parts = tseEntry.split(",");
    if (parts.length >= 1) {
      const processTypeNumber = parseInt(parts[0], 10) || 1;

      switch (processTypeNumber) {
        case 1:
          return "Kassenbeleg-V1";

        case 2:
          return "BestellungV1";

        case 3:
          return "SonstigerVorgang";

        default:
          return "Kassenbeleg-V1";
      }
    }
  }

  return processType;
}

/**
 * Funktion zur Bestimmung der passenden VAT-Rate Bezeichnung für die Fiskaly API
 *
 * @param {number} taxRate - Der Steuersatz (z.B. 19, 7, 0)
 * @returns {string} Die passende VAT-Rate Bezeichnung für die Fiskaly API
 */
function determineVatRateType(taxRate) {
  // Umwandlung von numerischen MwSt-Sätzen in Fiskaly API String-Format
  switch (taxRate) {
    case 19:
      return "NORMAL";

    case 16:
      return "NORMAL"; // Temporär während COVID (kann je nach Zeitraum angepasst werden)

    case 7:
      return "REDUCED_1";

    case 5:
      return "REDUCED_1"; // Temporär während COVID (kann je nach Zeitraum angepasst werden)

    case 10.7:
      return "SPECIAL_RATE_1"; // Durchschnittssatz Land- und Forstwirtschaft

    case 5.5:
      return "SPECIAL_RATE_2"; // Durchschnittssatz Land- und Forstwirtschaft

    case 0:
      return "NULL"; // 0% MwSt

    default:
      // Wenn kein bekannter Steuersatz, verwenden wir "NULL"
      return "NULL";
  }
}

// Behandle TSE-Nachrichten
function handleTseMessage(data, topic) {
  log.info("TSE-Nachricht erhalten auf Topic:", topic);
  // Hier noch eine Logik für den Umgang mit TSE-Nachrichten implementieren

  // Sende die Nachricht an das Hauptfenster für eventuelle UI-Updates
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send("mqtt-tse", { topic, data });
  }
}

// Hauptfenster erstellen
function createWindow() {
  // Prüfen, ob alle erforderlichen Komponenten bereit sind
  if (!allRequiredComponentsReady() && !userOverrideRequirements) {
    log.warn('createWindow() aufgerufen, aber nicht alle erforderlichen Komponenten sind bereit');
    return;
  }

  log.info('Erstelle Hauptfenster mit Webview...');

  // Browser-Fenster erstellen
  mainWindow = new BrowserWindow({
    width: config.windowSettings.width,
    height: config.windowSettings.height,
    fullscreen: config.windowSettings.fullscreen,
    kiosk: config.windowSettings.kiosk,
    autoHideMenuBar: !config.windowSettings.showMenuBar,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, "preload.js"),
    },
  });

  // Hauptfenster als globale Variable setzen für ZVT-Client und Modal
  global.mainWindow = mainWindow;

  // Menüleiste explizit ausblenden
  mainWindow.setMenuBarVisibility(config.windowSettings.showMenuBar === true);

  // Direkter Event-Listener für Rechtsklick im Hauptprozess
  mainWindow.webContents.on('context-menu', (event, params) => {
    log.info('Rechtsklick im Hauptprozess erkannt');

    // Menüleiste umschalten
    const isVisible = mainWindow.isMenuBarVisible();
    log.info(`Menüleiste wird ${isVisible ? 'ausgeblendet' : 'eingeblendet'} (direkt aus context-menu-Event)`);
    mainWindow.setMenuBarVisibility(!isVisible);

    // Event verhindern, damit das Standard-Kontextmenü nicht angezeigt wird
    event.preventDefault();
  });

  // Touch-Gesten für Menüleiste hinzufügen
  mainWindow.webContents.on('did-finish-load', () => {
    // JavaScript-Code, der im Hauptfenster ausgeführt wird, um Touch-Gesten zu erkennen
    mainWindow.webContents.executeJavaScript(`
      // Event-Listener für postMessage-Kommunikation vom shopView
      window.addEventListener('message', (event) => {
        if (event.data === 'toggle-menu-bar') {
          window.electronAPI.toggleMenuBar();
        }
      });
      // Variablen für Touch-Erkennung
      let touchStartTime = 0;
      let touchCount = 0;

      // Touch-Start-Event-Handler
      document.addEventListener('touchstart', (event) => {
        touchCount = event.touches.length;
        touchStartTime = Date.now();
      });

      // Touch-Ende-Event-Handler
      document.addEventListener('touchend', () => {
        // Zwei-Finger-Touch erkennen
        if (touchCount === 2 && (Date.now() - touchStartTime) < 500) {
          // Zwei-Finger-Touch erkannt, Menüleiste umschalten
          window.electronAPI.toggleMenuBar();
        }

        touchCount = 0;
      });

      // Rechtsklick-Event-Handler
      document.addEventListener('contextmenu', (event) => {
        // Rechtsklick erkannt, Menüleiste umschalten
        console.log('Rechtsklick im Hauptfenster erkannt');

        // Direkt IPC-Nachricht senden, um sicherzustellen, dass sie ankommt
        if (window.electronAPI && typeof window.electronAPI.toggleMenuBar === 'function') {
          window.electronAPI.toggleMenuBar();
          console.log('toggleMenuBar über electronAPI aufgerufen');
        } else {
          console.error('electronAPI.toggleMenuBar ist nicht verfügbar');
          // Fallback: Versuche, die Nachricht direkt zu senden
          try {
            const ipcRenderer = window.require ? window.require('electron').ipcRenderer : null;
            if (ipcRenderer) {
              ipcRenderer.send('toggle-menu-bar');
              console.log('toggleMenuBar über ipcRenderer aufgerufen');
            } else {
              console.error('ipcRenderer ist nicht verfügbar');
            }
          } catch (e) {
            console.error('Fehler beim Senden der Nachricht:', e);
          }
        }

        // Standard-Kontextmenü verhindern
        event.preventDefault();
        return false;
      });

      console.log('Touch-Gesten für Menüleiste wurden initialisiert');
    `);
  });

  // BrowserView für den Shop erstellen
  shopView = new BrowserView({
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      passwordManager: true,
      webSecurity: false,
      enableRemoteModule: true,
      partition: "persist:shop",
      autoplayPolicy: 'user-gesture-required'
    },
  });

  // Autofill-Funktionalität wurde entfernt, da sie nicht mehr benötigt wird

  // BrowserView zum Fenster hinzufügen
  mainWindow.setBrowserView(shopView);

  // BrowserView über das gesamte Fenster setzen
  const { width, height } = mainWindow.getContentBounds();
  shopView.setBounds({ x: 0, y: 0, width, height });
  shopView.setAutoResize({ width: true, height: true });

  // Bei Fenstergrößenänderung BrowserView anpassen
  mainWindow.on("resize", () => {
    const { width, height } = mainWindow.getContentBounds();
    shopView.setBounds({ x: 0, y: 0, width, height });
  });

  // POS-URL aus der API-Konfiguration laden
  let posUrl = config.fallbackUrl;

  if (apiConfig) {
    if (apiConfig.SystemConfig && apiConfig.SystemConfig.PosUrl) {
      // PascalCase Format
      posUrl = apiConfig.SystemConfig.PosUrl;
    } else if (apiConfig.system_config && apiConfig.system_config.pos_url) {
      // snake_case Format
      posUrl = apiConfig.system_config.pos_url;
    }
  }

  log.info('Lade POS-URL:', posUrl);
  shopView.webContents.loadURL(posUrl);

  // Direkter Event-Listener für Rechtsklick im shopView
  shopView.webContents.on('context-menu', (event, params) => {
    log.info('Rechtsklick im shopView-Prozess erkannt');

    // Menüleiste umschalten
    if (mainWindow && !mainWindow.isDestroyed()) {
      const isVisible = mainWindow.isMenuBarVisible();
      log.info(`Menüleiste wird ${isVisible ? 'ausgeblendet' : 'eingeblendet'} (direkt aus shopView context-menu-Event)`);
      mainWindow.setMenuBarVisibility(!isVisible);
    }

    // Event verhindern, damit das Standard-Kontextmenü nicht angezeigt wird
    event.preventDefault();
  });

  // Touch-Gesten auch für shopView hinzufügen
  shopView.webContents.on('did-finish-load', () => {
    // JavaScript-Code, der im shopView ausgeführt wird, um Touch-Gesten zu erkennen
    shopView.webContents.executeJavaScript(`
      // Variablen für Touch-Erkennung
      let touchStartTime = 0;
      let touchCount = 0;

      // Touch-Start-Event-Handler
      document.addEventListener('touchstart', (event) => {
        touchCount = event.touches.length;
        touchStartTime = Date.now();
      });

      // Touch-Ende-Event-Handler
      document.addEventListener('touchend', () => {
        // Zwei-Finger-Touch erkennen
        if (touchCount === 2 && (Date.now() - touchStartTime) < 500) {
          // Zwei-Finger-Touch erkannt, Menüleiste umschalten
          const customEvent = new CustomEvent('toggle-menu-bar');
          window.dispatchEvent(customEvent);
        }

        touchCount = 0;
      });

      // Rechtsklick-Event-Handler
      document.addEventListener('contextmenu', (event) => {
        // Rechtsklick erkannt, Menüleiste umschalten
        console.log('Rechtsklick im shopView erkannt');

        // Direkt IPC-Nachricht senden, um sicherzustellen, dass sie ankommt
        if (window.electronAPI && typeof window.electronAPI.toggleMenuBar === 'function') {
          window.electronAPI.toggleMenuBar();
          console.log('toggleMenuBar über electronAPI im shopView aufgerufen');
        } else {
          console.error('electronAPI.toggleMenuBar ist im shopView nicht verfügbar');
          // Fallback: Versuche, die Nachricht direkt zu senden
          try {
            const ipcRenderer = window.require ? window.require('electron').ipcRenderer : null;
            if (ipcRenderer) {
              ipcRenderer.send('toggle-menu-bar');
              console.log('toggleMenuBar über ipcRenderer im shopView aufgerufen');
            } else {
              console.error('ipcRenderer ist im shopView nicht verfügbar');
              // Fallback: Versuche, die Nachricht über ein benutzerdefiniertes Event zu senden
              const customEvent = new CustomEvent('toggle-menu-bar');
              window.dispatchEvent(customEvent);
              console.log('toggleMenuBar über customEvent im shopView aufgerufen');
            }
          } catch (e) {
            console.error('Fehler beim Senden der Nachricht im shopView:', e);
            // Fallback: Versuche, die Nachricht über ein benutzerdefiniertes Event zu senden
            const customEvent = new CustomEvent('toggle-menu-bar');
            window.dispatchEvent(customEvent);
            console.log('toggleMenuBar über customEvent im shopView aufgerufen (nach Fehler)');
          }
        }

        // Standard-Kontextmenü verhindern
        event.preventDefault();
        return false;
      });

      // Event-Listener für das benutzerdefinierte Event hinzufügen
      window.addEventListener('toggle-menu-bar', () => {
        // Nachricht an den Hauptprozess senden
        if (window.electronAPI && window.electronAPI.toggleMenuBar) {
          window.electronAPI.toggleMenuBar();
        } else {
          console.log('electronAPI.toggleMenuBar ist nicht verfügbar');
          // Fallback: Versuche, die Nachricht direkt an das Hauptfenster zu senden
          try {
            window.parent.postMessage('toggle-menu-bar', '*');
          } catch (e) {
            console.error('Konnte Nachricht nicht an Hauptfenster senden:', e);
          }
        }
      });

      console.log('Touch-Gesten für Menüleiste im shopView wurden initialisiert');
    `);
  });
}

// Stelle sicher, dass auch der IPC-Handler zum manuellen Starten vorhanden ist:
ipcMain.on('show-on-screen-keyboard-manual', () => {
  launchOnScreenKeyboard();
});

// IPC-Handler für das Umschalten der Menüleiste
ipcMain.on('toggle-menu-bar', (event) => {
  log.info('IPC-Event "toggle-menu-bar" empfangen');

  if (mainWindow && !mainWindow.isDestroyed()) {
    const isVisible = mainWindow.isMenuBarVisible();
    log.info(`Menüleiste wird ${isVisible ? 'ausgeblendet' : 'eingeblendet'}`);

    // Menüleiste umschalten
    mainWindow.setMenuBarVisibility(!isVisible);

    // Kurze Verzögerung, dann BrowserView-Größe anpassen
    setTimeout(() => {
      if (mainWindow && !mainWindow.isDestroyed() && shopView) {
        const { width, height } = mainWindow.getContentBounds();
        shopView.setBounds({ x: 0, y: 0, width, height });
        log.info(`BrowserView-Größe angepasst: ${width}x${height}`);
      }
    }, 100);
  } else {
    log.error('Hauptfenster ist nicht verfügbar oder wurde zerstört');
  }
});

// IPC-Handler für den Druck des Kundenbelegs hinzufügen
ipcMain.on("print-customer-receipt", async (event) => {
  if (!zvtClient) {
    log.error(
      "ZVT-Client nicht initialisiert, Kundenbeleg kann nicht gedruckt werden"
    );
    return;
  }

  try {
    log.info("Drucke Kundenbeleg auf Anforderung");
    const result = await zvtClient.printCustomerReceipt();

    // Aktualisiere das Modal mit dem Druckergebnis
    if (paymentModalWindow && !paymentModalWindow.isDestroyed()) {
      if (result.success) {
        paymentModalWindow.webContents.send("payment-update", {
          status: "success",
          message: "Kundenbeleg wurde gedruckt",
          statusText: "Beleg gedruckt",
        });
      } else {
        paymentModalWindow.webContents.send("payment-update", {
          status: "error",
          message:
            "Fehler beim Drucken: " + (result.error || "Unbekannter Fehler"),
          statusText: "Druckfehler",
        });
      }
    }
  } catch (error) {
    log.error("Fehler beim Drucken des Kundenbelegs:", error.message);

    // Fehler im Modal anzeigen
    if (paymentModalWindow && !paymentModalWindow.isDestroyed()) {
      paymentModalWindow.webContents.send("payment-update", {
        status: "error",
        message: "Fehler beim Drucken: " + error.message,
        statusText: "Druckfehler",
      });
    }
  }
});

//IPC-Handler für den Druck des Händlerbelegs hinzufügen
ipcMain.on("print-merchant-receipt", async (event) => {
  log.info('IPC-Event "print-merchant-receipt" empfangen');

  if (!zvtClient) {
    log.error(
      "ZVT-Client nicht initialisiert, Händlerbeleg kann nicht gedruckt werden"
    );
    return;
  }

  try {
    log.info("Rufe zvtClient.printMerchantReceipt() auf");

    // Überprüfe, ob die Methode existiert
    if (typeof zvtClient.printMerchantReceipt !== "function") {
      log.error("printMerchantReceipt ist keine Funktion im zvtClient!");
      throw new Error("printMerchantReceipt Methode nicht verfügbar");
    }

    // Überprüfe den Zustand der lastReceipts vor dem Aufruf
    log.info(
      "ZVT-Client lastReceipts Status:",
      zvtClient.lastReceipts
        ? `customer: ${
            zvtClient.lastReceipts.customer ? "vorhanden" : "nicht vorhanden"
          }, ` +
            `merchant: ${
              zvtClient.lastReceipts.merchant ? "vorhanden" : "nicht vorhanden"
            }`
        : "lastReceipts ist nicht definiert"
    );

    const result = await zvtClient.printMerchantReceipt();
    log.info("Ergebnis des printMerchantReceipt Aufrufs:", result);

    // Aktualisiere das Modal mit dem Druckergebnis
    if (paymentModalWindow && !paymentModalWindow.isDestroyed()) {
      if (result.success) {
        log.info("Sende Erfolgs-Update an Modal");
        paymentModalWindow.webContents.send("payment-update", {
          status: "success",
          message: "Händlerbeleg wurde gedruckt",
          statusText: "Beleg gedruckt",
        });
      } else {
        log.warn("Sende Fehler-Update an Modal:", result.error);
        paymentModalWindow.webContents.send("payment-update", {
          status: "error",
          message:
            "Fehler beim Drucken: " + (result.error || "Unbekannter Fehler"),
          statusText: "Druckfehler",
        });
      }
    } else {
      log.warn("Modal-Fenster nicht verfügbar, kann Update nicht senden");
    }
  } catch (error) {
    log.error(
      "Fehler beim Drucken des Händlerbelegs:",
      error.message,
      error.stack
    );

    // Fehler im Modal anzeigen
    if (paymentModalWindow && !paymentModalWindow.isDestroyed()) {
      paymentModalWindow.webContents.send("payment-update", {
        status: "error",
        message: "Fehler beim Drucken: " + error.message,
        statusText: "Druckfehler",
      });
    }
  }
});

// IPC Listener für die Kommunikation mit dem Renderer
ipcMain.on("payment-data", (event, data) => {
  log.info("Zahlungsdaten empfangen:", data);
  // Implementiere hier die Integration mit dem Kassensystem
});

// IPC Listener für das Schließen des Modal-Fensters
ipcMain.on("close-payment-modal", async (event, options = {}) => {
  const shouldAbort = options.abort === true;
  const transactionId = options.transactionId || null;

  // Wenn Abbruch angefordert wird und das ZVT-Terminal verfügbar ist
  if (shouldAbort && zvtClient) {
    try {
      log.info("Zahlung wird auf Benutzeranforderung abgebrochen");
      await zvtClient.abortPayment();

      // NEU: Transaktion in Datenbank als abgebrochen markieren
      if (transactionId && tseClient && tseClient.revisionDb) {
        log.info(`Markiere Transaktion ${transactionId} als abgebrochen in Datenbank`);
        try {
          await tseClient.revisionDb.updatePaymentTransaction(transactionId, {
            status: 'ABORTED',
            completed_at: Math.floor(Date.now() / 1000)
          });
          log.info(`Transaktion ${transactionId} erfolgreich als abgebrochen markiert`);
        } catch (dbError) {
          log.error(`Fehler beim Markieren der Transaktion ${transactionId} als abgebrochen:`, dbError.message);
        }
      }
    } catch (error) {
      log.error("Fehler beim Abbrechen der Zahlung:", error.message);
    }
  }

  // Modal-Fenster schließen
  closePaymentModal();
});

// IPC Listener für das Abrufen der POS-URL
ipcMain.handle("get-pos-url", async () => {
  try {
    // Wenn die API-Konfiguration noch nicht geladen ist, lade sie
    if (!apiConfig) {
      apiConfig = await loadApiConfig();
    }

    // Extrahiere POS-URL aus der API-Konfiguration
    let posUrl = config.fallbackUrl;

    if (apiConfig) {
      if (apiConfig.SystemConfig && apiConfig.SystemConfig.PosUrl) {
        // PascalCase Format
        posUrl = apiConfig.SystemConfig.PosUrl;
      } else if (apiConfig.system_config && apiConfig.system_config.pos_url) {
        // snake_case Format
        posUrl = apiConfig.system_config.pos_url;
      }
    }

    return posUrl;
  } catch (error) {
    log.error("Fehler beim Abrufen der POS-URL:", error);
    // Gib die Fallback-URL zurück, wenn ein Fehler auftritt
    return config.fallbackUrl;
  }
});

// IPC Listener für das Abrufen der vollständigen API-Konfiguration
ipcMain.handle("get-api-config", async () => {
  try {
    // Wenn die API-Konfiguration noch nicht geladen ist, lade sie
    if (!apiConfig) {
      apiConfig = await loadApiConfig();
    }
    // Gib die gesamte API-Konfiguration zurück
    return apiConfig;
  } catch (error) {
    log.error("Fehler beim Abrufen der API-Konfiguration:", error);
    return null;
  }
});

// IPC-Handler für das Laden von Transaktionen im Monitoring
ipcMain.handle("get-transactions", async (event, options = {}) => {
  try {
    const page = options.page || 1;
    const pageSize = options.pageSize || 50;
    const filter = options.filter || {};

    // Stelle sicher, dass die TSE-Datenbank initialisiert ist
    if (!tseClient || !tseClient.revisionDb) {
      log.info('Initialisiere Revisionsdatenbank für Monitoring-Abfrage');
      if (!global.monitoringDb) {
        const TseDatabase = require('./tse-database');
        global.monitoringDb = new TseDatabase();
        await global.monitoringDb.initialize();
      }
    }

    // Verwende die Datenbank vom tseClient oder die temporäre
    const db = tseClient?.revisionDb || global.monitoringDb;

    // Lade die Transaktionen mit den angegebenen Filtern
    const transactions = await db.getTransactionsView(page, pageSize, filter);
    const totalCount = await db.countTransactionsView(filter);

    return {
      success: true,
      transactions,
      totalCount,
      page,
      pageSize
    };
  } catch (error) {
    log.error('Fehler beim Laden der Transaktionen für das Monitoring:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// IPC-Handler für das Schließen des Monitoring-Fensters
ipcMain.on("close-monitoring-window", () => {
  if (monitoringWindow && !monitoringWindow.isDestroyed()) {
    monitoringWindow.close();
  }
});

// IPC-Handler für das Abschließen einer TSE-Transaktion mit 0€
ipcMain.handle('finish-tse-transaction', async (event, { transactionNumber, transactionId }) => {
  try {
    log.info(`Versuche TSE-Transaktion ${transactionNumber} (ID: ${transactionId}) mit 0€ abzuschließen`);

    // Prüfe, ob ein TSE-Client verfügbar ist
    if (!global.tseClient) {
      throw new Error('Kein TSE-Client verfügbar');
    }

    // Verwende die neue finishZeroTransaction Methode des TSE-Clients
    const finishResult = await global.tseClient.finishZeroTransaction({
      id: transactionId,
      transaction_number: transactionNumber
    });

    if (finishResult && finishResult.success !== false) {
      log.info(`TSE-Transaktion ${transactionNumber} erfolgreich mit 0€ abgeschlossen`);
      return {
        success: true,
        message: `Transaktion ${transactionNumber} erfolgreich abgeschlossen`,
        data: finishResult
      };
    } else {
      const errorMessage = finishResult?.error || 'Unbekannter Fehler beim Abschließen der Transaktion';
      log.error(`Fehler beim Abschließen der TSE-Transaktion ${transactionNumber}:`, errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    }
  } catch (error) {
    log.error(`Fehler beim Abschließen der TSE-Transaktion ${transactionNumber}:`, error.message);
    return {
      success: false,
      error: error.message
    };
  }
});

// IPC-Handler für das Drucken des Monitoring-Fensters
ipcMain.on("print-monitoring-window", () => {
  if (monitoringWindow && !monitoringWindow.isDestroyed()) {
    log.info('Drucke Monitoring-Fenster');
    monitoringWindow.webContents.print({
      silent: false,
      printBackground: true,
      color: true,
      margins: {
        marginType: 'default'
      },
      landscape: false,
      pageSize: 'A4',
      collate: true,
      copies: 1,
      header: 'Transaktionsübersicht',
      footer: 'Seite $current von $total'
    }, (success, errorType) => {
      if (!success) {
        log.warn(`Fehler beim Drucken: ${errorType}`);
      }
    });
  }
});

// IPC-Handler für das Laden von Transaktionsdetails im Monitoring
ipcMain.handle("get-transaction-details", async (event, tseTransactionIdFromView) => {
  try {
    if (!tseTransactionIdFromView) {
      return { success: false, error: "Keine TSE-Transaktions-ID angegeben" };
    }

    const db = tseClient?.revisionDb || global.monitoringDb;
    if (!db || !db.initialized) {
      // Handle DB not initialized
      log.error('Datenbank nicht initialisiert in get-transaction-details');
      return { success: false, error: "Datenbank nicht initialisiert" };
    }

    // 1. Lade die Haupt-TSE-Transaktionsdaten (aus der Tabelle tse_transactions)
    // tseTransactionIdFromView ist die ID, die von der View kommt (t.transaction_id in der View)
    const tseTransactionMasterData = await db.findTransactionByWizidId(tseTransactionIdFromView);

    let paymentData = null;
    let referencePaymentTransactionId = null;
    let referenceCheckoutId = null;

    // Die ID, die in der transaction_references-Tabelle in der Spalte 'tse_transaction_id' steht,
    // könnte die externe TSE-ID sein (aus tseTransactionMasterData.tse_transaction_id)
    // ODER die interne App-ID (tseTransactionIdFromView).
    // Wir müssen herausfinden, wie saveTransactionReference diese befüllt.
    // Annahme: Es ist die externe TSE-ID.
    const idForReferenceLookup = tseTransactionMasterData?.tse_transaction_id || tseTransactionIdFromView;

    log.info(`Suche Referenz mit ID: ${idForReferenceLookup} (Primär: externe TSE ID, Fallback: ID aus View)`);

    // 2. Versuche, Referenzdaten zu finden
    const reference = await new Promise((resolve, reject) => {
      db.db.get(
        `SELECT payment_transaction_id, checkout_id FROM transaction_references
         WHERE tse_transaction_id = ?  -- Suche mit der externen TSE ID
         ORDER BY created_at DESC LIMIT 1`,
        [idForReferenceLookup],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (reference && (reference.payment_transaction_id || reference.checkout_id)) {
      log.info(`Referenz für ${idForReferenceLookup} gefunden: ${JSON.stringify(reference)}`);
      referencePaymentTransactionId = reference.payment_transaction_id;
      referenceCheckoutId = reference.checkout_id;
    } else {
      log.warn(`Keine gültige Referenz (mit payment_transaction_id oder checkout_id) für ID ${idForReferenceLookup} gefunden. Referenz-Objekt: ${JSON.stringify(reference)}`);
      // Fallback: Versuche die checkout_id aus den Master-Daten der TSE-Transaktion, falls vorhanden.
      // Dies ist nur sinnvoll, wenn tseTransactionMasterData.checkout_id existiert und relevant ist.
      // Basierend auf dem Schema in tse-database.js hat tse_transactions keine checkout_id Spalte.
      // Diese kommt in der View aus transaction_references.
      // Wenn die Referenz leer ist ({}), sind referenceCheckoutId & referencePaymentTransactionId hier null.
    }

    // 3. Suche Kartenzahlung basierend auf den gefundenen IDs
    // Wichtig: Auch wenn referenceCheckoutId oder referencePaymentTransactionId null sind,
    // kann findCardPaymentByTransactionId damit umgehen (sucht dann nur mit der anderen ID).
    paymentData = await db.findCardPaymentByTransactionId(referencePaymentTransactionId, referenceCheckoutId);
    let foundBy = "";

    if (paymentData) {
      paymentData.type = 'card';
      foundBy = referencePaymentTransactionId ? `payment_transaction_id (${referencePaymentTransactionId})` : `checkout_id (${referenceCheckoutId})`;
      log.info(`Kartenzahlung gefunden via: ${foundBy}`);
    } else {
      // Fallback: Direkte Suche mit der ursprünglichen ID aus der View, falls es sich um eine Zahlungs-ID handeln könnte
      // ODER falls die card_transaction direkt die tse_transaction_id (interne App-ID) oder checkout_id (aus anderer Quelle) verwendet.
      log.info(`Keine Kartenzahlung über Referenz gefunden. Versuche direkte Suche mit tseTransactionIdFromView (${tseTransactionIdFromView}) oder referenceCheckoutId (${referenceCheckoutId}, falls vorhanden).`);
      paymentData = await db.findCardPaymentByTransactionId(tseTransactionIdFromView, referenceCheckoutId); // checkoutId hier beibehalten
      if (paymentData) {
        paymentData.type = 'card';
        foundBy = `direkt mit tseTransactionIdFromView (${tseTransactionIdFromView}) und/oder checkoutId (${referenceCheckoutId})`;
        log.info(`Kartenzahlung gefunden via: ${foundBy}`);
      }
    }

    // 4. Wenn keine Kartenzahlung, suche Barzahlung
    if (!paymentData) {
      log.info(`Keine Kartenzahlung gefunden. Suche nach Barzahlung.`);
      paymentData = await db.findCashPaymentByTransactionId(referencePaymentTransactionId, referenceCheckoutId);
      if (!paymentData) {
        paymentData = await db.findCashPaymentByTransactionId(tseTransactionIdFromView, referenceCheckoutId);
      }
      if (paymentData) {
        paymentData.type = 'cash';
      }
    }

    if (!tseTransactionMasterData && !paymentData) {
      return { success: false, error: `Keine Transaktions- oder Zahlungsdaten für ID ${tseTransactionIdFromView} gefunden.` };
    }

    log.info(`Geladene Details für ${tseTransactionIdFromView}: TSE-Master-Daten ${tseTransactionMasterData ? 'gefunden' : 'nicht gefunden'}, Zahlungsdaten ${paymentData ? `gefunden (Typ: ${paymentData.type})` : 'nicht gefunden'}`);
    if (paymentData && paymentData.type === 'card') {
        log.info(`Belege in Zahlungsdaten: Kunde: ${!!paymentData.customer_receipt}, Händler: ${!!paymentData.merchant_receipt}`);
    }

    return {
      success: true,
      transaction: tseTransactionMasterData,
      paymentData: paymentData
    };
  } catch (error) {
    log.error(`Kritischer Fehler beim Laden der Transaktionsdetails für ${tseTransactionIdFromView}:`, error);
    return { success: false, error: error.message };
  }
});

// IPC-Handler für den Nachdruck von EC-Belegen
ipcMain.handle("print-card-receipt", async (event, data) => {
  try {
    if (!data || !data.transactionId) {
      return { success: false, error: "Keine Transaktions-ID angegeben" };
    }

    const { transactionId, receiptType } = data;
    log.info(`Nachdruckanforderung für ${receiptType}-Beleg der Transaktion ${transactionId}`);

    if (!zvtClient || !zvtClient.receipt) {
      log.error("ZVT-Client oder ZVT-Receipt nicht initialisiert");
      return { success: false, error: "ZVT-Client nicht initialisiert" };
    }

    // Hole die Datenbankinstanz, genau wie bei get-transaction-details
    const db = tseClient?.revisionDb || global.monitoringDb;
    if (!db || !db.initialized) {
      log.error('Datenbank nicht initialisiert in print-card-receipt');
      return { success: false, error: "Datenbank nicht initialisiert" };
    }

    // ---- BEGIN: Ähnliche Logik wie in get-transaction-details verwenden ----
    // 1. Lade die Haupt-TSE-Transaktionsdaten 
    const tseTransactionMasterData = await db.findTransactionByWizidId(transactionId);

    let paymentData = null;
    let referencePaymentTransactionId = null;
    let referenceCheckoutId = null;

    // Die ID, die in der transaction_references-Tabelle steht, kann die externe oder interne ID sein
    const idForReferenceLookup = tseTransactionMasterData?.tse_transaction_id || transactionId;

    log.info(`Suche Referenz mit ID für Beleg-Nachdruck: ${idForReferenceLookup}`);

    // 2. Versuche, Referenzdaten zu finden
    const reference = await new Promise((resolve, reject) => {
      db.db.get(
        `SELECT payment_transaction_id, checkout_id FROM transaction_references
         WHERE tse_transaction_id = ?
         ORDER BY created_at DESC LIMIT 1`,
        [idForReferenceLookup],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (reference && (reference.payment_transaction_id || reference.checkout_id)) {
      log.info(`Referenz für Beleg-Nachdruck gefunden: ${JSON.stringify(reference)}`);
      referencePaymentTransactionId = reference.payment_transaction_id;
      referenceCheckoutId = reference.checkout_id;
    } else {
      log.warn(`Keine gültige Referenz für Beleg-Nachdruck mit ID ${idForReferenceLookup} gefunden.`);
    }

    // 3. Suche Kartenzahlung basierend auf den gefundenen IDs
    paymentData = await db.findCardPaymentByTransactionId(referencePaymentTransactionId, referenceCheckoutId);
    let foundBy = "";

    if (paymentData) {
      foundBy = referencePaymentTransactionId ? `payment_transaction_id (${referencePaymentTransactionId})` : `checkout_id (${referenceCheckoutId})`;
      log.info(`Kartenzahlung für Beleg-Nachdruck gefunden via: ${foundBy}`);
    } else {
      // Fallback: Direkte Suche mit der ursprünglichen ID
      log.info(`Keine Kartenzahlung über Referenz gefunden. Versuche direkte Suche mit transactionId (${transactionId}) oder referenceCheckoutId (${referenceCheckoutId}).`);
      paymentData = await db.findCardPaymentByTransactionId(transactionId, referenceCheckoutId);
      if (paymentData) {
        foundBy = `direkt mit transactionId (${transactionId}) und/oder checkoutId (${referenceCheckoutId})`;
        log.info(`Kartenzahlung für Beleg-Nachdruck gefunden via: ${foundBy}`);
      }
    }
    // ---- END: Ähnliche Logik wie in get-transaction-details verwenden ----

    if (!paymentData) {
      log.error(`Keine Kartenzahlungsdaten für Transaktion ${transactionId} gefunden`);
      return { success: false, error: "Keine Kartenzahlungsdaten gefunden" };
    }

    // Drucke den angeforderten Beleg
    let result;
    if (receiptType === "customer") {
      log.info(`Drucke Kundenbeleg für Transaktion ${transactionId}`);
      // Stelle sicher, dass der Kundenbeleg im zvtClient.receipt vorhanden ist
      if (paymentData.customer_receipt) {
        try {
          if (typeof paymentData.customer_receipt === 'string') {
            // Prüfe, ob es ein JSON-String ist
            if (paymentData.customer_receipt.startsWith('[') && paymentData.customer_receipt.endsWith(']')) {
              // Parsen als JSON-Array
              zvtClient.receipt.lastReceipts.customer = JSON.parse(paymentData.customer_receipt);
            } else {
              // Behandle als normalen String mit Zeilenumbrüchen
              zvtClient.receipt.lastReceipts.customer = paymentData.customer_receipt.split('\n');
            }
          } else {
            // Bereits ein Array
            zvtClient.receipt.lastReceipts.customer = paymentData.customer_receipt;
          }
        } catch (parseError) {
          log.error(`Fehler beim Parsen des Kundenbelegs: ${parseError.message}`);
          // Fallback: Als String behandeln
          zvtClient.receipt.lastReceipts.customer = String(paymentData.customer_receipt).split('\n');
        }
      }
      result = await zvtClient.receipt.printCustomerReceipt();
    } else if (receiptType === "merchant") {
      log.info(`Drucke Händlerbeleg für Transaktion ${transactionId}`);
      // Stelle sicher, dass der Händlerbeleg im zvtClient.receipt vorhanden ist
      if (paymentData.merchant_receipt) {
        try {
          if (typeof paymentData.merchant_receipt === 'string') {
            // Prüfe, ob es ein JSON-String ist
            if (paymentData.merchant_receipt.startsWith('[') && paymentData.merchant_receipt.endsWith(']')) {
              // Parsen als JSON-Array
              zvtClient.receipt.lastReceipts.merchant = JSON.parse(paymentData.merchant_receipt);
            } else {
              // Behandle als normalen String mit Zeilenumbrüchen
              zvtClient.receipt.lastReceipts.merchant = paymentData.merchant_receipt.split('\n');
            }
          } else {
            // Bereits ein Array
            zvtClient.receipt.lastReceipts.merchant = paymentData.merchant_receipt;
          }
        } catch (parseError) {
          log.error(`Fehler beim Parsen des Händlerbelegs: ${parseError.message}`);
          // Fallback: Als String behandeln
          zvtClient.receipt.lastReceipts.merchant = String(paymentData.merchant_receipt).split('\n');
        }
      }
      result = await zvtClient.receipt.printMerchantReceipt();
    } else {
      return { success: false, error: "Ungültiger Belegtyp" };
    }

    return result;
  } catch (error) {
    log.error(`Fehler beim Nachdrucken des Belegs:`, error);
    return { success: false, error: error.message };
  }
});

// IPC-Handler für manuelle Zahlungen über das ZVT-Terminal
ipcMain.handle("start-card-payment", async (event, amount) => {
  if (!zvtClient) {
    log.error("ZVT-Client nicht initialisiert");

    // Trotzdem das Modal anzeigen und den Fehler darin darstellen
    createPaymentModalWindow({
      amount: amount,
      transactionId: "manual-" + Date.now(),
    });

    // Modal mit Fehlerstatus aktualisieren
    updatePaymentModal({
      status: "error",
      message:
        "ZVT-Client nicht initialisiert. Bitte starten Sie die Anwendung neu.",
      statusText: "Initialisierungsfehler",
    });

    return { success: false, error: "ZVT-Client nicht initialisiert" };
  }

  try {
    // Separates Modal-Fenster anzeigen
    const transactionId = "manual-" + Date.now();

    // TSE-Datenbank initialisieren, falls noch nicht geschehen
    if (tseClient && !tseClient.revisionDb) {
      log.info('Initialisiere Revisionsdatenbank für manuelle Zahlungsprüfung');
      tseClient.revisionDb = new TseDatabase();
      await tseClient.revisionDb.initialize();
    }

    // Speichere Zahlungstransaktion in Datenbank
    if (tseClient && tseClient.revisionDb) {
      await tseClient.revisionDb.savePaymentTransaction({
        transaction_id: transactionId,
        amount: amount,
        payment_method: 'CARD',
        status: 'PENDING'
      });
    }

    createPaymentModalWindow({
      amount: amount,
      transactionId: transactionId,
    });

    // Zahlung mit kurzer Verzögerung starten
    return new Promise((resolve) => {
      setTimeout(async () => {
        try {
          // Betrag wird in Cent erwartet
          const paymentResult = await zvtClient.processPayment(
            amount,
            transactionId
          );

          // Transaktion in DB aktualisieren
          if (tseClient && tseClient.revisionDb) {
            if (paymentResult.success) {
              // Zahlungstransaktion als abgeschlossen markieren
              await tseClient.revisionDb.completePaymentTransaction(transactionId, {
                customer_receipt: JSON.stringify(paymentResult.customerReceipt || []),
                merchant_receipt: JSON.stringify(paymentResult.merchantReceipt || []),
                card_type: paymentResult.cardType || '',
                card_number: paymentResult.cardNumber || '',
                auth_code: paymentResult.authCode || '',
                receipt_number: paymentResult.receiptNumber || '',
                trace_number: paymentResult.traceNumber || '',
                terminal_id: paymentResult.terminalId || ''
              });
            } else {
              // Zahlungstransaktion als fehlgeschlagen markieren
              await tseClient.revisionDb.updatePaymentTransaction(transactionId, {
                status: 'FAILED',
                completed_at: Math.floor(Date.now() / 1000)
              });
            }
          }

          // Modal mit Ergebnis aktualisieren
          if (paymentResult.success) {
            updatePaymentModal({
              status: "success",
              message: "Zahlung erfolgreich!",
              ...paymentResult,
            });
          } else {
            updatePaymentModal({
              status: "error",
              message:
                "Fehler: " + (paymentResult.error || "Unbekannter Fehler"),
              ...paymentResult,
            });
          }

          resolve(paymentResult);
        } catch (error) {
          log.error("Fehler bei manueller Kartenzahlung:", error.message);

          // Transaktion in DB als fehlerhalb markieren
          if (tseClient && tseClient.revisionDb) {
            await tseClient.revisionDb.updatePaymentTransaction(transactionId, {
              status: 'ERROR',
              completed_at: Math.floor(Date.now() / 1000)
            });
          }

          // Fehler im Modal anzeigen
          updatePaymentModal({
            status: "error",
            message: "Fehler: " + error.message,
            transactionId: transactionId,
          });

          resolve({ success: false, error: error.message });
        }
      }, 100); // 100ms Verzögerung
    });
  } catch (error) {
    log.error("Fehler bei manueller Kartenzahlung:", error.message);
    return { success: false, error: error.message };
  }
});

// IPC-Handler für Tagesabschluss
ipcMain.handle("perform-day-end", async () => {
  if (!zvtClient) {
    log.error("ZVT-Client nicht initialisiert");
    return { success: false, error: "ZVT-Client nicht initialisiert" };
  }

  try {
    // Separates Modal-Fenster für Tagesabschluss
    const transactionId = "day-end-" + Date.now();

    // TSE-Datenbank initialisieren, falls noch nicht geschehen
    if (tseClient && !tseClient.revisionDb) {
      log.info('Initialisiere Revisionsdatenbank für Tagesabschluss');
      tseClient.revisionDb = new TseDatabase();
      await tseClient.revisionDb.initialize();
    }

    // Speichere Tagesabschluss-Transaktion in Datenbank
    if (tseClient && tseClient.revisionDb) {
      await tseClient.revisionDb.savePaymentTransaction({
        transaction_id: transactionId,
        amount: 0,
        payment_method: 'DAY_END',
        status: 'PENDING'
      });
    }

    createPaymentModalWindow({
      isEndOfDay: true,
      transactionId: transactionId,
    });

    // Modal mit Startinformation aktualisieren
    updatePaymentModal({
      status: "processing",
      message: "Tagesabschluss wird durchgeführt...",
      statusText: "Tagesabschluss",
      transactionId: transactionId,
      isEndOfDay: true,
    });

    // Tagesabschluss mit kurzer Verzögerung starten
    return new Promise((resolve) => {
      setTimeout(async () => {
        try {
          // Hier die neue direkte Methode verwenden statt performEndOfDay
          const endOfDayResult = await zvtClient.performDirectEndOfDay();

          // Transaktion in DB aktualisieren
          if (tseClient && tseClient.revisionDb) {
            if (endOfDayResult.success) {
              // Tagesabschluss als abgeschlossen markieren
              await tseClient.revisionDb.completePaymentTransaction(transactionId, {
                merchant_receipt: JSON.stringify(endOfDayResult.merchantReceipt || [])
              });
            } else {
              // Tagesabschluss als fehlgeschlagen markieren
              await tseClient.revisionDb.updatePaymentTransaction(transactionId, {
                status: 'FAILED',
                completed_at: Math.floor(Date.now() / 1000)
              });
            }
          }

          // Modal mit Ergebnis aktualisieren
          if (endOfDayResult.success) {
            updatePaymentModal({
              status: "success",
              message: "Tagesabschluss erfolgreich durchgeführt",
              statusText: "Abgeschlossen",
              transactionId: transactionId,
              isEndOfDay: true,
              hasMerchantReceipt: !!endOfDayResult.merchantReceipt
            });
          } else {
            updatePaymentModal({
              status: "error",
              message:
                "Fehler: " + (endOfDayResult.error || "Unbekannter Fehler"),
              statusText: "Fehlgeschlagen",
              transactionId: transactionId,
              isEndOfDay: true,
            });
          }

          resolve(endOfDayResult);
        } catch (error) {
          log.error("Fehler bei Tagesabschluss:", error.message);

          // Transaktion in DB als fehlerhaft markieren
          if (tseClient && tseClient.revisionDb) {
            await tseClient.revisionDb.updatePaymentTransaction(transactionId, {
              status: 'ERROR',
              completed_at: Math.floor(Date.now() / 1000)
            });
          }

          // Fehler im Modal anzeigen
          updatePaymentModal({
            status: "error",
            message: "Fehler: " + error.message,
            statusText: "Fehlgeschlagen",
            transactionId: transactionId,
            isEndOfDay: true,
          });

          resolve({ success: false, error: error.message });
        }
      }, 100); // 100ms Verzögerung
    });
  } catch (error) {
    log.error("Fehler bei Tagesabschluss:", error.message);
    return { success: false, error: error.message };
  }
});

// IPC-Handler für Terminal-Status-Check
ipcMain.handle("check-terminal-status", async () => {
  if (!zvtClient) {
    return { connected: false, message: "ZVT-Client nicht initialisiert" };
  }

  try {
    // Prüfen, ob Terminal verbunden ist und ggf. verbinden
    if (!zvtClient.connected) {
      const connectResult = await zvtClient.connect();
      if (!connectResult) {
        return {
          connected: false,
          message: "Terminal nicht verbunden",
          error: zvtClient.getLastError(),
        };
      }
    }

    return {
      connected: true,
      busy: zvtClient.isBusy(),
      message: "Terminal verbunden und bereit",
    };
  } catch (error) {
    return {
      connected: false,
      message: "Fehler bei Terminal-Status-Check",
      error: error.message,
    };
  }
});

//  Handler-Funktion für neu erstellte Transaktionen
async function handleTransactionUpdated(data) {
  try {
    const transactionId = data.params?.detail?.id || "Unbekannt";
    const transactionStatus = data.params?.detail?.status || "Unbekannt";

    log.info(
      "Aktualisierte Transaktion empfangen:",
      transactionId,
      "Status:",
      transactionStatus
    );

    // Prüfen, ob die notwendigen Daten vorhanden sind
    if (!data.params || !data.params.detail) {
      log.warn(
        "Unvollständige Transaktionsdaten empfangen, überspringe Verarbeitung"
      );
      return;
    }

    const transactionDetail = data.params.detail;

    // Normale Transaktionsverarbeitung
    handleTransactionMessage(data);

    // TSE-Signierung basierend auf Transaktionsstatus
    if (tseClient && tseClient.connected) {
      try {
        // FALL 1: Transaktion wurde auf finished gesetzt und ist noch nicht TSE-signiert
        if (
          transactionDetail.status === "finished" &&
          transactionDetail.tse_finish !== true
        ) {
          log.info("Schließe TSE-Transaktion ab:", transactionId);

          const isSpecialTransaction =
            transactionDetail.transaction_sub_type === "Anfangsbestand" ||
            transactionDetail.transaction_sub_type === "Abschluss" ||
            transactionDetail.transaction_sub_type === "Entnahme";

          if (!transactionDetail.tse_entry && isSpecialTransaction) {
            log.warn(
              "Kein tse_entry in der Transaktion gefunden, aber versuche TSE-Signierung trotzdem"
            );
          } else if (transactionDetail.tse_entry) {
            log.info("tse_entry gefunden:", transactionDetail.tse_entry);
          }

          try {
            // TSE-Transaktion abschließen
            const tseData = await tseClient.finishTransaction(
              transactionDetail
            );

            // Einheitliche TSE-Nachricht senden
            publishUnifiedTseMessage(transactionDetail, tseData, "finished");
          } catch (finishError) {
            log.error(
              "Fehler beim Abschließen der TSE-Transaktion:",
              finishError.message
            );

            // Trotzdem eine Nachricht senden, damit das Backend informiert ist
            const errorData = {
              tenant_id: transactionDetail.tenant_id,
              cashbox_id:
                tseClient?.config?.fiskaly_config?.client_id || "K001",
              wizid_transaction_id: transactionDetail.id,
              tse_transaction_id: transactionDetail.tse_transaction_id || "",
              tse_error: `Fehler beim Abschließen: ${finishError.message}`,
              status: "error",
            };

            // Fehler-Nachricht über das einheitliche Format senden
            publishUnifiedTseMessage(transactionDetail, errorData, "finished");
          }
        }
        // FALL 2: Transaktion ist bereits TSE-signiert
        else if (
          transactionDetail.status === "finished" &&
          transactionDetail.tse_finish === true
        ) {
          log.info(
            "TSE-Transaktion bereits abgeschlossen (tse_finish=true):",
            transactionId
          );
        }
        // FALL 3: Transaktion wurde abgebrochen
        else if (
          transactionDetail.status === "aborted" &&
          transactionDetail.tse_finish !== true
        ) {
          log.info("Breche TSE-Transaktion ab:", transactionId);

          try {
            const tseData = await tseClient.cancelTransaction(
              transactionDetail
            );

            // Einheitliche TSE-Nachricht senden
            publishUnifiedTseMessage(transactionDetail, tseData, "canceled");
          } catch (cancelError) {
            log.debug(
              "Fehler beim Abbrechen der TSE-Transaktion:",
              cancelError.message
            );

            // Fehler-Nachricht senden
            const errorData = {
              tse_error: `Fehler beim Abbrechen: ${cancelError.message}`,
              status: "error",
            };

            // Einheitliche TSE-Nachricht senden
            publishUnifiedTseMessage(transactionDetail, errorData, "canceled");
          }
        }
        // FALL 4: Abgebrochene Transaktion, die bereits TSE-signiert ist
        else if (
          transactionDetail.status === "aborted" &&
          transactionDetail.tse_finish === true
        ) {
          log.info(
            "TSE-Transaktion bereits abgebrochen (tse_finish=true):",
            transactionId
          );
        }
        // FALL 5: Transaktion hat jetzt einen tse_entry, aber wurde vorher noch nicht verarbeitet
        else if (
          transactionDetail.tse_entry &&
          transactionDetail.tse_signatur_start === undefined
        ) {
          log.info(
            "Transaktion hat neuen tse_entry, aber noch keine TSE-Signatur:",
            transactionId
          );

          // TSE-Transaktion starten
          try {
            const tseData = await tseClient.openTransaction(transactionDetail);

            // Einheitliche TSE-Nachricht senden
            publishUnifiedTseMessage(transactionDetail, tseData, "created");

            // Wenn die Transaktion bereits auf finished steht, direkt abschließen
            if (transactionDetail.status === "finished") {
              log.info(
                "Transaktion ist bereits finished, schließe TSE-Transaktion direkt ab"
              );

              // Kurze Verzögerung, um sicherzustellen, dass die openTransaction-Nachricht zuerst verarbeitet wird
              setTimeout(async () => {
                try {
                  const finishData = await tseClient.finishTransaction(
                    transactionDetail
                  );
                  publishUnifiedTseMessage(
                    transactionDetail,
                    finishData,
                    "finished"
                  );
                } catch (finishError) {
                  log.error(
                    "Fehler beim direkten Abschließen der TSE-Transaktion:",
                    finishError.message
                  );
                }
              }, 500);
            }
          } catch (startError) {
            log.error(
              "Fehler beim Starten der TSE-Transaktion:",
              startError.message
            );
          }
        }
      } catch (error) {
        log.error(
          "Fehler bei TSE-Verarbeitung der aktualisierten Transaktion:",
          error.message
        );
      }
    } else {
      log.warn(
        "TSE nicht verbunden, aktualisierte Transaktion wird ohne TSE-Verarbeitung bearbeitet"
      );
    }
  } catch (error) {
    log.error("Fehler bei Verarbeitung der aktualisierten Transaktion:", error);
  }
}

// Handler für aktualisierte Transaktionen
async function handleTransactionUpdated(data) {
  try {
    const transactionId = data.params?.detail?.id || "Unbekannt";
    const transactionStatus = data.params?.detail?.status || "Unbekannt";
    const transactionSubType = data.params?.detail?.transaction_sub_type || "Unbekannt";

    log.info(
      "Aktualisierte Transaktion empfangen:",
      transactionId,
      "Status:",
      transactionStatus,
      "Typ:",
      transactionSubType
    );

    // Prüfen, ob die notwendigen Daten vorhanden sind
    if (!data.params || !data.params.detail) {
      log.warn(
        "Unvollständige Transaktionsdaten empfangen, überspringe Verarbeitung"
      );
      return;
    }

    const transactionDetail = data.params.detail;

    // Prüfen, ob es sich um einen Kassenabschluss handelt und ob automatischer EC-Abschluss aktiviert ist
    if (transactionSubType === "Abschluss" &&
        transactionStatus === "finished" &&
        config.zvt_config &&
        config.zvt_config.auto_day_end === true) {

      log.info("Kassenabschluss erkannt und automatischer EC-Abschluss ist aktiviert");

      // Prüfen, ob bereits ein EC-Abschluss läuft
      if (ecDayEndInProgress) {
        log.warn("EC-Abschluss läuft bereits, automatischer EC-Abschluss wird übersprungen");
        return;
      }

      // Prüfen, ob ZVT-Client verfügbar ist
      if (zvtClient) {
        // Prüfen, ob das Terminal beschäftigt ist
        if (zvtClient.isBusy && zvtClient.isBusy()) {
          log.warn("Terminal ist beschäftigt, automatischer EC-Abschluss wird verzögert");

          // Längere Verzögerung, wenn das Terminal beschäftigt ist
          setTimeout(async () => {
            try {
              // Erneut prüfen, ob ein EC-Abschluss bereits läuft
              if (ecDayEndInProgress) {
                log.warn("EC-Abschluss läuft bereits, verzögerter automatischer EC-Abschluss wird übersprungen");
                return;
              }

              // Erneut prüfen, ob das Terminal noch beschäftigt ist
              if (zvtClient.isBusy && zvtClient.isBusy()) {
                log.warn("Terminal ist immer noch beschäftigt, automatischer EC-Abschluss wird abgebrochen");
                return;
              }

              log.info("Starte verzögerten automatischen EC-Abschluss");
              await performECDayEnd();
              log.info("Automatischer EC-Abschluss erfolgreich durchgeführt");
            } catch (error) {
              log.error("Fehler beim verzögerten automatischen EC-Abschluss:", error.message);
            }
          }, 10000); // 10 Sekunden Verzögerung
        } else {
          log.info("Starte automatischen EC-Abschluss");

          // Längere Verzögerung, um sicherzustellen, dass der Kassenabschluss vollständig verarbeitet wurde
          setTimeout(async () => {
            try {
              // Erneut prüfen, ob ein EC-Abschluss bereits läuft
              if (ecDayEndInProgress) {
                log.warn("EC-Abschluss läuft bereits, automatischer EC-Abschluss wird übersprungen");
                return;
              }

              // Erneut prüfen, ob das Terminal beschäftigt ist
              if (zvtClient.isBusy && zvtClient.isBusy()) {
                log.warn("Terminal ist jetzt beschäftigt, automatischer EC-Abschluss wird verzögert");

                // Erneuter Versuch nach weiterer Verzögerung
                setTimeout(async () => {
                  try {
                    // Erneut prüfen, ob ein EC-Abschluss bereits läuft
                    if (ecDayEndInProgress) {
                      log.warn("EC-Abschluss läuft bereits, verzögerter automatischer EC-Abschluss wird übersprungen");
                      return;
                    }

                    if (!zvtClient.isBusy || !zvtClient.isBusy()) {
                      log.info("Starte verzögerten automatischen EC-Abschluss");
                      await performECDayEnd();
                      log.info("Automatischer EC-Abschluss erfolgreich durchgeführt");
                    } else {
                      log.warn("Terminal ist immer noch beschäftigt, automatischer EC-Abschluss wird abgebrochen");
                    }
                  } catch (error) {
                    log.error("Fehler beim verzögerten automatischen EC-Abschluss:", error.message);
                  }
                }, 5000); // 5 Sekunden zusätzliche Verzögerung
                return;
              }

              await performECDayEnd();
              log.info("Automatischer EC-Abschluss erfolgreich durchgeführt");
            } catch (error) {
              log.error("Fehler beim automatischen EC-Abschluss:", error.message);
            }
          }, 5000); // 5 Sekunden Verzögerung
        }
      } else {
        log.warn("ZVT-Client nicht verfügbar, automatischer EC-Abschluss nicht möglich");
      }
    }

    // Normale Transaktionsverarbeitung
    handleTransactionMessage(data);

    // TSE-Signierung basierend auf Transaktionsstatus
    if (tseClient && tseClient.connected) {
      try {
        // FALL 1: Transaktion wurde auf finished gesetzt und ist noch nicht TSE-signiert
        if (
          transactionDetail.status === "finished" &&
          transactionDetail.tse_finish !== true
        ) {
          log.info("Schließe TSE-Transaktion ab:", transactionId);

          // Prüfen, ob tse_entry vorhanden ist
          if (transactionDetail.tse_entry) {
            log.info("tse_entry gefunden:", transactionDetail.tse_entry);

            try {
              // TSE-Transaktion abschließen
              const tseData = await tseClient.finishTransaction(
                transactionDetail
              );

              // GEÄNDERT: Update statt Neuanlage in Datenbank
              await updateTseTransactionInDatabase(transactionDetail, tseData, "FINISHED");

              // Einheitliche TSE-Nachricht senden
              publishUnifiedTseMessage(transactionDetail, tseData, "finished");
            } catch (finishError) {
              log.error(
                "Fehler beim Abschließen der TSE-Transaktion:",
                finishError.message
              );

              // Fehler-Status in Datenbank setzen
              await updateTseTransactionInDatabase(transactionDetail, null, "ERROR", finishError.message);

              // Trotzdem eine Nachricht senden, damit das Backend informiert ist
              const errorData = {
                tenant_id: transactionDetail.tenant_id,
                cashbox_id:
                  tseClient?.config?.fiskaly_config?.client_id || "K001",
                wizid_transaction_id: transactionDetail.id,
                tse_transaction_id: transactionDetail.tse_transaction_id || "",
                tse_error: `Fehler beim Abschließen: ${finishError.message}`,
                status: "error",
              };

              // Fehler-Nachricht über das einheitliche Format senden
              publishUnifiedTseMessage(
                transactionDetail,
                errorData,
                "finished"
              );
            }
          } else {
            log.warn(
              "Kein tse_entry in der Transaktion gefunden, TSE-Signierung nicht möglich!"
            );

            // Fehler-Status in Datenbank setzen
            await updateTseTransactionInDatabase(transactionDetail, null, "ERROR", "Kein tse_entry gefunden");

            // Fehler-Nachricht senden
            const errorData = {
              tenant_id: transactionDetail.tenant_id,
              cashbox_id:
                tseClient?.config?.fiskaly_config?.client_id || "K001",
              wizid_transaction_id: transactionDetail.id,
              tse_transaction_id: "",
              tse_error: "Kein tse_entry in der Transaktion gefunden",
              status: "error",
            };

            // Fehler-Nachricht über das einheitliche Format senden
            publishUnifiedTseMessage(transactionDetail, errorData, "finished");
          }
        }
        // FALL 2: Transaktion wurde abgebrochen (verschiedene Abbruch-Status)
        else if (
          (transactionDetail.status === "aborted" || 
           transactionDetail.status === "canceled" || 
           transactionDetail.status === "cancelled" ||
           transactionDetail.status === "abort") &&
          transactionDetail.tse_finish !== true
        ) {
          log.info(`Breche TSE-Transaktion ab (Status: ${transactionDetail.status}):`, transactionId);

          try {
            const tseData = await tseClient.cancelTransaction(
              transactionDetail
            );

            // Update in Datenbank mit Status "CANCELED"
            await updateTseTransactionInDatabase(transactionDetail, tseData, "CANCELED");

            // Einheitliche TSE-Nachricht senden
            publishUnifiedTseMessage(transactionDetail, tseData, "canceled");
          } catch (cancelError) {
            log.debug(
              "Fehler beim Abbrechen der TSE-Transaktion:",
              cancelError.message
            );

            // Fehler-Status in Datenbank setzen
            await updateTseTransactionInDatabase(transactionDetail, null, "ERROR", cancelError.message);

            // Fehler-Nachricht senden
            const errorData = {
              tse_error: `Fehler beim Abbrechen: ${cancelError.message}`,
              status: "error",
            };

            // Einheitliche TSE-Nachricht senden
            publishUnifiedTseMessage(transactionDetail, errorData, "canceled");
          }
        }
        // FALL 2b: Transaktion bereits abgebrochen, aber TSE-Signierung war bereits abgeschlossen
        else if (
          (transactionDetail.status === "aborted" || 
           transactionDetail.status === "canceled" || 
           transactionDetail.status === "cancelled" ||
           transactionDetail.status === "abort") &&
          transactionDetail.tse_finish === true
        ) {
          log.info(
            `TSE-Transaktion bereits abgebrochen (tse_finish=true, Status: ${transactionDetail.status}):`,
            transactionId
          );
          
          // Nur Datenbank-Status aktualisieren, keine TSE-Operation erforderlich
          await updateTseTransactionInDatabase(transactionDetail, transactionDetail, "CANCELED_AFTER_FINISH");
        }
        // FALL 3: Transaktion ist bereits TSE-signiert
        else if (
          transactionDetail.status === "finished" &&
          transactionDetail.tse_finish === true
        ) {
          log.info(
            "TSE-Transaktion bereits abgeschlossen (tse_finish=true):",
            transactionId
          );
        }
        // FALL 4: Transaktion hat neue TSE-Daten (z.B. tse_entry hinzugefügt)
        else if (transactionDetail.tse_entry || transactionDetail.tse_signatur_start) {
          log.info("TSE-Daten in Transaktion aktualisiert, update Datenbank:", transactionId);
          
          // Update der TSE-Transaktion in der Datenbank mit neuen Daten
          await updateTseTransactionInDatabase(transactionDetail, transactionDetail, "UPDATED");
        }
        // FALL 5: Transaktion hat jetzt einen tse_entry, aber wurde vorher noch nicht verarbeitet
        else if (
          transactionDetail.tse_entry &&
          !transactionDetail.tse_signatur_start
        ) {
          log.info(
            "Transaktion hat neuen tse_entry, aber noch keine TSE-Signatur:",
            transactionId
          );

          // TSE-Transaktion starten
          try {
            const tseData = await tseClient.openTransaction(transactionDetail);

            // Einheitliche TSE-Nachricht senden
            publishUnifiedTseMessage(transactionDetail, tseData, "created");

            // Wenn die Transaktion bereits auf finished steht, direkt abschließen
            if (transactionDetail.status === "finished") {
              log.info(
                "Transaktion ist bereits finished, schließe TSE-Transaktion direkt ab"
              );

              // Kurze Verzögerung, um sicherzustellen, dass die openTransaction-Nachricht zuerst verarbeitet wird
              setTimeout(async () => {
                try {
                  const finishData = await tseClient.finishTransaction(
                    transactionDetail
                  );
                  publishUnifiedTseMessage(
                    transactionDetail,
                    finishData,
                    "finished"
                  );
                } catch (finishError) {
                  log.error(
                    "Fehler beim direkten Abschließen der TSE-Transaktion:",
                    finishError.message
                  );
                }
              }, 500);
            }
          } catch (startError) {
            log.error(
              "Fehler beim Starten der TSE-Transaktion:",
              startError.message
            );
          }
        }
      } catch (error) {
        log.error(
          "Fehler bei TSE-Verarbeitung der aktualisierten Transaktion:",
          error.message
        );
      }
    } else {
      log.warn(
        "TSE nicht verbunden, aktualisierte Transaktion wird ohne TSE-Verarbeitung bearbeitet"
      );
    }
  } catch (error) {
    log.error("Fehler bei Verarbeitung der aktualisierten Transaktion:", error);
  }
}

// Handler-Funktion für Transaktionsstornierungen
async function handleTransactionCreated(data) {
  try {
    const transactionId = data.params?.detail?.id || "Unbekannt";
    log.info("Transaktion empfangen:", transactionId);

    // Prüfen, ob die notwendigen Daten vorhanden sind
    if (!data.params || !data.params.detail) {
      log.warn(
        "Unvollständige Transaktionsdaten empfangen, überspringe Verarbeitung"
      );
      return;
    }

    const transactionDetail = data.params.detail;

    // Normale Transaktionsverarbeitung
    handleTransactionMessage(data);

    // TSE-Signierung nur starten, wenn:
    // 1. TSE-Client verfügbar ist
    // 2. Die Transaktion noch nicht TSE-signiert ist (tse_finish !== true)
    if (
      tseClient &&
      tseClient.connected &&
      transactionDetail.tse_finish !== true
    ) {
      try {
        log.info("Starte TSE-Signierung für Transaktion:", transactionId);

        const isSpecialTransaction =
          transactionDetail.transaction_sub_type === "Anfangsbestand" ||
          transactionDetail.transaction_sub_type === "Abschluss" ||
          transactionDetail.transaction_sub_type === "Entnahme";

        if (!transactionDetail.tse_entry && isSpecialTransaction) {
          log.info(
            "Kein tse_entry in der Transaktion gefunden, warte auf Update mit vollständigen Daten"
          );
          return;
        }

        // TSE-Transaktion starten
        const tseData = await tseClient.openTransaction(transactionDetail);

        // NEUE FUNKTION: Sofort in Datenbank speichern mit Status "CREATED"
        await saveTseTransactionToDatabase(transactionDetail, tseData, "CREATED");

        // Einheitliche TSE-Nachricht senden
        publishUnifiedTseMessage(transactionDetail, tseData, "created");

        // Wenn die Transaktion bereits den Status "finished" hat, sofort abschließen
        if (transactionDetail.status === "finished") {
          log.info(
            "Transaktion ist bereits finished, schließe TSE-Transaktion direkt ab"
          );

          // Kurze Verzögerung, um sicherzustellen, dass die openTransaction-Nachricht zuerst verarbeitet wird
          setTimeout(async () => {
            try {
              const finishData = await tseClient.finishTransaction(
                transactionDetail
              );
              
              // Datenbank-Update statt Neuanlage
              await updateTseTransactionInDatabase(transactionDetail, finishData, "FINISHED");
              
              publishUnifiedTseMessage(
                transactionDetail,
                finishData,
                "finished"
              );
            } catch (finishError) {
              log.error(
                "Fehler beim direkten Abschließen der TSE-Transaktion:",
                finishError.message
              );
              
              // Fehler-Status in Datenbank setzen
              await updateTseTransactionInDatabase(transactionDetail, null, "ERROR", finishError.message);
            }
          }, 500);
        }
      } catch (error) {
        log.error("Fehler bei TSE-Signierung:", error.message);
        
        // Fehler-Status in Datenbank setzen
        await updateTseTransactionInDatabase(transactionDetail, null, "ERROR", error.message);
      }
    } else if (!tseClient || !tseClient.connected) {
      log.warn(
        "TSE nicht verbunden, Transaktion wird ohne TSE-Signierung verarbeitet"
      );
    } else if (transactionDetail.tse_finish === true) {
      log.info(
        "Transaktion bereits TSE-signiert (tse_finish=true), überspringe Signierung"
      );
    }
  } catch (error) {
    log.error("Fehler bei Verarbeitung der neuen Transaktion:", error);
  }
}

/**
 * Speichert eine neue TSE-Transaktion in der Datenbank
 * @param {Object} transactionDetail Die Transaktionsdetails
 * @param {Object} tseData Die TSE-Daten
 * @param {string} status Der Status der Transaktion
 */
async function saveTseTransactionToDatabase(transactionDetail, tseData, status) {
  try {
    if (!tseClient || !tseClient.revisionDb) {
      log.warn("TSE-Revisionsdatenbank nicht verfügbar, überspringe Speicherung");
      return false;
    }

    // Zeit-Konvertierung für Start-Zeit
    let startTime = tseData.tse_opening_timestamp || new Date().toISOString();
    if (typeof startTime === 'string' && startTime.includes('.') && !startTime.includes('T')) {
      // Deutsches Datumsformat in ISO umwandeln
      const parts = startTime.split(' ');
      if (parts.length === 2) {
        const dateParts = parts[0].split('.');
        if (dateParts.length === 3) {
          startTime = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}T${parts[1]}.000Z`;
        }
      }
    }
    startTime = Math.floor(new Date(startTime).getTime() / 1000);

    // ProcessData für die Datenbank vorbereiten
    const processDataJson = JSON.stringify({
      original: transactionDetail.tse_entry || "",
      dsfin_entry: transactionDetail.dsfin_entry || "",
      status: status,
      created_at: startTime
    });

    // In Revisionsdatenbank speichern
    await tseClient.revisionDb.saveTransaction({
      fiskaly_client: tseClient.tseConfig?.client_id || 'K001',
      tse_transaction_id: tseData.tse_transaction_id || transactionDetail.tse_transaction_id || "",
      cash_register_id: transactionDetail.cash_register_id || tseClient.tseConfig?.client_id || 'K001',
      transaction_id: transactionDetail.id,
      transaction_number: parseInt(tseData.tse_transaction_id || transactionDetail.tse_transaction_id, 10) || 0,
      transaction_counter: parseInt(tseData.tse_signatur_counter || transactionDetail.tse_signatur_counter, 10) || 0,
      start: startTime,
      end: 0, // Noch nicht abgeschlossen
      state: status,
      type: 'TRANSACTION',
      start_signature: tseData.tse_signatur_start || transactionDetail.tse_signatur_start || "",
      end_signature: "", // Noch nicht abgeschlossen
      process_data: processDataJson
    });

    log.info(`TSE-Transaktion ${transactionDetail.id} mit Status ${status} in Datenbank gespeichert`);
    return true;
  } catch (error) {
    log.error(`Fehler beim Speichern der TSE-Transaktion ${transactionDetail.id}:`, error.message);
    return false;
  }
}

/**
 * Aktualisiert eine bestehende TSE-Transaktion in der Datenbank
 * @param {Object} transactionDetail Die Transaktionsdetails
 * @param {Object} tseData Die TSE-Daten (kann null sein bei Fehlern)
 * @param {string} status Der neue Status
 * @param {string} errorMessage Optionale Fehlermeldung
 */
async function updateTseTransactionInDatabase(transactionDetail, tseData, status, errorMessage = null) {
  try {
    if (!tseClient || !tseClient.revisionDb) {
      log.warn("TSE-Revisionsdatenbank nicht verfügbar, überspringe Update");
      return false;
    }

    // Bestehende Transaktion suchen
    const existingTransaction = await tseClient.revisionDb.findTransactionByWizidId(transactionDetail.id);
    if (!existingTransaction) {
      log.warn(`Keine bestehende TSE-Transaktion für Update gefunden: ${transactionDetail.id}`);
      
      // Falls nicht gefunden, erstelle neue (Fallback)
      if (tseData) {
        return await saveTseTransactionToDatabase(transactionDetail, tseData, status);
      }
      return false;
    }

    // Update-Daten vorbereiten
    let endTime = existingTransaction.end;
    let endSignature = existingTransaction.end_signature;
    let processData = existingTransaction.process_data;

    // Bei Abschluss oder Fehler die End-Zeit setzen
    if (status === "FINISHED" || status === "CANCELED" || status === "ERROR" || status === "CANCELED_AFTER_FINISH") {
      if (tseData && tseData.tse_signatur_end_timestamp) {
        let endTimestamp = tseData.tse_signatur_end_timestamp;
        
        // Zeit-Konvertierung für End-Zeit
        if (typeof endTimestamp === 'string' && endTimestamp.includes('.') && !endTimestamp.includes('T')) {
          const parts = endTimestamp.split(' ');
          if (parts.length === 2) {
            const dateParts = parts[0].split('.');
            if (dateParts.length === 3) {
              endTimestamp = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}T${parts[1]}.000Z`;
            }
          }
        }
        endTime = Math.floor(new Date(endTimestamp).getTime() / 1000);
      } else {
        endTime = Math.floor(Date.now() / 1000);
      }

      if (tseData && tseData.tse_signatur_end) {
        endSignature = tseData.tse_signatur_end;
      }

      // ProcessData aktualisieren
      try {
        const existingProcessData = JSON.parse(processData || '{}');
        const updatedProcessData = {
          ...existingProcessData,
          finished: {
            status: status,
            completed_at: endTime,
            qr_code: tseData?.qr_code || "",
            error_message: errorMessage,
            original_transaction_status: transactionDetail.status
          }
        };
        processData = JSON.stringify(updatedProcessData);
      } catch (parseError) {
        log.warn("Fehler beim Aktualisieren der ProcessData:", parseError.message);
      }
    }

    // Transaktion aktualisieren
    await tseClient.revisionDb.saveTransaction({
      fiskaly_client: existingTransaction.fiskaly_client,
      tse_transaction_id: existingTransaction.tse_transaction_id,
      cash_register_id: existingTransaction.cash_register_id,
      transaction_id: existingTransaction.transaction_id,
      transaction_number: existingTransaction.transaction_number,
      transaction_counter: tseData?.tse_signatur_counter ? parseInt(tseData.tse_signatur_counter, 10) : existingTransaction.transaction_counter,
      start: existingTransaction.start,
      end: endTime,
      state: status,
      type: existingTransaction.type,
      start_signature: existingTransaction.start_signature,
      end_signature: endSignature,
      process_data: processData
    });

    log.info(`TSE-Transaktion ${transactionDetail.id} mit Status ${status} in Datenbank aktualisiert`);
    return true;
  } catch (error) {
    log.error(`Fehler beim Aktualisieren der TSE-Transaktion ${transactionDetail.id}:`, error.message);
    return false;
  }
}

/**
 * Behandelt explizit Abbruch-Nachrichten für Transaktionen
 * @param {Object} data Die MQTT-Nachrichtendaten
 */
async function handleTransactionAborted(data) {
  try {
    const transactionId = data.params?.detail?.id || "Unbekannt";
    const abortReason = data.params?.detail?.abort_reason || "Nicht angegeben";
    const originalStatus = data.params?.detail?.status || "aborted";
    
    log.info(`Abbruch-Nachricht empfangen für Transaktion ${transactionId}, Grund: ${abortReason}, Status: ${originalStatus}`);

    // Prüfen, ob die notwendigen Daten vorhanden sind
    if (!data.params || !data.params.detail) {
      log.warn("Unvollständige Abbruch-Transaktionsdaten empfangen, überspringe Verarbeitung");
      return;
    }

    const transactionDetail = data.params.detail;

    // Normale Transaktionsverarbeitung
    handleTransactionMessage(data);

    // TSE-Behandlung für Abbruch
    if (tseClient && tseClient.connected) {
      try {
        // Prüfen, ob TSE-Transaktion bereits abgeschlossen wurde
        if (transactionDetail.tse_finish === true) {
          log.info(`TSE-Transaktion ${transactionId} war bereits abgeschlossen, markiere als nachträglich abgebrochen`);
          
          // Nur Status-Update in Datenbank, keine TSE-Operation
          await updateTseTransactionInDatabase(
            transactionDetail, 
            transactionDetail, 
            "CANCELED_AFTER_FINISH", 
            `Nachträglicher Abbruch: ${abortReason}`
          );
          
          // Benachrichtige Backend über nachträglichen Abbruch
          publishUnifiedTseMessage(transactionDetail, {
            ...transactionDetail,
            tse_error: `Nachträglicher Abbruch: ${abortReason}`,
            abort_reason: abortReason
          }, "canceled");
          
        } else {
          log.info(`Breche aktive TSE-Transaktion ${transactionId} ab, Grund: ${abortReason}`);
          
          try {
            // TSE-Transaktion aktiv abbrechen
            const tseData = await tseClient.cancelTransaction(transactionDetail);
            
            // Status in Datenbank aktualisieren
            await updateTseTransactionInDatabase(
              transactionDetail, 
              tseData, 
              "CANCELED", 
              `Abbruch: ${abortReason}`
            );
            
            // Erfolgreiche Abbruch-Nachricht senden
            publishUnifiedTseMessage(transactionDetail, {
              ...tseData,
              abort_reason: abortReason
            }, "canceled");
            
          } catch (cancelError) {
            log.error(`Fehler beim Abbrechen der TSE-Transaktion ${transactionId}:`, cancelError.message);
            
            // Fehler-Status in Datenbank setzen
            await updateTseTransactionInDatabase(
              transactionDetail, 
              null, 
              "ERROR", 
              `Fehler beim Abbrechen (${abortReason}): ${cancelError.message}`
            );
            
            // Fehler-Nachricht senden
            publishUnifiedTseMessage(transactionDetail, {
              tse_error: `Fehler beim Abbrechen: ${cancelError.message}`,
              abort_reason: abortReason,
              status: "error"
            }, "canceled");
          }
        }
      } catch (error) {
        log.error(`Fehler bei der Behandlung des Transaktionsabbruchs ${transactionId}:`, error.message);
        
        // Kritischer Fehler - Status trotzdem in Datenbank setzen
        await updateTseTransactionInDatabase(
          transactionDetail, 
          null, 
          "ERROR", 
          `Kritischer Fehler bei Abbruchbehandlung: ${error.message}`
        );
      }
    } else {
      log.warn(`TSE nicht verfügbar für Abbruch von Transaktion ${transactionId}, markiere nur in Datenbank`);
      
      // Auch ohne TSE den Abbruch in der Datenbank dokumentieren
      await updateTseTransactionInDatabase(
        transactionDetail, 
        null, 
        "CANCELED_NO_TSE", 
        `Abbruch ohne TSE-Verbindung: ${abortReason}`
      );
    }
    
  } catch (error) {
    log.error("Kritischer Fehler bei der Behandlung von Transaktionsabbruch:", error.message);
  }
}

// Anwendungsstart mit Statusanzeige
async function startApp() {
  try {
    log.info("Starte Anwendung mit strukturierter Initialisierung...");

    // Ladebildschirm erstellen
    createLoadingWindow();

    // Event-Listener für "Trotzdem fortfahren"-Button
    ipcMain.on('continue-anyway', () => {
      log.info("Benutzer hat 'Trotzdem fortfahren' geklickt, starte Anwendung trotz Fehler");

      // Alle Anforderungen überschreiben
      userOverrideRequirements = true;

      // Hauptfenster erstellen
      createWindow();

      // Kundendisplay initialisieren, falls konfiguriert
      if (config.customer_display && config.customer_display.enabled) {
        createCustomerDisplay();
      }

      // Ladebildschirm schließen
      if (loadingWindow && !loadingWindow.isDestroyed()) {
        loadingWindow.close();
      }
    });

    // Konfiguration laden
    updateComponentStatus("config", "loading", "Lade Konfiguration...");
    try {
      apiConfig = await loadApiConfig();
      updateComponentStatus("config", "success", "Konfiguration erfolgreich geladen");
    } catch (error) {
      log.warn("Fehler beim Laden der API-Konfiguration:", error.message);

      // Versuche, gespeicherte Konfiguration zu laden
      apiConfig = loadSavedApiConfig();
      if (apiConfig) {
        updateComponentStatus("config", "warning", "Lokale Konfiguration verwendet");
      } else {
        // Standardkonfiguration verwenden
        apiConfig = { /* Standardkonfiguration */ };
        updateComponentStatus("config", "error", "Standardkonfiguration verwendet");
      }
    }

    // API-Konfiguration an den Logger-Service übergeben
    loggerService.setApiConfig(apiConfig);

    // Wir führen alle Initialisierungen nacheinander aus, unabhängig von Fehlern

    // TSE-System initialisieren
    const tseProvider = config.tse_provider || "fiskaly";
    updateComponentStatus("tse", "loading", `${tseProvider.toUpperCase()} TSE wird initialisiert...`);

    try {
      // Kombinierte Konfiguration erstellen
      const combinedConfig = {
        ...apiConfig,
        api: config.api,
        printer: config.printer,
        fiskaly_config: config.fiskaly_config,
      };

      // TSE-Provider-spezifische Initialisierung
      if (tseProvider.toLowerCase() === "epson") {
        try {
          const EpsonTseClient = require("./epson-tse-client");
          tseClient = new EpsonTseClient(combinedConfig);
          log.info("Epson TSE-Client initialisiert");

          // Bei Epson TSE dauert die Verbindung länger und ist kritisch
          updateComponentStatus("tse", "loading", "Epson TSE-Verbindung wird hergestellt (kann bis zu 30 Sekunden dauern)...");

          const tseConnected = await tseClient.connect();

          // Zusätzliche Prüfung, ob die TSE wirklich verfügbar ist, aber nur wenn der Test noch nicht durchgeführt wurde
          let selfTestResult = true;
          if (!tseSelfTestPerformed) {
            selfTestResult = await tseClient.runSelfTest();
            tseSelfTestPerformed = true; // Markiere den Selbsttest als durchgeführt
          }

          if (tseConnected && selfTestResult) {
            updateComponentStatus("tse", "success", "Epson TSE-System verbunden");
          } else {
            updateComponentStatus("tse", "error", "Epson TSE-System konnte nicht verbunden werden");
            log.error("Epson TSE-Verbindung fehlgeschlagen");
            // Kein früher Abbruch mehr, wir setzen die Initialisierung fort
          }
        } catch (error) {
          log.error("Fehler bei der Initialisierung des Epson TSE-Clients:", error);
          updateComponentStatus("tse", "error", "Fehler bei Epson TSE-Initialisierung");
          // Kein früher Abbruch mehr, wir setzen die Initialisierung fort
        }
      } else {
        // Fiskaly TSE (schnellere Initialisierung)
        try {
          tseClient = new FiskalyClient(combinedConfig);
          log.info("Fiskaly TSE-Client initialisiert");

          const tseConnected = await tseClient.connect();

          // Zusätzliche Prüfung, ob die TSE wirklich verfügbar ist
          let tseAvailable = false;
          if (tseConnected) {
            try {
              // Versuche, den TSE-Status abzurufen, um zu prüfen, ob die TSE wirklich verfügbar ist
              const tseStatus = await tseClient.getTssStatus();
              tseAvailable = tseStatus && tseStatus.state === 'INITIALIZED';
            } catch (statusError) {
              log.error("Fehler beim Abrufen des TSE-Status:", statusError.message);
              tseAvailable = false;
            }
          }

          if (tseConnected && tseAvailable) {
            updateComponentStatus("tse", "success", "Fiskaly TSE-Verbindung hergestellt");
          } else {
            // Wenn die Verbindung fehlschlägt, setze den Status auf "error"
            updateComponentStatus("tse", "error", "Fiskaly TSE nicht verfügbar");
            log.error("Fiskaly TSE-Verbindung konnte nicht hergestellt werden");
          }
        } catch (error) {
          log.error("Fehler bei Fiskaly TSE-Initialisierung:", error);
          updateComponentStatus("tse", "error", "Fehler bei Fiskaly TSE-Initialisierung");
        }
      }
    } catch (error) {
      log.error("Fehler bei TSE-Initialisierung:", error);
      updateComponentStatus("tse", "error", "TSE-Fehler: " + error.message);
    }

    // MQTT initialisieren
    updateComponentStatus("mqtt", "loading", "Stelle MQTT-Verbindung her...");
    try {
      await setupMqttConnection();
      updateComponentStatus("mqtt", "success", "MQTT-Verbindung hergestellt");
    } catch (error) {
      log.error("Fehler bei MQTT-Verbindung:", error);
      updateComponentStatus("mqtt", "error", "MQTT-Fehler: " + error.message);
    }

    // Bondrucker initialisieren
    updateComponentStatus("printer", "loading", "Verbinde mit Bondrucker...");
    try {
      // Kombiniere Konfigurationen
      const combinedConfig = {
        ...apiConfig,
        api: config.api,
        printer: config.printer
      };

      // Initialisiere Drucker
      epsonPrinter = new EpsonPrinter(combinedConfig);

      // Prüfe tatsächliche Verbindung zum Drucker mit Ping
      log.info("Prüfe Verbindung zum Drucker mit Ping-Test...");
      const printerConnected = await epsonPrinter.checkPrinterConnection();

      if (printerConnected) {
        log.info("Drucker erfolgreich verbunden und erreichbar");
        updateComponentStatus("printer", "success", "Bondrucker verbunden und erreichbar");
      } else {
        log.warn("Drucker nicht erreichbar, aber Objekt initialisiert");
        // Setze Status auf "error" statt "warning", um den "Trotzdem fortfahren"-Button anzuzeigen
        updateComponentStatus("printer", "error", "Drucker nicht erreichbar");
      }
    } catch (error) {
      log.error("Fehler bei Drucker-Initialisierung:", error);
      updateComponentStatus("printer", "error", "Drucker nicht verfügbar");
    }

    // Kundendisplay-Status aktualisieren, aber nicht direkt initialisieren
    if (config.customer_display) {
      updateComponentStatus("display", "success", "Kundendisplay wird vorbereitet");
    } else {
      updateComponentStatus("display", "success", "Kundendisplay deaktiviert");
    }

    // Cart-Status aktualisieren
    updateComponentStatus("cart", "success", "Warenkorb wird vorbereitet");

    // Log zum Abschluss der Initialisierung
    log.info("Anwendungsstart abgeschlossen. Bereit für Zahlungen: " +
             (allRequiredComponentsReady() ? "Ja" : "Nein"));

    // Hinweis: Das automatische Erstellen des Hauptfensters und das Kundendisplay
    // wird jetzt von der updateComponentStatus-Funktion bei erfüllten Voraussetzungen gesteuert

  } catch (error) {
    log.error("Kritischer Fehler beim Anwendungsstart:", error);
  }
}

// Hilfsfunktion zur Erkennung von Netzwerkfehlern
function isNetworkError(error) {
  if (!error || !error.message) return false;

  const networkErrorMessages = [
    "ECONNREFUSED",
    "ETIMEDOUT",
    "ENOTFOUND",
    "nicht vorhandene Netzwerkverbindung",
    "network error",
    "Network Error",
    "getaddrinfo",
    "connect ETIMEDOUT",
    "connect ECONNREFUSED",
  ];

  return networkErrorMessages.some((msg) => error.message.includes(msg));
}

// App starten, wenn bereit
app.whenReady().then(async () => { // whenReady als async markieren
  createAppMenu();

  // Nur wenn wir die erste Instanz sind (gotTheLock ist true), starten wir den EasyTseService
  // Dies verhindert, dass eine zweite Instanz den EasyTseService beendet
  if (gotTheLock) {
    try {
      // Warten, bis der EasyTseService gestartet (oder fehlgeschlagen) ist
      await startEasyTseService();
      log.info("EasyTseService Start-Logik ausgeführt.");
    } catch (tseServiceError) {
      log.error(`Start des EasyTseService in whenReady fehlgeschlagen: ${tseServiceError.message}`);
    }
  }

  setupAutoUpdater(); //
  // startApp sollte idealerweise auch awaited werden, falls es weitere asynchrone Schritte enthält,
  // die vor checkForUpdates abgeschlossen sein müssen.
  await startApp(); //
  setTimeout(checkForUpdates, 5000); //
});

// Bei Aktivierung
app.on("activate", function () {
  if (BrowserWindow.getAllWindows().length === 0) startAppWithStatus();
});

// App schließen, wenn alle Fenster geschlossen sind
app.on("window-all-closed", function () {
  if (process.platform !== "darwin") app.quit();
});

// Funktion zum Ausführen des Tagesabschlusses am EC-Terminal
async function performECDayEnd() {
  try {
    // Prüfen, ob bereits ein EC-Abschluss läuft
    if (ecDayEndInProgress) {
      log.warn("EC-Abschluss läuft bereits, doppelter Aufruf wird ignoriert");
      return { success: false, error: "EC-Abschluss läuft bereits" };
    }

    // Markieren, dass ein EC-Abschluss läuft
    ecDayEndInProgress = true;

    log.info("Starte EC-Terminal Tagesabschluss");

    // Prüfen, ob Terminal-Client verfügbar
    if (!zvtClient) {
      log.error("ZVT-Client nicht initialisiert, Tagesabschluss nicht möglich");
      dialog.showMessageBox({
        type: "error",
        title: "EC-Terminal Fehler",
        message: "EC-Terminal nicht initialisiert. Bitte starten Sie die Anwendung neu.",
        buttons: ["OK"],
      });
      ecDayEndInProgress = false; // Zurücksetzen der Variable
      return;
    }

    // Prüfen, ob das Terminal beschäftigt ist
    if (zvtClient.isBusy && zvtClient.isBusy()) {
      log.warn("Terminal ist beschäftigt, Tagesabschluss wird abgebrochen");
      ecDayEndInProgress = false; // Zurücksetzen der Variable
      return { success: false, error: "Terminal ist beschäftigt" };
    }

    // Separates Modal-Fenster für Tagesabschluss anzeigen
    const transactionId = "day-end-" + Date.now();

    try {
      // Prüfen, ob bereits ein Modal-Fenster existiert
      if (paymentModalWindow && !paymentModalWindow.isDestroyed()) {
        log.info("Verwende bestehendes Modal-Fenster für Tagesabschluss");

        // Bestehendes Modal-Fenster aktualisieren
        updatePaymentModal({
          status: "processing",
          message: "Tagesabschluss wird gestartet...",
          statusText: "Tagesabschluss",
          transactionId: transactionId,
          isEndOfDay: true,
        });
      } else {
        // Neues Modal-Fenster erstellen
        log.info("Erstelle neues Modal-Fenster für Tagesabschluss");
        createPaymentModalWindow({
          isEndOfDay: true,
          transactionId: transactionId,
        });

        // Kurze Verzögerung, um sicherzustellen, dass das Modal-Fenster geladen ist
        await new Promise(resolve => setTimeout(resolve, 500));

        // Modal mit Startinformation aktualisieren
        updatePaymentModal({
          status: "processing",
          message: "Tagesabschluss wird gestartet...",
          statusText: "Tagesabschluss",
          transactionId: transactionId,
          isEndOfDay: true,
        });
      }
    } catch (modalError) {
      log.error("Fehler beim Erstellen/Aktualisieren des Modal-Fensters:", modalError);
      // Wir setzen den Prozess trotzdem fort, auch wenn das Modal-Fenster nicht erstellt werden konnte
    }

    try {
      // Direkt die direkte Methode aufrufen, die ohne ZVTService auskommt
      log.info("Rufe zvtClient.performDirectEndOfDay() auf");
      const endOfDayResult = await zvtClient.performDirectEndOfDay();
      log.info("Ergebnis des Tagesabschlusses:", JSON.stringify(endOfDayResult));

      // Ergebnis verarbeiten
      if (endOfDayResult && endOfDayResult.success) {
        log.info("Tagesabschluss erfolgreich durchgeführt");

        // Prüfen, ob das Modal-Fenster noch existiert
        if (paymentModalWindow && !paymentModalWindow.isDestroyed()) {
          updatePaymentModal({
            status: "success",
            message: "Tagesabschluss erfolgreich durchgeführt",
            statusText: "Abgeschlossen",
            transactionId: transactionId,
            isEndOfDay: true,
            hasMerchantReceipt: !!endOfDayResult.merchantReceipt
          });

          // Modal-Fenster nach kurzer Verzögerung automatisch schließen
          setTimeout(() => {
            log.info("Schließe Modal-Fenster nach erfolgreichem Tagesabschluss");
            if (paymentModalWindow && !paymentModalWindow.isDestroyed()) {
              paymentModalWindow.close();
              paymentModalWindow = null;
            }
          }, 3000); // 3 Sekunden Verzögerung, damit der Benutzer die Erfolgsmeldung sehen kann
        }

        // Zurücksetzen der Variable nach einer Verzögerung, um doppelte Aufrufe zu vermeiden
        setTimeout(() => {
          ecDayEndInProgress = false;
          log.info("EC-Abschluss-Status zurückgesetzt");
        }, 10000); // 10 Sekunden Verzögerung

        return endOfDayResult;
      } else {
        // Fehlerdetails anzeigen
        const errorCode = endOfDayResult?.statusCode || "FF";
        const errorMessage = endOfDayResult?.error || "Fehler beim Tagesabschluss";

        log.error(`Tagesabschluss fehlgeschlagen: Code ${errorCode}, Meldung: ${errorMessage}`);

        // Prüfen, ob das Modal-Fenster noch existiert
        if (paymentModalWindow && !paymentModalWindow.isDestroyed()) {
          updatePaymentModal({
            status: "error",
            message: `Fehler: ${errorMessage}`,
            statusText: `Fehlgeschlagen (${errorCode})`,
            transactionId: transactionId,
            isEndOfDay: true,
          });
        }

        // Zurücksetzen der Variable
        ecDayEndInProgress = false;

        return endOfDayResult;
      }
    } catch (error) {
      log.error("Fehler bei Tagesabschluss-Ausführung:", error);

      // Modal-Fenster aktualisieren, falls vorhanden
      if (paymentModalWindow && !paymentModalWindow.isDestroyed()) {
        updatePaymentModal({
          status: "error",
          message: `Unerwarteter Fehler: ${error.message}`,
          statusText: "Systemfehler",
          isEndOfDay: true,
        });
      }

      // Zurücksetzen der Variable
      ecDayEndInProgress = false;

      return { success: false, error: error.message };
    }
  } catch (outerError) {
    log.error("Kritischer Fehler bei Tagesabschluss-Ausführung:", outerError);

    // Zurücksetzen der Variable auch bei kritischen Fehlern
    ecDayEndInProgress = false;

    return { success: false, error: outerError.message };
  }
}

// Aufräumen vor dem Beenden
app.on("before-quit", async () => {
  // MQTT-Verbindung schließen
  if (mqttClient) {
    log.info("Schließe MQTT-Verbindung");
    if (typeof mqttClient.disconnect === "function") {
      mqttClient.disconnect();
    } else if (typeof mqttClient.end === "function") {
      mqttClient.end(true);
    }
  }
  if (easyTseProcess) {
    log.info("Beende EasyTseService...");
    try {
      // Windows-spezifisch: Process tree beenden
      if (process.platform === "win32") {
        exec(`taskkill /pid ${easyTseProcess.pid} /t /f`, (error) => {
          if (error) {
            log.error(
              `Fehler beim Beenden des EasyTseService: ${error.message}`
            );
          } else {
            log.info("EasyTseService erfolgreich beendet");
          }
        });
      } else {
        easyTseProcess.kill();
      }
    } catch (error) {
      log.error(`Fehler beim Beenden des EasyTseService: ${error.message}`);
    }
  }

  //  ZVT-Terminal abmelden
  if (zvtClient) {
    log.info("Melde ZVT-Terminal ab");
    try {
      await zvtClient.disconnect();
    } catch (error) {
      log.error("Fehler beim Abmelden des ZVT-Terminals:", error);
    }
  }

  if (tseClient) {
    log.info("Räume TSE-Client auf");
    if (typeof tseClient.cleanup === "function") {
      tseClient.cleanup();
    }
  }

  //  Warenkorbüberwachung beenden
  if (global.cartMonitoringInterval) {
    clearInterval(global.cartMonitoringInterval);
    log.info("Warenkorbüberwachung beendet");
  }

  //  Kundendisplay schließen
  if (customerDisplayWindow && !customerDisplayWindow.isDestroyed()) {
    customerDisplayWindow.close();
    log.info("Kundendisplay geschlossen");
  }

  // Modal-Fenster schließen, falls noch geöffnet
  closePaymentModal();

  // Lade-Fenster schließen, falls noch geöffnet
  if (loadingWindow && !loadingWindow.isDestroyed()) {
    loadingWindow.close();
    log.info("Lade-Fenster geschlossen");
  }

  // Bereinigung der Log-Dateien durchführen, wenn in der Konfiguration aktiviert
  if (config.logging?.cleanupOnExit) {
    log.info("Führe Log-Bereinigung vor Beenden durch");
    try {
      loggerService.cleanupOldLogs(config.logging.maxLogAgeDays || 30);
    } catch (error) {
      log.error("Fehler bei der Log-Bereinigung:", error);
    }
  }
});