<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Transaktions-Monitoring</title>
  <style>
    :root {
      --primary-color: #2c3e50;
      --secondary-color: #3498db;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-color: #ecf0f1;
      --dark-color: #34495e;
      --border-color: #ddd;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f8f9fa;
      padding: 20px;
    }

    .container {
      max-width: 100%;
      margin: 0 auto;
    }

    h1 {
      color: var(--primary-color);
      margin-bottom: 20px;
      border-bottom: 2px solid var(--secondary-color);
      padding-bottom: 10px;
    }

    .filter-section {
      background-color: white;
      padding: 15px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }

    .filter-row {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 15px;
    }

    .filter-group {
      flex: 1;
      min-width: 200px;
    }

    .filter-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: var(--dark-color);
    }

    .filter-group select,
    .filter-group input[type="text"],
    .filter-group input[type="date"] { /* Spezifischer für Text- und Datumseingaben */
      width: 100%;
      padding: 8px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
    }

    .filter-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 10px; /* Etwas Abstand nach oben */
    }

    button {
      padding: 8px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
      transition: background-color 0.3s;
    }

    .btn-primary {
      background-color: var(--secondary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: #2980b9;
    }

    .btn-secondary {
      background-color: var(--light-color);
      color: var(--dark-color);
    }

    .btn-secondary:hover {
      background-color: #bdc3c7;
    }

    .table-wrapper { /* NEU: Wrapper für horizontales Scrollen */
      overflow-x: auto;
      width: 100%;
    }

    .transactions-table {
      width: 100%;
      border-collapse: collapse;
      background-color: white;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      border-radius: 5px;
      overflow: hidden;
    }

    .transactions-table th,
    .transactions-table td {
      padding: 10px 12px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
      font-size: 0.88em;
      white-space: nowrap;
    }

    .transactions-table th {
      background-color: var(--primary-color);
      color: white;
      font-weight: bold;
    }

    .transactions-table tr:hover {
      background-color: #f5f5f5;
    }

    .status-badge {
      display: inline-block;
      padding: 3px 8px;
      border-radius: 12px;
      font-size: 0.95em;
      font-weight: bold;
      white-space: nowrap;
    }

    .status-completed, .status-finished {
      background-color: var(--success-color);
      color: white;
    }

    .status-pending, .status-created {
      background-color: var(--warning-color);
      color: white;
    }

    .status-aborted, .status-canceled, .status-failed, .status-error {
      background-color: var(--danger-color);
      color: white;
    }
     .status-unknown {
      background-color: #d6d8d9;
      color: #343a40;
    }

    /* Styles for Status Icons */
    .status-icon-cell {
      text-align: center; /* Center the icon in the cell */
    }

    .status-icon {
      display: inline-flex; /* Use inline-flex for better alignment */
      justify-content: center;
      align-items: center;
      width: 24px;
      height: 24px;
      border-radius: 50%; /* Make it round */
      font-size: 1.1em; /* Slightly larger icon */
      font-weight: bold;
      color: white; /* Default icon color */
    }

    .status-icon.status-success-icon {
      background-color: var(--success-color);
    }

    .status-icon.status-pending-icon {
      background-color: var(--warning-color);
    }

    .status-icon.status-error-icon {
      background-color: var(--danger-color);
    }

    .status-icon.status-unknown-icon {
      background-color: #d6d8d9;
      color: #343a40;
    }

    /* Tooltip für Status-Icons */
    .status-icon-container {
      position: relative;
      display: inline-block;
    }

    .status-icon-container .tooltip-text {
      visibility: hidden;
      width: 120px;
      background-color: #555;
      color: #fff;
      text-align: center;
      border-radius: 6px;
      padding: 5px;
      position: absolute;
      z-index: 1;
      bottom: 125%;
      left: 50%;
      margin-left: -60px;
      opacity: 0;
      transition: opacity 0.3s;
      font-size: 0.8em;
      font-weight: normal;
    }

    .status-icon-container:hover .tooltip-text {
      visibility: visible;
      opacity: 1;
    }


    .pagination {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;
      background-color: white;
      padding: 10px 15px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .pagination-info {
      font-size: 0.9em;
      color: #666;
    }

    .pagination-controls {
      display: flex;
      gap: 10px;
    }

    .detail-modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      z-index: 1000;
      overflow: auto;
      padding: 20px;
    }

    .detail-content {
      background-color: white;
      margin: 50px auto;
      padding: 20px;
      width: 80%;
      max-width: 900px;
      border-radius: 5px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.3);
      position: relative;
    }

    .close-modal {
      position: absolute;
      top: 10px;
      right: 15px;
      font-size: 24px;
      font-weight: bold;
      cursor: pointer;
      color: #aaa;
    }

    .close-modal:hover {
      color: #333;
    }

    .detail-tabs {
      display: flex;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 15px;
    }

    .detail-tab {
      padding: 10px 15px;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      margin-right: 5px;
    }

    .detail-tab.active {
      border-bottom-color: var(--secondary-color);
      font-weight: bold;
      color: var(--secondary-color);
    }

    .detail-section {
      display: none;
    }

    .detail-section.active {
      display: block;
    }

    .detail-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 15px;
    }

    .detail-item {
      margin-bottom: 10px;
    }

    .detail-label {
      font-weight: bold;
      color: var(--dark-color);
      margin-bottom: 5px;
      font-size: 0.9em;
    }

    .detail-value {
      padding: 8px;
      background-color: #f8f9fa;
      border: 1px solid #eee;
      border-radius: 3px;
      word-break: break-all;
      font-size: 0.9em;
    }

    .detail-content pre {
      white-space: pre-wrap;
      font-family: monospace;
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
    }
    
    .receipt-content {
      white-space: pre-wrap;
      font-family: monospace;
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 10px;
    }

    .receipt-print-button {
      margin-top: 15px;
      display: flex;
      justify-content: center;
    }

    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255,255,255,0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 2000;
    }

    .spinner {
      width: 50px;
      height: 50px;
      border: 5px solid var(--light-color);
      border-top: 5px solid var(--secondary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .error-message {
      background-color: #f8d7da;
      color: #721c24;
      padding: 10px 15px;
      border-radius: 5px;
      margin-bottom: 20px;
      display: none;
    }

    /* Druckstile */
    @media print {
      body {
        padding: 0;
        background-color: white;
      }

      .container {
        max-width: 100%;
        width: 100%;
      }

      h1 {
        font-size: 18pt;
        margin-bottom: 10px;
      }

      .filter-section, .pagination, .detail-modal, .loading-overlay, .error-message,
      .btn-primary, .btn-secondary, #print-list {
        display: none !important;
      }

      .table-wrapper {
        overflow: visible;
      }

      .transactions-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 9pt;
      }

      .transactions-table th, .transactions-table td {
        padding: 5px;
        border: 1px solid #ddd;
      }

      .status-icon-container .tooltip-text {
        display: none;
      }

      .status-icon {
        width: 16px;
        height: 16px;
        font-size: 0.8em;
      }

      .print-header {
        display: block;
        margin-bottom: 10px;
      }

      .print-footer {
        display: block;
        margin-top: 10px;
        font-size: 8pt;
        text-align: center;
        border-top: 1px solid #ddd;
        padding-top: 5px;
      }
    }

    @media screen {
      .print-header, .print-footer {
        display: none;
      }
    }

    /* Drucken-Button-Stil */
    .print-icon {
      font-style: normal;
      margin-right: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="print-header">
      <h2>Transaktionsübersicht</h2>
      <p>Datum: <span id="print-date"></span></p>
      <p>Filter: <span id="print-filter-info"></span></p>
    </div>

    <h1>Transaktions-Monitoring</h1>

    <div class="error-message" id="error-container"></div>

    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-group">
          <label for="filter-payment-method">Zahlungsart</label>
          <select id="filter-payment-method">
            <option value="">Alle</option>
            <option value="CASH">Bargeld</option>
            <option value="CARD">Kartenzahlung</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="filter-status">Zahlstatus</label>
          <select id="filter-status">
            <option value="">Alle</option>
            <option value="COMPLETED">Abgeschlossen</option>
            <option value="PENDING">Ausstehend</option>
            <option value="ABORTED">Abgebrochen</option>
            <option value="FAILED">Fehlgeschlagen</option>
          </select>
        </div>
        <div class="filter-group">
          <label for="filter-checkout-id">Checkout-ID</label>
          <input type="text" id="filter-checkout-id" placeholder="Checkout-ID eingeben">
        </div>
      </div>
      <div class="filter-row">
        <div class="filter-group">
          <label for="filter-date-from">Datum von</label>
          <input type="date" id="filter-date-from">
        </div>
        <div class="filter-group">
          <label for="filter-date-to">Datum bis</label>
          <input type="date" id="filter-date-to">
        </div>
      </div>
      <div class="filter-buttons">
        <button class="btn-secondary" id="reset-filters">Filter zurücksetzen</button>
        <button class="btn-primary" id="apply-filters">Filter anwenden</button>
        <button class="btn-secondary" id="print-list" title="Aktuelle Liste drucken"><i class="print-icon">🖨️</i> Drucken</button>
      </div>
    </div>

    <div class="table-wrapper">
      <table class="transactions-table" id="transactions-table">
        <thead>
          <tr>
            <th>Datum</th>
            <th>Transaction-ID</th>
            <th>Checkout-ID</th>
            <th>Betrag</th>
            <th>Zahlungsart</th>
            <th>TSE</th>
            <th>Bezahlt</th>
            <th>EC</th>
            <th>Bar</th>
            <th>Aktionen</th>
          </tr>
        </thead>
        <tbody id="transactions-body">
          </tbody>
      </table>
    </div>

    <div class="pagination">
      <div class="pagination-info" id="pagination-info">
        Zeige 0 von 0 Transaktionen
      </div>
      <div class="pagination-controls">
        <button class="btn-secondary" id="prev-page" disabled>Zurück</button>
        <button class="btn-secondary" id="next-page" disabled>Weiter</button>
      </div>
    </div>
  </div>

  <div class="detail-modal" id="detail-modal">
    <div class="detail-content">
      <span class="close-modal" id="close-modal-btn">&times;</span> <h2>Transaktionsdetails</h2>

      <div class="detail-tabs">
        <div class="detail-tab active" data-tab="tse">TSE-Daten</div>
        <div class="detail-tab" data-tab="payment">Zahlungsdaten</div>
        <div class="detail-tab" data-tab="receipts">EC-Belege</div>
      </div>

      <div class="detail-section active" id="tse-section">
        <h3>TSE Hauptdaten</h3>
        <div class="detail-grid" id="tse-details">
          </div>
      </div>

      <div class="detail-section" id="payment-section">
        <h3>Zahlungsinformationen</h3>
        <div class="detail-grid" id="payment-details">
          </div>
      </div>

      <div class="detail-section" id="receipts-section">
        <h3>Kundenbeleg</h3>
        <div class="receipt-content" id="customer-receipt">
          </div>

        <h3 style="margin-top: 20px;">Händlerbeleg</h3>
        <div class="receipt-content" id="merchant-receipt">
          </div>
      </div>
    </div>

    <div class="print-footer">
      <p>Erstellt am <span id="print-footer-date"></span> - Seite <span class="page-number"></span></p>
    </div>
  </div>

  <div class="loading-overlay" id="loading-overlay" style="display: none;"> <div class="spinner"></div>
  </div>

  <script>
    // Globale Variablen
    let currentPage = 1;
    let pageSize = 50;
    let totalTransactions = 0;
    let currentFilters = {};

    // DOM-Elemente
    const transactionsBody = document.getElementById('transactions-body');
    const paginationInfo = document.getElementById('pagination-info');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    const loadingOverlay = document.getElementById('loading-overlay');
    const errorContainer = document.getElementById('error-container');

    const detailModal = document.getElementById('detail-modal');
    const closeModalBtn = document.getElementById('close-modal-btn');
    const detailTabs = document.querySelectorAll('.detail-tab');
    const detailSections = document.querySelectorAll('.detail-section');
    const tseDetailsContainer = document.getElementById('tse-details');
    const paymentDetailsContainer = document.getElementById('payment-details');
    const customerReceiptContainer = document.getElementById('customer-receipt');
    const merchantReceiptContainer = document.getElementById('merchant-receipt');

    const filterPaymentMethod = document.getElementById('filter-payment-method');
    const filterStatus = document.getElementById('filter-status'); // This is the "Status (Zahlung Gesamt)" filter
    const filterCheckoutId = document.getElementById('filter-checkout-id');
    const filterDateFrom = document.getElementById('filter-date-from');
    const filterDateTo = document.getElementById('filter-date-to');
    const applyFiltersBtn = document.getElementById('apply-filters');
    const resetFiltersBtn = document.getElementById('reset-filters');
    const printListBtn = document.getElementById('print-list');

    function formatReceipt(receiptData) {
      if (!receiptData || receiptData === 'null' || receiptData === '[]' || receiptData === '{}') return 'Kein Beleg verfügbar';
      try {
        let receiptLines = [];
        if (typeof receiptData === 'string') {
          try { receiptLines = JSON.parse(receiptData); }
          catch (e) { return receiptData.replace(/\\n/g, '\n'); }
        } else if (Array.isArray(receiptData)) {
          receiptLines = receiptData;
        } else { return 'Beleg in unbekanntem Format vorhanden'; }
        if (!Array.isArray(receiptLines)) return 'Beleg vorhanden, aber in falschem Array-Format';
        return receiptLines.join('\n');
      } catch (error) {
        console.error('Fehler beim Formatieren des Belegs:', error, "Rohdaten:", receiptData);
        return 'Fehler beim Anzeigen des Belegs';
      }
    }

    detailTabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const tabId = tab.getAttribute('data-tab');
        detailTabs.forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
        detailSections.forEach(section => {
          section.classList.remove('active');
          if (section.id === `${tabId}-section`) section.classList.add('active');
        });
      });
    });

    closeModalBtn.addEventListener('click', () => { detailModal.style.display = 'none'; });
    window.addEventListener('click', (event) => { if (event.target === detailModal) detailModal.style.display = 'none'; });

    applyFiltersBtn.addEventListener('click', () => {
      let dateFrom = filterDateFrom.value || null;
      let dateTo = filterDateTo.value || null;
      if (dateFrom) dateFrom = Math.floor(new Date(dateFrom + "T00:00:00").getTime() / 1000);
      if (dateTo) {
        const toDate = new Date(dateTo + "T00:00:00");
        toDate.setHours(23, 59, 59, 999);
        dateTo = Math.floor(toDate.getTime() / 1000);
      }

      currentFilters = {
        payment_method: filterPaymentMethod.value || null,
        // No longer using tse_state directly from this filter if it's meant for overall payment status
        // currentFilters.tse_state = filterStatus.value || null; // Old line
        checkout_id: filterCheckoutId.value || null,
        date_from: dateFrom,
        date_to: dateTo
      };

      // Logic for the "Status (Zahlung Gesamt)" filter
      const selectedOverallStatus = filterStatus.value;
      if (selectedOverallStatus) {
        // This key 'filter_by_overall_payment_status' will be interpreted by the backend
        // to apply the complex logic for "Zahlstatus Gesamt"
        currentFilters.filter_by_overall_payment_status = selectedOverallStatus;
      } else {
        delete currentFilters.filter_by_overall_payment_status;
      }

      Object.keys(currentFilters).forEach(key => { if (currentFilters[key] === null || currentFilters[key] === '') delete currentFilters[key]; });
      currentPage = 1;
      loadTransactions();
    });

    resetFiltersBtn.addEventListener('click', () => {
      filterPaymentMethod.value = '';
      filterStatus.value = '';
      filterCheckoutId.value = '';
      filterDateFrom.value = '';
      filterDateTo.value = '';
      currentFilters = {};
      currentPage = 1;
      loadTransactions();
    });

    prevPageBtn.addEventListener('click', () => { if (currentPage > 1) { currentPage--; loadTransactions(); } });
    nextPageBtn.addEventListener('click', () => { if (currentPage * pageSize < totalTransactions) { currentPage++; loadTransactions(); } });

    // Event-Listener für den Drucken-Button
    printListBtn.addEventListener('click', printTransactionsList);

    async function loadTransactions() {
      try {
        showLoading(true);
        errorContainer.style.display = 'none'; errorContainer.textContent = '';
        const result = await window.monitoringApi.getTransactions({ page: currentPage, pageSize: pageSize, filter: { ...currentFilters } });
        if (result.success) {
          renderTransactions(result.transactions);
          totalTransactions = result.totalCount;
          updatePagination();
        } else {
          showError(result.error || 'Fehler beim Laden der Transaktionen');
        }
      } catch (error) {
        console.error("Fehler in loadTransactions:", error);
        showError(error.message || 'Unbekannter clientseitiger Fehler beim Laden der Transaktionen');
      } finally {
        showLoading(false);
      }
    }

    function renderTransactions(transactions) {
      transactionsBody.innerHTML = '';
      if (!transactions || transactions.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="10" style="text-align: center;">Keine Transaktionen für die aktuellen Filter gefunden.</td>`;
        transactionsBody.appendChild(row);
        return;
      }

      transactions.forEach(transaction => {
        const row = document.createElement('tr');
        const date = window.monitoringApi.formatDate(transaction.tse_created_at);

        // Funktion zum Erstellen von Status-Icons mit Tooltip
        function createStatusIcon(status, tooltipText) {
            const statusLower = (status || 'unknown').toLowerCase();
            let icon = '❓';
            let iconClass = 'status-unknown-icon';

            if (['completed', 'finished'].includes(statusLower)) {
                icon = '✓'; // Checkmark
                iconClass = 'status-success-icon';
            } else if (['pending', 'created', 'active', 'initialized'].includes(statusLower)) {
                icon = '⏳'; // Hourglass
                iconClass = 'status-pending-icon';
            } else if (['aborted', 'canceled', 'failed', 'error', 'expired', 'deleted', 'uninitialized', 'disabled', 'replaced_by_card'].includes(statusLower)) {
                icon = '✗'; // X mark
                iconClass = 'status-error-icon';
            }

            return `<div class="status-icon-container"><span class="status-icon ${iconClass}">${icon}</span><span class="tooltip-text">${tooltipText}</span></div>`;
        }

        // TSE Status Icon
        const tseState = (transaction.tse_state || 'unknown').toLowerCase();
        const tseStatusText = transaction.tse_state ? window.monitoringApi.formatStatus(transaction.tse_state) : 'Unbekannt';
        const tseStatusHtml = createStatusIcon(tseState, tseStatusText);

        // Card Status Icon
        const cardStatusText = transaction.card_status ? window.monitoringApi.formatStatus(transaction.card_status) : 'Keine EC-Zahlung';
        const cardStatusHtml = createStatusIcon(transaction.card_status, cardStatusText);

        // Cash Status Icon
        const cashStatusText = transaction.cash_status ? window.monitoringApi.formatStatus(transaction.cash_status) : 'Keine Barzahlung';
        const cashStatusHtml = createStatusIcon(transaction.cash_status, cashStatusText);

        // Funktion zur Bestimmung des Gesamtzahlungsstatus
        function determineOverallPaymentStatus(transaction) {
            const isCardSuccess = transaction.card_status === 'COMPLETED' || transaction.card_status === 'FINISHED';
            const isCashSuccess = transaction.cash_status === 'COMPLETED' || transaction.cash_status === 'FINISHED';
            const isCardPending = transaction.card_status === 'PENDING' || transaction.card_status === 'CREATED';
            const isCashPending = transaction.cash_status === 'PENDING' || transaction.cash_status === 'CREATED';
            const isCardFailure = ['ABORTED', 'CANCELED', 'FAILED', 'ERROR'].includes(transaction.card_status);
            const isCashFailure = ['ABORTED', 'CANCELED', 'FAILED', 'ERROR'].includes(transaction.cash_status);

            let status = null;

            if (isCardSuccess || isCashSuccess) {
                status = 'COMPLETED';
            } else if (isCardPending || isCashPending) {
                if ((isCardFailure && isCashPending) || (isCashFailure && isCardPending) || (isCardPending && isCashPending) ||
                    (isCardPending && transaction.cash_status == null) || (isCashPending && transaction.card_status == null)) {
                    status = 'PENDING';
                }
            }

            if (!status) {
                if (isCardFailure && isCashFailure) status = transaction.card_status;
                else if (isCardFailure && (transaction.cash_status == null || !isCashPending)) status = transaction.card_status;
                else if (isCashFailure && (transaction.card_status == null || !isCardPending)) status = transaction.cash_status;
            }

            if (!status && (isCardPending || isCashPending)) status = 'PENDING';

            // Wenn immer noch null und einer der einzelnen Status auf einen Fehler hinweist
            if (!status && (isCardFailure || isCashFailure)) {
                status = isCardFailure ? transaction.card_status : transaction.cash_status;
            }

            return status;
        }

        // Overall Payment Status Icon
        const overallPaymentStatus = determineOverallPaymentStatus(transaction);
        const overallPaymentStatusText = overallPaymentStatus ? window.monitoringApi.formatStatus(overallPaymentStatus) : 'Unbekannt';
        const overallPaymentStatusHtml = createStatusIcon(overallPaymentStatus, overallPaymentStatusText);
        const detailButtonId = transaction.tse_transaction_id;

        row.innerHTML = `
          <td>${date}</td>
          <td>${detailButtonId || '-'}</td>
          <td>${transaction.checkout_id || '-'}</td>
          <td>${window.monitoringApi.formatAmount(transaction.payment_amount)}</td>
          <td>${window.monitoringApi.formatPaymentMethod(transaction.effective_payment_method)}</td>
          <td class="status-icon-cell">${tseStatusHtml}</td>
          <td class="status-icon-cell">${overallPaymentStatusHtml}</td>
          <td class="status-icon-cell">${cardStatusHtml}</td>
          <td class="status-icon-cell">${cashStatusHtml}</td>
          <td><button class="btn-primary detail-btn" data-id="${detailButtonId}">Details</button></td>
        `;
        transactionsBody.appendChild(row);
      });

      document.querySelectorAll('.detail-btn').forEach(btn => {
        btn.removeEventListener('click', handleDetailButtonClick);
        btn.addEventListener('click', handleDetailButtonClick);
      });
    }

    async function handleDetailButtonClick(event) {
      const transactionId = event.target.getAttribute('data-id');
      if (transactionId) await showTransactionDetails(transactionId);
      else showError("Keine Transaktions-ID für Details gefunden.");
    }

    async function showTransactionDetails(transactionId) {
      try {
        showLoading(true); errorContainer.style.display = 'none'; errorContainer.textContent = '';
        const result = await window.monitoringApi.getTransactionDetails(transactionId);
        if (result.success) {
          tseDetailsContainer.innerHTML = '';
          if (result.transaction) {
            const dateFieldsTSE = ['created_at', 'tse_created_at', 'start', 'end', 'tse_start', 'tse_end', 'last_modified_at'];
            Object.entries(result.transaction).forEach(([key, value]) => {
              let displayValue = value;
              if (dateFieldsTSE.includes(key)) {
                if (value && !isNaN(Number(value))) displayValue = window.monitoringApi.formatDate(value);
                else if (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)) displayValue = window.monitoringApi.formatDate(new Date(value).getTime() / 1000);
                else if (typeof value === 'string' && value.match(/^\d{2}\.\d{2}\.\d{4}, \d{2}:\d{2}:\d{2}$/)) displayValue = value;
              } else if (key === 'process_data') displayValue = value ? (String(value).length > 200 ? String(value).substring(0, 200) + '...' : String(value)) : '-';
              const detailItem = document.createElement('div'); detailItem.className = 'detail-item';
              detailItem.innerHTML = `<div class="detail-label">${formatFieldName(key)}</div><div class="detail-value">${displayValue !== null && displayValue !== undefined ? displayValue : '-'}</div>`;
              tseDetailsContainer.appendChild(detailItem);
            });
          } else { tseDetailsContainer.innerHTML = '<p>Keine TSE-Daten verfügbar</p>'; }

          paymentDetailsContainer.innerHTML = '';
          if (result.paymentData) {
            const hiddenPaymentFields = ['receipt_number', 'trace_number', 'terminal_id'];
            const dateFieldsPayment = ['created_at', 'completed_at', 'card_created_at', 'card_completed_at', 'cash_created_at', 'cash_completed_at'];
            Object.entries(result.paymentData).forEach(([key, value]) => {
              if (hiddenPaymentFields.includes(key.toLowerCase())) return;
              let displayValue = value;
              if (dateFieldsPayment.includes(key)) {
                if (value && !isNaN(Number(value))) displayValue = window.monitoringApi.formatDate(value);
                else if (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)) displayValue = window.monitoringApi.formatDate(new Date(value).getTime() / 1000);
                else if (typeof value === 'string' && value.match(/^\d{2}\.\d{2}\.\d{4}, \d{2}:\d{2}:\d{2}$/)) displayValue = value;
              } else if (['amount', 'card_amount', 'cash_amount', 'payment_amount'].includes(key)) displayValue = window.monitoringApi.formatAmount(value);
              else if (['status', 'card_status', 'cash_status', 'tse_state'].includes(key)) displayValue = window.monitoringApi.formatStatus(value);
              else if (['type', 'payment_method', 'effective_payment_method', 'initial_payment_method', 'card_type', 'tse_type'].includes(key)) displayValue = window.monitoringApi.formatPaymentMethod(value);
              if (key === 'customer_receipt' || key === 'merchant_receipt') return;
              const detailItem = document.createElement('div'); detailItem.className = 'detail-item';
              detailItem.innerHTML = `<div class="detail-label">${formatFieldName(key)}</div><div class="detail-value">${displayValue !== null && displayValue !== undefined ? displayValue : '-'}</div>`;
              paymentDetailsContainer.appendChild(detailItem);
            });
          } else { paymentDetailsContainer.innerHTML = '<p>Keine Zahlungsdaten verfügbar</p>'; }

          // Kundenbeleg Container leeren und neu befüllen
          customerReceiptContainer.innerHTML = 'Kein Kundenbeleg verfügbar';
          merchantReceiptContainer.innerHTML = 'Kein Händlerbeleg verfügbar';
          
          // Transaktions-ID für den Druck speichern
          const currentTransactionId = result.transaction?.tse_transaction_id || result.transaction?.transaction_id;
          
          if (result.paymentData) {
            // Wenn Kundenbeleg vorhanden, dann anzeigen und Druckbutton hinzufügen
            if (result.paymentData.customer_receipt) {
              const customerReceiptText = formatReceipt(result.paymentData.customer_receipt);
              const customerPrintButton = `<div class="receipt-print-button"><button class="btn btn-primary" onclick="printReceipt('${currentTransactionId}', 'customer')">Kundenbeleg drucken</button></div>`;
              customerReceiptContainer.innerHTML = `<div class="receipt-content">${customerReceiptText}</div>${customerPrintButton}`;
            }
            
            // Wenn Händlerbeleg vorhanden, dann anzeigen und Druckbutton hinzufügen
            if (result.paymentData.merchant_receipt) {
              const merchantReceiptText = formatReceipt(result.paymentData.merchant_receipt);
              const merchantPrintButton = `<div class="receipt-print-button"><button class="btn btn-primary" onclick="printReceipt('${currentTransactionId}', 'merchant')">Händlerbeleg drucken</button></div>`;
              merchantReceiptContainer.innerHTML = `<div class="receipt-content">${merchantReceiptText}</div>${merchantPrintButton}`;
            }
          }
          detailModal.style.display = 'block';
          const activeTab = document.querySelector('.detail-tab.active') || detailTabs[0];
          if (activeTab) activeTab.click();
        } else { showError(result.error || 'Fehler beim Laden der Transaktionsdetails'); }
      } catch (error) {
        console.error("Fehler in showTransactionDetails:", error);
        showError(error.message || 'Unbekannter clientseitiger Fehler beim Laden der Details');
      } finally { showLoading(false); }
    }

    function formatFieldName(key) { return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()); }
    function updatePagination() {
      const startRecord = totalTransactions > 0 ? (currentPage - 1) * pageSize + 1 : 0;
      const endRecord = Math.min(currentPage * pageSize, totalTransactions);
      paginationInfo.textContent = `Zeige ${startRecord}-${endRecord} von ${totalTransactions} Transaktionen`;
      prevPageBtn.disabled = currentPage <= 1;
      nextPageBtn.disabled = currentPage * pageSize >= totalTransactions;
    }
    function showLoading(show) { loadingOverlay.style.display = show ? 'flex' : 'none'; }
    function showError(message) { errorContainer.textContent = message; errorContainer.style.display = 'block'; }

    // Funktion zum Nachdrucken von EC-Belegen
    async function printReceipt(transactionId, receiptType) {
      try {
        showLoading(true);
        errorContainer.style.display = 'none';
        errorContainer.textContent = '';
        
        const result = await window.monitoringApi.printReceipt(transactionId, receiptType);
        
        if (result.success) {
          // Erfolgsbenachrichtigung anzeigen
          const successMessage = document.createElement('div');
          successMessage.className = 'alert alert-success';
          successMessage.textContent = receiptType === 'customer' ? 'Kundenbeleg wurde gedruckt!' : 'Händlerbeleg wurde gedruckt!';
          
          // Alert kurz anzeigen und dann ausblenden
          const parentElement = document.querySelector('.receipt-print-button');
          if (parentElement) {
            parentElement.appendChild(successMessage);
            setTimeout(() => {
              successMessage.remove();
            }, 3000);
          }
        } else {
          showError(result.error || 'Fehler beim Drucken des Belegs');
        }
      } catch (error) {
        console.error('Fehler beim Drucken des Belegs:', error);
        showError(error.message || 'Unbekannter Fehler beim Drucken des Belegs');
      } finally {
        showLoading(false);
      }
    }

    /**
     * Bereitet die Transaktionsliste für den Druck vor und öffnet den Druckdialog
     */
    function printTransactionsList() {
      try {
        // Aktuelles Datum für den Ausdruck setzen
        const now = new Date();
        const formattedDate = now.toLocaleString('de-DE', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });

        document.getElementById('print-date').textContent = formattedDate;
        document.getElementById('print-footer-date').textContent = formattedDate;

        // Filterinformationen für den Ausdruck setzen
        let filterInfo = [];

        if (filterPaymentMethod.value) {
          const paymentMethodText = window.monitoringApi.formatPaymentMethod(filterPaymentMethod.value);
          filterInfo.push(`Zahlungsart: ${paymentMethodText}`);
        }

        if (filterStatus.value) {
          const statusText = window.monitoringApi.formatStatus(filterStatus.value);
          filterInfo.push(`Status: ${statusText}`);
        }

        if (filterCheckoutId.value) {
          filterInfo.push(`Checkout-ID: ${filterCheckoutId.value}`);
        }

        if (filterDateFrom.value) {
          filterInfo.push(`Datum von: ${filterDateFrom.value}`);
        }

        if (filterDateTo.value) {
          filterInfo.push(`Datum bis: ${filterDateTo.value}`);
        }

        document.getElementById('print-filter-info').textContent =
          filterInfo.length > 0 ? filterInfo.join(', ') : 'Keine Filter aktiv';

        // Seitenzahlen für den Druck vorbereiten
        const style = document.createElement('style');
        style.textContent = `
          @media print {
            .page-number:after {
              content: counter(page);
            }
            @page {
              counter-increment: page;
              counter-reset: page 1;
            }
          }
        `;
        document.head.appendChild(style);

        // Druckdialog öffnen
        window.monitoringApi.printCurrentWindow();
      } catch (error) {
        console.error('Fehler beim Vorbereiten des Drucks:', error);
        showError('Fehler beim Vorbereiten des Drucks: ' + error.message);
      }
    }

    document.addEventListener('DOMContentLoaded', () => { loadTransactions(); });
  </script>
</body>
</html>