/**
 * ZVT-Parser für Antworten vom Terminal
 */
const loggerService = require("./logger-service");
const zvtUtils = require("./zvt-utils");

class ZVTParser {
  constructor(client, constants) {
    this.client = client;
    this.constants = constants;
    this.ZVT = constants.ZVT;
    this.statusCodes = constants.statusCodes;
    this.log = loggerService.getModuleLogger("ZVTParser");
  }

  /**
   * Prüft den Zustand der Antwort und extrahiert relevante Daten
   * @param {Buffer} data Empfangene Daten
   * @returns {Object} Zustandsobjekt { complete, expectsMoreData }
   */
  _checkResponseState(data) {
    try {
      // Detailliertes Logging für alle eingehenden Daten
      this.log.info(
        `*** DEBUG: Analysiere Antwort (${data.length} Bytes): ${data
          .toString("hex")
          .toUpperCase()}`
      );

      // Prüfung auf Abbruchcodes 061E und 1E
      if (this._isAbortCommand(data)) {
        this.log.warn("DEBUG: Abbruchcode (061E/1E) vom Terminal erkannt!");
        this.client._sendToWindow("zvt-payment-update", {
          status: "error",
          message: "Vorgang wurde am Terminal abgebrochen",
          statusText: "Abgebrochen"
        });

        this.client._sendToWindow("zvt-payment-result", {
          success: false,
          error: "Vorgang am Terminal abgebrochen",
          statusCode: "0C"
        });

        // Zahlung als abgebrochen markieren
        this.client.payment.paymentInProgress = false;
        this.client.connection.busy = false;

        return { complete: true, expectsMoreData: false };
      }

      // Extrahiere und analysiere Displaynachrichten
      if (this._extractAndAnalyzeDisplayMessages(data)) {
        return { complete: true, expectsMoreData: false };
      }

      // Extrahiere Kartendaten aus Autorisierungsnachricht
      // Sowohl für TLV (06D3) als auch für StatusInformation (040F)
      this._extractCardDataFromAuthorization(data);

      // Spezielle Behandlung für StatusInformation (040F)
      if (this._isStatusInformationMessage(data)) {
        this._handleStatusInformationMessage(data);
        return { complete: true, expectsMoreData: false };
      }

      // Spezielle Behandlung für "Karte bitte"-Nachricht
      if (this._isCardRequestMessage(data)) {
        this._handleCardRequestMessage();
        return { complete: false, expectsMoreData: true };
      }

      // Prüfung auf Fehlernachricht
      if (this._isErrorMessage(data)) {
        return this._handleErrorMessage(data);
      }

      // Prüfung auf Erfolgsnachricht
      if (this._isSuccessMessage(data)) {
        this.log.info("DEBUG: Erfolgreiche Transaktion (060F00) erkannt");
        return { complete: true, expectsMoreData: false };
      }

      // Befehlsbestätigung (80 00 00)
      if (this._isCommandConfirmation(data)) {
        if (this.client.payment.paymentInProgress) {
          this.log.info("DEBUG: Befehlsbestätigung vom Terminal während Zahlung empfangen - warte auf finale Antwort");
          return { complete: false, expectsMoreData: true };
        } else {
          this.log.info("DEBUG: Befehlsbestätigung vom Terminal empfangen (kein Zahlungsvorgang)");
          return { complete: true, expectsMoreData: false };
        }
      }

      // Prüfung auf ETX (Ende der Übertragung)
      if (this._isEndOfTransmission(data)) {
        this.log.info("DEBUG: ETX-Zeichen (Ende der Übertragung) erkannt");
        return { complete: true, expectsMoreData: false };
      }

      // Sicherheits-Timeout - wenn sehr viele Daten empfangen werden ohne Abschluss
      if (data.length > 1200) {
        this.log.warn(`DEBUG: Sehr viele Daten (${data.length} Bytes) ohne vollständige Antwort - erzwinge Abschluss`);
        return { complete: true, expectsMoreData: false };
      }

      // Standardfall: Antwort ist noch nicht vollständig
      this.log.debug(`DEBUG: Antwort noch nicht vollständig (${data.length} Bytes), warte auf weitere Daten`);
      return { complete: false, expectsMoreData: false };
    } catch (error) {
      this.log.error(`Fehler beim Prüfen des Antwortstatus: ${error.message}`);
      // Im Fehlerfall als unvollständig betrachten, um weitere Daten zu sammeln
      return { complete: false, expectsMoreData: false };
    }
  }

  /**
   * Extrahiert und analysiert Display-Nachrichten aus den Daten
   * @param {Buffer} data - Die zu analysierenden Daten
   * @returns {boolean} True wenn Fehlermeldung gefunden, sonst False
   * @private
   */
  _extractAndAnalyzeDisplayMessages(data) {
    let pos = 0;
    while ((pos = data.indexOf(0x04, pos)) !== -1) {
      if (pos + 1 < data.length && data[pos + 1] === 0xff) {
        // Displaynachricht gefunden - versuche, Text zu extrahieren
        try {
          // Schätze die Position, wo der Text beginnt
          let textStart = pos + 10;
          let messageEnd = Math.min(pos + 50, data.length);

          // Text extrahieren
          let displayText = "";
          for (let i = textStart; i < messageEnd; i++) {
            if (data[i] >= 0x20 && data[i] <= 0x7e) {
              displayText += String.fromCharCode(data[i]);
            }
          }

          if (displayText) {
            this.log.info(`DEBUG: Displaytext gefunden: "${displayText}"`);

            // Auf Fehlerwörter prüfen
            const errorKeywords = [
              "abgebrochen", "Abbruch", "Fehler", "Error", "fehlgeschlagen",
              "ungültig", "Karte abgelehnt", "nicht möglich", "Canceled",
              "Invalid", "verweigert",
            ];

            for (const keyword of errorKeywords) {
              if (displayText.toLowerCase().includes(keyword.toLowerCase())) {
                this.log.warn(`DEBUG: Fehlermeldung in Displaytext gefunden: "${displayText}"`);

                // Fehlermeldung an UI senden
                this.client._sendToWindow("zvt-payment-update", {
                  status: "error",
                  message: `Terminal-Meldung: ${displayText}`,
                  statusText: "Fehlgeschlagen",
                });

                // Auch payment-result senden
                this.client._sendToWindow("zvt-payment-result", {
                  success: false,
                  error: displayText,
                  statusCode: "FF", // Generischer Fehlercode
                  statusMessage: displayText,
                });

                return true;
              }
            }
          }
        } catch (textError) {
          this.log.warn(`DEBUG: Fehler beim Parsen des Displaytexts: ${textError.message}`);
        }
      }
      pos++; // Zur nächsten Position
    }
    return false;
  }

  /**
   * Prüft, ob die Daten ein Abbruchkommando enthalten
   * @param {Buffer} data - Die zu prüfenden Daten
   * @returns {boolean} True wenn Abbruchkommando, sonst False
   */
  _isAbortCommand(data) {
    // Standard-Abbruchbefehle mit Konstanten
    const [cmd1, cmd2] = this.ZVT.MESSAGE.ABORT;
    const isStandardAbort = (data.length >= 2 && data[0] === cmd1 && data[1] === cmd2) ||
                            (data.length >= 1 && data[0] === this.ZVT.RESPONSE.ABORT);

    // Erweiterte Abbrucherkennung für 061e016c (der exakte Fehlercode im Log)
    const isExtendedAbort = data.length >= 4 &&
                           data[0] === cmd1 &&
                           data[1] === cmd2 &&
                           (data[2] === 0x01);

    // ASCII-Text nach Abbruch-Schlüsselwörtern durchsuchen
    let containsAbortText = false;
    if (data.length > 4) {
      const dataText = this._bytesToAscii(data);
      const abortKeywords = ["ABBRUCH", "CANCEL", "ABORT", "TIMEOUT", "ERROR"];
      containsAbortText = abortKeywords.some(keyword =>
        dataText.toUpperCase().includes(keyword));
    }

    return isStandardAbort || isExtendedAbort || containsAbortText;
  }

  /**
   * Behandelt ein empfangenes Abbruchkommando
   * @param {Buffer} data - Die Abbruchdaten
   */
  _handleAbortCommand(data) {
    this.log.warn(`Abbruchcode vom Terminal empfangen: ${data.toString("hex").toUpperCase()}`); //

    // NEU: ACK senden, wenn es sich um einen Completion-Befehl vom Terminal handelt (061E)
    // Das ZVT.MESSAGE.ABORT ist 0x1E. Der Befehl vom Terminal ist 0x06 0x1E ...
    // CMD.REGISTER ist 0x06, CMD.ABORT ist 0x1E aus zvt-constants.js
    if (data.length > 1 && data[0] === this.ZVT.CMD.REGISTER && data[1] === this.ZVT.CMD.ABORT) {
      this.log.info("Sende ACK für Terminal-Completion/Abort (061E)");
      try {
        if (this.client.connection.socket && this.client.connection.connected) {
          const ackCommand = Buffer.from([0x80, 0x00, 0x00]);
          this.client.connection.socket.write(ackCommand);
          this.log.info("ACK (80-00-00) für Terminal-Abbruch (061E) gesendet.");
        } else {
          this.log.warn("Kann ACK für Terminal-Abbruch nicht senden: Socket nicht verbunden.");
        }
      } catch (ackError) {
        this.log.error("Fehler beim Senden des ACK für Terminal-Abbruch:", ackError.message);
      }
    }
    // ENDE NEU

    // Laufenden Timeout für die 060F00-Verarbeitung abbrechen
    if (this.client.connection.receiptProcessingTimeout) { //
      clearTimeout(this.client.connection.receiptProcessingTimeout); //
      this.client.connection.receiptProcessingTimeout = null; //
      this.log.warn("060F00-Verarbeitung abgebrochen, da nachfolgender Abbruchcode empfangen wurde"); //
    }

    // Zahlungsstatus aktualisieren
    this.client._sendToWindow("zvt-payment-update", { //
      status: "error", //
      message: "Vorgang wurde am Terminal abgebrochen", //
      statusText: "Abgebrochen", //
      hasCustomerReceipt: false, //
      hasMerchantReceipt: false //
    });

    // Zahlungsergebnis senden
    this.client._sendToWindow("zvt-payment-result", { //
      success: false, //
      error: "Vorgang am Terminal abgebrochen", //
      statusCode: "0C", //
      amount: this.client.lastAmount, //
      transactionId: this.client.lastTransactionId, //
      hasCustomerReceipt: false, //
      hasMerchantReceipt: false //
    });

    // Transaktion in der Datenbank als abgebrochen markieren
    this._markTransactionAsAborted(this.client.lastTransactionId); //

    // Zahlung als abgebrochen markieren
    this.client.payment.paymentInProgress = false; //
    this.client.connection.busy = false; //

    // Terminal-Cooldown aktivieren (wie bei manuellem Abbruch)
    if (this.client.payment) { //
      // Cooldown-Phase aktivieren
      this.client.payment.terminalCooldownActive = true; //

      // Bestehenden Timer löschen, falls vorhanden
      if (this.client.payment.terminalCooldownTimer) { //
        clearTimeout(this.client.payment.terminalCooldownTimer); //
      }

      // Neuen Timer starten
      this.client.payment.terminalCooldownTimer = setTimeout(() => { //
        this.client.payment.terminalCooldownActive = false; //
        this.log.info("Terminal-Cooldown beendet, Terminal ist wieder bereit für neue Zahlungen"); //

        // UI über Ende des Cooldowns informieren
        this.client._sendToWindow("zvt-payment-update", { //
          status: "info", //
          message: "Terminal ist wieder bereit für Zahlungen", //
          statusText: "Terminal bereit" //
        });
      }, 10000); // 10 Sekunden Cooldown

      this.log.info("Zahlung am Terminal abgebrochen, Terminal-Cooldown für 10 Sekunden aktiviert"); //
    }

    // Puffer zurücksetzen
    this.client.connection.resetResponseBuffer(); //

    // Aktuelle Callback verarbeiten, falls vorhanden
    if (this.client.connection.currentCallback) { //
      clearTimeout(this.client.connection.timeout); //
      const callback = this.client.connection.currentCallback; //
      this.client.connection.currentCallback = null; //

      callback(null, { //
        success: false, //
        statusCode: "0C", //
        statusMessage: "Vorgang abgebrochen", //
        rawData: data.toString("hex"), //
      });
    }
  }

  /**
   * Hilfsmethode zum Markieren einer Transaktion als abgebrochen
   * @param {string} transactionId - Die Transaktions-ID
   * @private
   */
  async _markTransactionAsAborted(transactionId) {
    return zvtUtils.markTransactionStatus(transactionId, 'ABORTED');
  }

  /**
   * Prüft, ob die Daten eine Display-Nachricht enthalten
   * @param {Buffer} data - Die zu prüfenden Daten
   * @returns {boolean} True wenn Display-Nachricht, sonst False
   */
  _isDisplayMessage(data) {
    const [cmd1, cmd2] = this.ZVT.MESSAGE.DISPLAY;
    return data.length > 2 && data[0] === cmd1 && data[1] === cmd2;
  }

  /**
   * Prüft, ob die Daten eine StatusInformation-Nachricht enthalten (040F)
   * @param {Buffer} data - Die zu prüfenden Daten
   * @returns {boolean} True wenn StatusInformation-Nachricht, sonst False
   */
  _isStatusInformationMessage(data) {
    const [cmd1, cmd2] = this.ZVT.MESSAGE.AUTHORIZATION;
    return data.length > 2 && data[0] === cmd1 && data[1] === cmd2;
  }

  /**
   * Behandelt eine empfangene StatusInformation-Nachricht (040F)
   * @param {Buffer} data - Die StatusInformation-Daten
   */
  _handleStatusInformationMessage(data) {
    this.log.info("StatusInformation (040F) empfangen, extrahiere Daten und erstelle Beleg");

    // Analysiere Status-Code und andere wichtige Informationen
    const statusInfo = this._parseStatusInformation(data);

    // Sende sofortige Bestätigung, falls erforderlich
    try {
      if (this.client.connection.socket && this.client.connection.connected) {
        const ackCommand = Buffer.from([0x80, 0x00, 0x00]);
        this.client.connection.socket.write(ackCommand);
        this.log.info("Sofortige Bestätigung (80-00-00) für StatusInformation gesendet");
      }
    } catch (ackError) {
      this.log.error("Fehler beim Senden der sofortigen Bestätigung:", ackError.message);
    }

    // Setze einen kurzen Timeout, damit noch weitere Daten empfangen werden können
    setTimeout(() => {
      // Bereinige alle Timeouts
      if (this.client.connection.currentTimeoutId) {
        clearTimeout(this.client.connection.currentTimeoutId);
        this.client.connection.currentTimeoutId = null;
      }

      // Prüfe, ob es ein Erfolg oder Fehler war
      const isSuccess = statusInfo.success ||
                        (this.client.lastAuthCode && this.client.lastAuthCode.trim() !== "");

      if (isSuccess) {
        // Erfolgsmeldung
        this.log.info("StatusInformation wurde als erfolgreich interpretiert");

        // Erstelle Belege mit den extrahierten Daten
        this.client.receipt._createEnhancedReceipts(false, statusInfo);

        // Sende Erfolgsmeldung
        this.client._sendToWindow("zvt-payment-result", {
          success: true,
          amount: this.client.lastAmount,
          transactionId: this.client.lastTransactionId,
          statusCode: "00",
          statusMessage: "Erfolg",
          hasCustomerReceipt: true,
          hasMerchantReceipt: true,
        });

        // Belege verarbeiten
        this.client.receipt.processPaymentReceipts({
          customerReceipt: this.client.receipt.lastReceipts.customer,
          merchantReceipt: this.client.receipt.lastReceipts.merchant,
        }).catch((err) => {
          this.log.error("Fehler bei StatusInformation-Belegverarbeitung:", err.message);
        });

      } else {
        // Fehlermeldung
        this.log.warn("StatusInformation wurde als Fehler interpretiert");

        // Erstelle Fehlerbelege
        this.client.receipt._createEnhancedReceipts(true, statusInfo);

        // Sende Fehlermeldung
        this.client._sendToWindow("zvt-payment-update", {
          status: "error",
          message: statusInfo.errorMessage || "Zahlung fehlgeschlagen",
          statusText: "Fehler",
        });

        this.client._sendToWindow("zvt-payment-result", {
          success: false,
          error: statusInfo.errorMessage || "Unbekannter Fehler",
          statusCode: statusInfo.errorCode || "FF",
          amount: this.client.lastAmount,
          transactionId: this.client.lastTransactionId,
          hasCustomerReceipt: true,
          hasMerchantReceipt: true,
        });

        // Prüfe, ob es sich um einen Abbruch handelt und aktiviere ggf. den Cooldown
        const isAbort = this._isAbortStatusCode(statusInfo.errorCode) ||
                        this._containsAbortText(data);

        if (isAbort && this.client.payment) {
          // Cooldown-Phase aktivieren
          this.client.payment.terminalCooldownActive = true;

          // Bestehenden Timer löschen, falls vorhanden
          if (this.client.payment.terminalCooldownTimer) {
            clearTimeout(this.client.payment.terminalCooldownTimer);
          }

          // Neuen Timer starten
          this.client.payment.terminalCooldownTimer = setTimeout(() => {
            this.client.payment.terminalCooldownActive = false;
            this.log.info("Terminal-Cooldown beendet, Terminal ist wieder bereit für neue Zahlungen");

            // UI über Ende des Cooldowns informieren
            this.client._sendToWindow("zvt-payment-update", {
              status: "info",
              message: "Terminal ist wieder bereit für Zahlungen",
              statusText: "Terminal bereit"
            });
          }, 10000); // 10 Sekunden Cooldown

          this.log.info("Zahlung am Terminal abgebrochen, Terminal-Cooldown für 10 Sekunden aktiviert");
        }
      }

      // Callback auflösen
      if (this.client.connection.currentCallback) {
        const callback = this.client.connection.currentCallback;
        this.client.connection.currentCallback = null;
        callback(null, {
          success: isSuccess,
          statusCode: isSuccess ? "00" : (statusInfo.errorCode || "FF"),
          statusMessage: isSuccess ? "Erfolg" : (statusInfo.errorMessage || "Fehler"),
          rawData: data.toString("hex"),
          customerReceipt: this.client.receipt.lastReceipts.customer,
          merchantReceipt: this.client.receipt.lastReceipts.merchant,
        });
      }

      // Zahlung ist abgeschlossen
      this.client.payment.paymentInProgress = false;
      this.client.connection.busy = false;

      // Puffer zurücksetzen
      this.client.connection.responseData = Buffer.alloc(0);
      this.client.connection.currentReceipt = null;
      this.client.connection.receiptComplete = false;

    }, 500); // 500ms Verzögerung für weitere Daten
  }

  /**
   * Prüft, ob ein Statuscode einem Abbruch entspricht
   * @param {string} statusCode - Der zu prüfende Statuscode
   * @returns {boolean} True wenn Abbruchcode, sonst False
   * @private
   */
  _isAbortStatusCode(statusCode) {
    // Liste von Statuscodes, die einen Abbruch darstellen
    const abortCodes = ['0C', '0D', '30', '85', '9C', '9D', 'D0', 'D2', 'D3'];
    return abortCodes.includes(statusCode);
  }

  /**
   * Prüft, ob die Daten Abbruch-Text enthalten
   * @param {Buffer} data - Die zu prüfenden Daten
   * @returns {boolean} True wenn Abbruchtext gefunden, sonst False
   * @private
   */
  _containsAbortText(data) {
    // Konvertiere Daten zu Text
    const dataText = this._bytesToAscii(data);

    // Liste von Abbruch-Schlüsselwörtern
    const abortKeywords = [
      "ABBRUCH", "CANCEL", "ABORT", "TIMEOUT", "ERROR",
      "ABGEBROCHEN", "VORGANG ABGEBROCHEN", "CANCELLED",
      "USER CANCELLED", "FEHLGESCHLAGEN", "NICHT ERFOLGREICH",
      "STORNO", "KARTE GESPERRT", "KARTE NICHT ZUGELASSEN",
      "FEHLER", "DECLINED", "ABGELEHNT"
    ];

    // Prüfe, ob einer der Abbruch-Schlüsselwörter im Text enthalten ist
    return abortKeywords.some(keyword =>
      dataText.toUpperCase().includes(keyword));
  }

  /**
   * Parst die StatusInformation-Nachricht (040F) und extrahiert Daten
   * @param {Buffer} data - Die StatusInformation-Daten
   * @returns {Object} Extrahierte Status-Informationen
   */
  _parseStatusInformation(data) {
    try {
      const result = {
        success: false,
        errorCode: null,
        errorMessage: null,
        amount: 0,
        cardType: null,
        terminalId: null
      };

      // Analysiere die Bytes der Nachricht basierend auf BMP-Feldern
      if (data.length < 5) return result;

      // Verarbeite die führenden Bytes (typischerweise 040F...)
      // Prüfe auf Erfolgs-/Fehlerstatus
      if (data.length > 5 && data[4] === 0x00) {
        result.success = true;
      }

      // Extrahiere Terminal-ID (üblicherweise zwischen Byte 8-12)
      if (data.length > 12) {
        // Prüfe auf Terminal-ID Tag (0x29 oder ähnliches)
        for (let i = 4; i < data.length - 6; i++) {
          if (data[i] === 0x29 || data[i] === 0x17) {
            // Terminal-ID Tag gefunden
            const idBytes = data.slice(i + 1, i + 5);
            result.terminalId = idBytes.toString('hex').toUpperCase();
            break;
          }
        }
      }

      // Versuche, Betrag zu extrahieren (üblicherweise Tag 0x04 mit 6 Bytes Länge)
      for (let i = 4; i < data.length - 8; i++) {
        if (data[i] === 0x04) {
          // Potentieller Betrags-Tag gefunden
          const amountBytes = data.slice(i + 1, i + 7);
          // Konvertiere BCD zu Dezimal
          try {
            const amountHex = amountBytes.toString('hex');
            // Umwandlung von Hex zu Cent und dann zu Euro
            const amountCents = parseInt(amountHex, 10);
            if (!isNaN(amountCents)) {
              result.amount = amountCents / 100; // Umrechnung in Euro
            }
          } catch (e) {
            this.log.warn(`Fehler bei der Betragextraktion: ${e.message}`);
          }
          break;
        }
      }

      // Versuche, Kartentyp zu extrahieren (üblicherweise Tag 0x8A oder 0x19)
      // In StatusInformation oft als Einzelbyte-Tag
      for (let i = 4; i < data.length - 3; i++) {
        if (data[i] === 0x8a || data[i] === 0x19) {
          const cardTypeValue = data[i + 1];
          // Mapping für gängige Kartentypen
          const cardTypeMap = {
            0x05: "girocard",
            0x06: "Mastercard",
            0x0a: "Visa",
            0x0d: "V PAY",
            0x2e: "Maestro",
          };

          result.cardType = cardTypeMap[cardTypeValue] || `Karte (Typ ${cardTypeValue.toString(16)})`;
          break;
        }
      }

      // Extrahiere Fehlercode und -meldung, falls vorhanden (meist Tag 0x27)
      for (let i = 4; i < data.length - 3; i++) {
        if (data[i] === 0x27) {
          const errorCode = data[i + 1];
          result.errorCode = errorCode.toString(16).padStart(2, '0').toUpperCase();
          // Versuche, die Fehlermeldung aus den Statuscodes zu bekommen
          result.errorMessage = this.statusCodes[result.errorCode] || "Unbekannter Fehler";
          break;
        }
      }

      // Wenn kein expliziter Fehler gefunden wurde, aber kein Erfolg, setze Standardfehler
      if (!result.success && !result.errorCode) {
        result.errorCode = "FF";
        result.errorMessage = "Unspezifischer Fehler bei der Verarbeitung";
      }

      return result;
    } catch (error) {
      this.log.error(`Fehler beim Parsen der StatusInformation: ${error.message}`);
      return {
        success: false,
        errorCode: "FF",
        errorMessage: "Fehler bei der Verarbeitung der Terminalnachricht"
      };
    }
  }

  /**
   * Prüft, ob die Daten eine Autorisierungsnachricht enthalten
   * @param {Buffer} data - Die zu prüfenden Daten
   * @returns {boolean} True wenn Autorisierungsnachricht, sonst False
   */
  _isAuthorizationMessage(data) {
    const [cmd1, cmd2] = this.ZVT.MESSAGE.AUTHORIZATION;
    return data.length > 2 && data[0] === cmd1 && data[1] === cmd2;
  }

  /**
   * Behandelt eine empfangene Autorisierungsnachricht
   * @param {Buffer} data - Die Autorisierungsdaten
   */
  _handleAuthorizationMessage(data) {
    this.log.info("Autorisierungsnachricht empfangen, sende sofortige Bestätigung");
    try {
      if (this.client.connection.socket && this.client.connection.connected) {
        const ackCommand = Buffer.from([0x80, 0x00, 0x00]);
        this.client.connection.socket.write(ackCommand);
        this.log.info("Sofortige Bestätigung (80-00-00) für Autorisierung gesendet");
      }
    } catch (ackError) {
      this.log.error("Fehler beim Senden der sofortigen Bestätigung:", ackError.message);
    }
    // Extrahiere Kartendaten
    this._extractCardDataFromAuthorization(data);
  }

  /**
   * Prüft, ob die Daten eine Beleg-Nachricht enthalten
   * @param {Buffer} data - Die zu prüfenden Daten
   * @returns {boolean} True wenn Beleg-Nachricht, sonst False
   */
  _isReceiptMessage(data) {
    // Altes Format (06 0F 00)
    const [cmd1, cmd2, cmd3] = this.ZVT.MESSAGE.RECEIPT;
    const isOldFormat = data.length === 3 && data[0] === cmd1 && data[1] === cmd2 && data[2] === cmd3;

    // Neues Format (06 D3 FF...)
    const isNewFormat = data.length > 3 && data[0] === cmd1 && data[1] === 0xd3 && data[2] === 0xff;

    return isOldFormat || isNewFormat;
  }

  /**
   * Prüft, ob es sich um TLV-Daten handelt (Verifone-Format)
   * @param {Buffer} data Die zu prüfenden Daten
   * @returns {boolean} True wenn es TLV-Daten sind
   */
  _isTLVData(data) {
    if (data.length >= 3 && data[0] === 0x06 && data[1] === 0xD3) {
      this.log.info("06D3 TLV-Daten empfangen - extrahiere Terminal-Daten");
      return true;
    }
    return false;
  }

  /**
   * Extrahiert Terminal-Daten aus TLV-Daten (unterstützt verschiedene Terminal-Formate)
   * @param {Buffer} data Die zu analysierenden Daten
   */
  _extractTerminalDataFromTLV(data) {
    try {
      if (!this._isTLVData(data)) {
        return;
      }

      // TLV-Daten mit verbesserter Zeichenkodierung konvertieren
      let tlvData;

      try {
        // Versuche zuerst Latin1 (ISO-8859-1), da dies häufig für deutsche Umlaute verwendet wird
        tlvData = data.slice(2).toString("latin1");

        // Prüfe, ob die Umlaute korrekt sind
        if (tlvData.includes("�") ||
            tlvData.includes("├") ||
            tlvData.includes("╝") ||
            tlvData.includes("ñ")) {
          // Wenn nicht, versuche UTF-8
          tlvData = data.slice(2).toString("utf8");
        }
      } catch (e) {
        // Fallback auf ASCII, wenn andere Kodierungen fehlschlagen
        tlvData = this._bytesToAscii(data.slice(2));
      }

      // Umlaute korrigieren
      tlvData = this._fixReceiptText(tlvData);

      // BMP-Tags direkt im Binärformat suchen
      this._extractBmpTags(data);

      // Terminal-ID extrahieren - verschiedene Formate unterstützen
      // Format 1: Terminal-ID : 55596462 (Verifone)
      let terminalIdMatch = tlvData.match(/Terminal-ID\s*[:=]\s*\D*([0-9]{8})/i);

      // Format 2: T-ID 52613587 (CCV)
      if (!terminalIdMatch) {
        terminalIdMatch = tlvData.match(/T-ID\s+([0-9]{8})/i);
      }

      // Format 3: Terminal-ID : XXXXX (allgemein)
      if (!terminalIdMatch) {
        terminalIdMatch = tlvData.match(/Terminal[-\s]*ID\s*[:=]?\s*([A-Za-z0-9]{5,10})/i);
      }

      if (terminalIdMatch && terminalIdMatch[1]) {
        const terminalId = terminalIdMatch[1].trim();
        this.log.info(`Terminal-ID aus TLV-Container extrahiert: ${terminalId}`);
        this.client.lastTerminalId = terminalId;
      }

      // TA-Nr extrahieren - verschiedene Formate unterstützen
      // Format 1: TA-Nr 076551 (Verifone)
      let traceNumberMatch = tlvData.match(/TA-Nr\s*[:=]?\s*(\d+)/i);

      // Format 2: TA-Nr. 000206 (CCV)
      if (!traceNumberMatch) {
        traceNumberMatch = tlvData.match(/TA-Nr\.\s+(\d+)/i);
      }

      if (traceNumberMatch && traceNumberMatch[1]) {
        const traceNumber = traceNumberMatch[1].trim();
        this.log.info(`TA-Nr aus TLV-Container extrahiert: ${traceNumber}`);
        this.client.lastTraceNumber = traceNumber;
      }

      // Beleg-Nr extrahieren - verschiedene Formate unterstützen
      // Format 1: BNr 0003 (Verifone)
      let receiptNumberMatch = tlvData.match(/BNr\s*[:=]?\s*(\d+)/i);

      // Format 2: Beleg-Nr. 0126 (CCV)
      if (!receiptNumberMatch) {
        receiptNumberMatch = tlvData.match(/Beleg-Nr\.\s+(\d+)/i);
      }

      if (receiptNumberMatch && receiptNumberMatch[1]) {
        const receiptNumber = receiptNumberMatch[1].trim();
        this.log.info(`Beleg-Nr aus TLV-Container extrahiert: ${receiptNumber}`);
        this.client.lastReceiptNumber = receiptNumber;
      }

      // VU-Nr extrahieren - verschiedene Formate unterstützen
      // Format 1: VU-Nr A5E91002 (Verifone)
      let vuNumberMatch = tlvData.match(/VU-Nr\s*[:=]?\s*\D*([a-zA-Z0-9]{8})/i);

      // Format 2: Allgemeines Format
      if (!vuNumberMatch) {
        vuNumberMatch = tlvData.match(/VU[-\s]*Nr\.?\s*[:=]?\s*([a-zA-Z0-9]{5,10})/i);
      }

      if (vuNumberMatch && vuNumberMatch[1]) {
        const vuNumber = vuNumberMatch[1].trim();
        this.log.info(`VU-Nr aus TLV-Container extrahiert: ${vuNumber}`);
        this.client.lastVUNumber = vuNumber;
      }

      // Kartentyp extrahieren - verschiedene Formate unterstützen
      const cardTypePatterns = [
        /Kartentyp\s*[:=]?\s*([a-zA-Z]+)/i,
        /Kartenzahlung\s+([a-zA-Z]+)/i,
        /Karte\s*[:=]?\s*([a-zA-Z]+)/i,
        /\s+([a-zA-Z]+card)\s+/i,
        /Debit\s+([a-zA-Z]+)/i,  // CCV-Format: Debit Mastercard
        /Credit\s+([a-zA-Z]+)/i  // CCV-Format: Credit Mastercard
      ];

      for (const pattern of cardTypePatterns) {
        const cardTypeMatch = tlvData.match(pattern);
        if (cardTypeMatch && cardTypeMatch[1]) {
          const cardType = cardTypeMatch[1].trim();
          // Nur überschreiben, wenn es sich um einen bekannten Kartentyp handelt
          if (/girocard|maestro|mastercard|visa|vpay|amex/i.test(cardType)) {
            this.log.info(`Kartentyp aus TLV-Container extrahiert: ${cardType}`);
            this.client.lastCardType = cardType;
            break;
          }
        }
      }

      // Spezialfall: Vollständiger Kartentyp "Debit Mastercard" oder "Credit Mastercard"
      const fullCardTypeMatch = tlvData.match(/(Debit|Credit)\s+(Mastercard|VISA|girocard)/i);
      if (fullCardTypeMatch) {
        const cardType = fullCardTypeMatch[2].trim();
        this.log.info(`Vollständiger Kartentyp aus TLV-Container extrahiert: ${cardType}`);
        this.client.lastCardType = cardType;
      }

      // Kartennummer extrahieren (letzte 4 Stellen)
      const panPatterns = [
        /PAN\s+[*#]+(\d{4})/i,
        /Kartennummer\s*[:=]?\s*[*#x]+\s*(\d{4})/i,
        /KNr\s+[*#]+(\d{4})/i,
        /KNr\s+[*#]+(\d{4})\s+\d{2}/i,  // CCV-Format: KNr ####7738 00
        /PAN\s+[*#]+(\d{4})\s+\d{2}/i   // CCV-Format: PAN ####1016 00
      ];

      for (const pattern of panPatterns) {
        const panMatch = tlvData.match(pattern);
        if (panMatch && panMatch[1]) {
          const lastDigits = panMatch[1].trim();
          this.log.info(`Kartennummer (letzte 4 Stellen) aus TLV-Container extrahiert: ${lastDigits}`);
          this.client.lastCardNumber = lastDigits;
          break;
        }
      }

      // Autorisierungscode extrahieren
      const authCodePatterns = [
        /Autorisierungscode\s*[:=]?\s*(\d+)/i,
        /Genehmigungsnummer\s*[:=]?\s*(\d+)/i,
        /Autorisierungsnummer\s*[:=]?\s*(\d+)/i
      ];

      for (const pattern of authCodePatterns) {
        const authMatch = tlvData.match(pattern);
        if (authMatch && authMatch[1]) {
          const authCode = authMatch[1].trim();
          this.log.info(`Autorisierungscode aus TLV-Container extrahiert: ${authCode}`);
          this.client.lastAuthCode = authCode;
          break;
        }
      }
    } catch (error) {
      this.log.error(`Fehler beim Extrahieren der Terminal-Daten aus TLV: ${error.message}`);
    }
  }

  /**
   * Extrahiert BMP-Tags direkt aus den Binärdaten
   * @param {Buffer} data Die zu analysierenden Daten
   */
  _extractBmpTags(data) {
    try {
      // BMP-Tags und ihre Bedeutung
      const BMP_TAGS = {
        0x29: { name: "Terminal-ID", length: 4, parser: this._parseBcdToInt.bind(this) },
        0x0B: { name: "Trace-Nummer", length: 3, parser: this._parseBcdToInt.bind(this) },
        0x87: { name: "Belegnummer", length: 2, parser: this._parseBcdToInt.bind(this) },
        0x2A: { name: "VU-Nummer", length: 15, parser: this._parseAscii.bind(this) },
        0x8A: { name: "Kartentyp", length: 1, parser: this._parseCardType.bind(this) },
        0x22: { name: "PAN", length: -1, parser: this._parsePan.bind(this) }, // Variable Länge
        0x3B: { name: "Autorisierungsattribut", length: 8, parser: this._parseAscii.bind(this) }
      };

      // Durchsuche die Daten nach BMP-Tags
      for (let i = 0; i < data.length - 2; i++) {
        const tagId = data[i];

        // Prüfe, ob der aktuelle Byte ein bekannter BMP-Tag ist
        if (BMP_TAGS[tagId]) {
          const tagInfo = BMP_TAGS[tagId];
          let length = tagInfo.length;
          let dataStartPos = i + 1;

          // Bei variabler Länge (PAN)
          if (length === -1) {
            // LL-Format: Die nächsten 2 Bytes enthalten die Länge
            if (i + 2 < data.length) {
              const firstByteHex = data[i + 1].toString(16).padStart(2, '0');
              const secondByteHex = data[i + 2].toString(16).padStart(2, '0');

              // Extrahiere die Länge aus den BCD-kodierten Bytes
              const n1 = parseInt(firstByteHex[1], 10);
              const n2 = parseInt(secondByteHex[1], 10);

              if (!isNaN(n1) && !isNaN(n2)) {
                length = n1 * 10 + n2;
                dataStartPos = i + 3; // Überspringe die Längenangabe
              }
            }
          }

          // Extrahiere die Daten, wenn genügend Bytes vorhanden sind
          if (dataStartPos + length <= data.length) {
            const tagData = data.slice(dataStartPos, dataStartPos + length);

            // Parse die Daten mit der entsprechenden Methode
            const parsedValue = tagInfo.parser(tagData, tagInfo);

            if (parsedValue !== null) {
              this.log.info(`BMP-Tag ${tagId.toString(16).toUpperCase()} (${tagInfo.name}) extrahiert: ${parsedValue}`);

              // Speichere die extrahierten Daten
              switch (tagId) {
                case 0x29: // Terminal-ID
                  this.client.lastTerminalId = parsedValue.toString().padStart(8, '0');
                  break;
                case 0x0B: // Trace-Nummer
                  this.client.lastTraceNumber = parsedValue.toString().padStart(6, '0');
                  break;
                case 0x87: // Belegnummer
                  this.client.lastReceiptNumber = parsedValue.toString().padStart(4, '0');
                  break;
                case 0x2A: // VU-Nummer
                  this.client.lastVUNumber = parsedValue;
                  break;
                case 0x8A: // Kartentyp
                  this.client.lastCardType = parsedValue;
                  break;
                case 0x22: // PAN
                  // Extrahiere die letzten 4 Stellen
                  if (parsedValue.length >= 4) {
                    this.client.lastCardNumber = parsedValue.slice(-4);
                  }
                  break;
                case 0x3B: // Autorisierungsattribut
                  this.client.lastAuthCode = parsedValue;
                  break;
              }
            }
          }
        }
      }
    } catch (error) {
      this.log.error(`Fehler beim Extrahieren der BMP-Tags: ${error.message}`);
    }
  }

  /**
   * Konvertiert BCD-kodierte Bytes in einen Integer
   * @param {Buffer} data Die zu konvertierenden Bytes
   * @returns {number} Der konvertierte Integer
   */
  _parseBcdToInt(data) {
    try {
      let result = 0;
      for (let i = 0; i < data.length; i++) {
        const byte = data[i];
        const highNibble = (byte >> 4) & 0x0F;
        const lowNibble = byte & 0x0F;

        result = result * 10 + highNibble;
        result = result * 10 + lowNibble;
      }
      return result;
    } catch (error) {
      this.log.error(`Fehler beim Konvertieren von BCD zu Int: ${error.message}`);
      return null;
    }
  }

  /**
   * Konvertiert ASCII-kodierte Bytes in einen String
   * @param {Buffer} data Die zu konvertierenden Bytes
   * @returns {string} Der konvertierte String
   */
  _parseAscii(data) {
    try {
      return data.toString('ascii').replace(/\0/g, '').trim();
    } catch (error) {
      this.log.error(`Fehler beim Konvertieren von ASCII: ${error.message}`);
      return null;
    }
  }

  /**
   * Konvertiert PAN-Daten in einen String
   * @param {Buffer} data Die zu konvertierenden Bytes
   * @returns {string} Der konvertierte String
   */
  _parsePan(data) {
    try {
      // Konvertiere zu Hex-String
      let hexString = '';
      for (let i = 0; i < data.length; i++) {
        hexString += data[i].toString(16).padStart(2, '0');
      }

      // Ersetze 'F' durch '' und 'E' durch '*'
      return hexString.replace(/F/gi, '').replace(/E/gi, '*');
    } catch (error) {
      this.log.error(`Fehler beim Konvertieren der PAN: ${error.message}`);
      return null;
    }
  }

  /**
   * Konvertiert Kartentyp-ID in einen String
   * @param {Buffer} data Die zu konvertierenden Bytes
   * @returns {string} Der konvertierte String
   */
  _parseCardType(data) {
    try {
      const cardTypeId = data[0];

      // Kartentyp-Mapping basierend auf der BmpParser.cs-Datei
      const CARD_TYPES = {
        0x05: "girocard",
        0x06: "Mastercard",
        0x08: "American Express",
        0x0A: "Visa",
        0x0D: "V PAY",
        0x2E: "Maestro",
        0xE8: "Discover Card"
      };

      return CARD_TYPES[cardTypeId] || `Unbekannter Kartentyp (${cardTypeId.toString(16).toUpperCase()})`;
    } catch (error) {
      this.log.error(`Fehler beim Konvertieren des Kartentyps: ${error.message}`);
      return null;
    }
  }

 /**
 * Behandelt eine empfangene Beleg-Nachricht
 * @param {Buffer} data - Die Beleg-Nachrichtdaten
 */
_handleReceiptMessage(data) {
  this.log.info("060F00 Belegdaten empfangen - interpretiere als separaten Beleg");

  // Prüfen, ob es sich um TLV-Daten handelt und Terminal-Daten extrahieren
  if (this._isTLVData(data)) {
    this._extractTerminalDataFromTLV(data);
  }

  // WICHTIG: Haupttimer immer löschen, auch wenn kein Callback mehr vorhanden ist
  if (this.client.connection.timeout) {
    clearTimeout(this.client.connection.timeout);
    this.client.connection.timeout = null;
    this.log.info("Haupttimer der ZVT-Verbindung nach Belegempfang gelöscht");
  }

  // Analysiere die ZVT-Protokoll-Codes mit drei Zuständen: true=Erfolg, false=Fehler, null=Zwischenstatus
 const zvtStatus = this._analyzeZvtProtocolSuccess(data);
 
 this.log.info(`ZVT-Protokoll-Analyse: Status=${zvtStatus === true ? 'ERFOLG' : zvtStatus === false ? 'FEHLER' : 'ZWISCHENSTATUS'}`);

 // ZWISCHENSTATUS: Wenn null zurückgegeben wird, handelt es sich um einen Zwischenstatus
 // In diesem Fall wird keine Aktion ausgeführt, sondern auf weitere Daten gewartet
 if (zvtStatus === null) {
   this.log.info("ZVT-Protokoll-Analyse: ZWISCHENSTATUS - Warte auf finale Completion-Nachricht");
   // Bestätigung senden, aber keine weiteren Aktionen ausführen (weder Erfolg noch Fehler)
   this._sendReceiptAcknowledgement();
   return;
 }

 // FEHLER: Bei erkanntem Fehler über ZVT-Protokoll sofort die Fehlermeldung senden
 if (zvtStatus === false) {
   this.log.warn("ZVT-Protokoll-Analyse: FEHLER - Zahlung fehlgeschlagen");

   // Timeout löschen
   if (this.client.connection.currentTimeoutId) {
     clearTimeout(this.client.connection.currentTimeoutId);
     this.client.connection.currentTimeoutId = null;
   }

   // Fehler-Status an UI senden
   this.client._sendToWindow("zvt-payment-update", {
     status: "error",
     message: "Zahlung fehlgeschlagen: Karte wurde abgelehnt",
     statusText: "Abgelehnt",
     hasCustomerReceipt: true,
     hasMerchantReceipt: true
   });

   // Fehler-Ergebnis an UI senden
   this.client._sendToWindow("zvt-payment-result", {
     success: false,
     error: "Karte wurde abgelehnt oder ist gesperrt",
     statusCode: "05", // Karte abgelehnt
     amount: this.client.lastAmount,
     transactionId: this.client.lastTransactionId,
     hasCustomerReceipt: true,
     hasMerchantReceipt: true
   });

   this.client.payment.paymentInProgress = false;
   this.client.connection.busy = false;

   // Belege als Fehlerbelege erstellen
   this.client.receipt._createEnhancedReceipts(true); // true = Fehlerbeleg

   // Sofortige Bestätigung senden
   this._sendReceiptAcknowledgement();

   // Callback mit Fehlerstatus auflösen
   if (this.client.connection.currentCallback) {
     const callback = this.client.connection.currentCallback;
     this.client.connection.currentCallback = null;
     callback(null, {
       success: false,
       statusCode: "05",
       statusMessage: "Karte abgelehnt",
       rawData: data.toString("hex"),
       customerReceipt: this.client.receipt.lastReceipts.customer,
       merchantReceipt: this.client.receipt.lastReceipts.merchant
     });
   }

   // Puffer zurücksetzen
   this.client.connection.responseData = Buffer.alloc(0);
   this.client.connection.currentReceipt = null;
   this.client.connection.receiptComplete = false;

   return;
 } 

  // Kurze Wartezeit für eventuelle nachfolgende Abbruchcodes
  this.client.connection.receiptProcessingTimeout = setTimeout(() => {
    // Nach der Verzögerung normal fortfahren
    if (this.client.connection.currentTimeoutId) {
      clearTimeout(this.client.connection.currentTimeoutId);
      this.client.connection.currentTimeoutId = null;
    }

    // ERFOLGREICH - ZVT-Protokoll zeigt Erfolg an
    this.log.info("Zahlung erfolgreich: ZVT-Protokoll zeigt Erfolg an");
    
    // Belege erstellen
    this.client.receipt._createEnhancedReceipts();

    // Erfolgs-Event senden
    this.client._sendToWindow("zvt-payment-result", {
      success: true,
      amount: this.client.lastAmount,
      transactionId: this.client.lastTransactionId,
      statusCode: "00",
      statusMessage: "Erfolg",
      hasCustomerReceipt: this.client.receipt.lastReceipts.customer && this.client.receipt.lastReceipts.customer.length > 0,
      hasMerchantReceipt: this.client.receipt.lastReceipts.merchant && this.client.receipt.lastReceipts.merchant.length > 0,
    });

    // Belege verarbeiten (Druck)
    this.client.receipt.processPaymentReceipts({
      customerReceipt: this.client.receipt.lastReceipts.customer,
      merchantReceipt: this.client.receipt.lastReceipts.merchant,
    }).catch((err) => {
      this.log.error("Fehler bei separater Belegverarbeitung:", err.message);
    });

    // Callback auflösen
    if (this.client.connection.currentCallback) {
      this.log.info("Löse Callback auf, da 060F00 empfangen wurde.");
      const callback = this.client.connection.currentCallback;
      this.client.connection.currentCallback = null;
      callback(null, {
        success: true,
        statusCode: "00",
        statusMessage: "Erfolg",
        rawData: data.toString("hex"),
        customerReceipt: this.client.receipt.lastReceipts.customer,
        merchantReceipt: this.client.receipt.lastReceipts.merchant,
        cardType: this.client.lastCardType,
        cardNumber: this.client.lastCardNumber,
        receiptNumber: this.client.lastReceiptNumber,
        authCode: this.client.lastAuthCode,
        terminalId: this.client.lastTerminalId,
        traceNumber: this.client.lastTraceNumber,
        emvData: this.client.lastEmvData
      });
    } else {
      this.log.warn("Kein aktiver Callback vorhanden bei Empfang von 060F00.");
    }

    // Bestätigung in jedem Fall senden
    this._sendReceiptAcknowledgement();

    // Zahlung ist abgeschlossen
    this.client.payment.paymentInProgress = false;
    this.client.connection.busy = false;

    // Puffer zurücksetzen
    this.client.connection.responseData = Buffer.alloc(0);
    this.client.connection.currentReceipt = null;
    this.client.connection.receiptComplete = false;
  }, 500); // 500ms warten für eventuelle Abbruchcodes
}

/**
 * Analysiert die ZVT-Protokoll-Codes auf Erfolg/Fehler basierend auf dem gesamten Response-Buffer
 * @param {Buffer} currentData - Die aktuell empfangenen Daten
 * @returns {null|boolean} True für finalen Erfolg, False für finalen Fehler, null für Zwischenstatus (warten auf weitere Daten)
 * @private
 */
_analyzeZvtProtocolSuccess(currentData) {
  const fullResponseData = this.client.connection.responseData;
  
  this.log.info(`Analysiere ZVT-Protokoll für Erfolg. Vollständige Daten: ${fullResponseData.toString('hex').toUpperCase()}`);
  this.log.info(`Aktuelle Daten: ${currentData.toString('hex').toUpperCase()}`);

  // 1. KRITISCH: Prüfe auf 060F00 (Completion mit Erfolg) - NUR diese gilt als finaler Erfolg
  if (currentData.length >= 3 && 
      currentData[0] === 0x06 && 
      currentData[1] === 0x0F && 
      currentData[2] === 0x00) {
    this.log.info("060F00 Completion-Code mit Erfolg (00) erkannt - FINALER ERFOLG");
    return true; // Finaler ERFOLG
  }

  // 2. Prüfe auf explizite Fehlercodes in aktuellen oder vorherigen Daten
  const hasErrorCode = this._findZvtErrorCodes(fullResponseData) || this._findZvtErrorCodes(currentData);
  if (hasErrorCode) {
    this.log.warn("ZVT-Fehlercode gefunden - FINALER FEHLER");
    return false; // Finaler FEHLER
  }

  // 3. Standard: Wenn 060F ohne expliziten Fehlercode empfangen wird, als Erfolg werten
  if (currentData.length >= 2 && currentData[0] === 0x06 && currentData[1] === 0x0F) {
    this.log.info("060F Completion empfangen, wird als Erfolg gewertet");
    return true; // Finaler ERFOLG
  }

  // 4. Prüfe auf StatusInformation (040F) - ZWISCHENSTATUS
  if (this._containsStatusInformation(fullResponseData) || this._containsStatusInformation(currentData)) {
    this.log.info("040F StatusInformation erkannt - ZWISCHENSTATUS: Warte auf finale Completion-Nachricht");
    // Rufe die Funktion auf, um im Log die Resultat-Codes anzuzeigen (aber nehme kein Ergebnis an)
    this._findStatusInformationSuccess(fullResponseData);
    return null; // ZWISCHENSTATUS
  }

  // 5. TLV-Daten (06D3) - ZWISCHENSTATUS
  if (this._isTLVData(currentData)) {
    this.log.info("TLV-Daten (06D3) empfangen - ZWISCHENSTATUS: Warte auf finale Completion-Nachricht");
    return null; // ZWISCHENSTATUS
  }
  
  // 6. Letzter Fallback: Das Terminal sendet normalerweise immer eine finale Nachricht
  this.log.info("Weder Completion-Nachricht (060F) noch andere bekannte Nachricht erkannt - ZWISCHENSTATUS");
  return null; // ZWISCHENSTATUS
}

/**
 * Sucht nach StatusInformation (040F) mit Erfolgs-Resultat-Code
 * @param {Buffer} data - Die zu durchsuchenden Daten
 * @returns {boolean} True wenn gefunden, sonst False
 * @private
 */
_findStatusInformationSuccess(data) {
  // WICHTIG: StatusInformation (0x040F) sollen nie als finale erfolgreiche Zahlungen gewertet werden,
  // egal welcher Resultatcode vorhanden ist. Zahlung ist erst erfolgreich bei Completion (0x060F).
  
  // Protokollieren der erkannten Codes für Debug-Zwecke
  for (let i = 0; i < data.length - 4; i++) {
    if (data[i] === 0x04 && data[i + 1] === 0x0F) {
      // StatusInformation gefunden, prüfe den Resultat-Code nur für Logging
      for (let j = i + 2; j < Math.min(i + 8, data.length - 1); j++) {
        if (data[j] === 0x27 && j + 1 < data.length) {
          // BMP 27 (Resultat-Code) gefunden
          const resultCode = data[j + 1];
          this.log.info(`StatusInformation Resultat-Code gefunden: 0x${resultCode.toString(16).padStart(2, '0')} (wird als Zwischenstatus behandelt)`);
          break;
        }
      }
    }
  }
  
  // StatusInformation soll nie als finaler Erfolg gewertet werden
  return false;
}

/**
 * Sucht nach expliziten ZVT-Fehlercodes
 * @param {Buffer} data - Die zu durchsuchenden Daten
 * @returns {boolean} True wenn finaler Fehlercode gefunden, sonst False
 * @private
 */
_findZvtErrorCodes(data) {
  // WICHTIGE KORREKTUR: Nur echte finale ZVT-Codes behandeln
  // 060F XX → Completion (XX=00 Erfolg, XX≠00 Fehler)
  // 061E → Abort (finale Fehlermeldung)
  // 84 XX → BMP-Tags/Strukturdaten, NICHT finale Fehlercodes!

  // Prüfe auf Completion mit Fehlercode (060F XX wo XX != 00) - finale Fehlermeldung
  for (let i = 0; i < data.length - 2; i++) {
    if (data[i] === 0x06 && data[i + 1] === 0x0F && i + 2 < data.length) {
      const completionCode = data[i + 2];
      if (completionCode !== 0x00) {
        this.log.warn(`Completion Fehlercode gefunden: 060F ${completionCode.toString(16).padStart(2, '0')} - FINALE FEHLERMELDUNG`);
        return true;
      }
    }
  }

  // Prüfe auf Abbruch-Codes (061E) - finale Fehlermeldung
  for (let i = 0; i < data.length - 1; i++) {
    if (data[i] === 0x06 && data[i + 1] === 0x1E) {
      this.log.warn("Abbruchcode gefunden: 061E - FINALE FEHLERMELDUNG");
      return true;
    }
  }

  // ENTFERNT: Die fehlerhafte Behandlung von 84 XX als finale Fehlermeldung
  // 84 XX sind BMP-Tags/Strukturdaten, keine finalen Fehlercodes!
  // Nur zur Information loggen, aber nicht als Fehler behandeln
  for (let i = 0; i < data.length - 2; i++) {
    if (data[i] === 0x84) {
      const bmpTag = data[i + 1];
      this.log.info(`DEBUG: 84 ${bmpTag.toString(16).padStart(2, '0')} erkannt - BMP-Tag/Strukturdaten, KEIN finaler Fehlercode`);
    }
  }

  // StatusInformation (040F) mit Fehlercode - KEIN finaler Fehler, nur Logging
  for (let i = 0; i < data.length - 4; i++) {
    if (data[i] === 0x04 && data[i + 1] === 0x0F) {
      // Suche nach BMP 27 (Resultat-Code)
      for (let j = i + 2; j < Math.min(i + 20, data.length - 1); j++) {
        if (data[j] === 0x27 && j + 1 < data.length) {
          const resultCode = data[j + 1];
          this.log.info(`StatusInformation Resultat-Code gefunden: 27 ${resultCode.toString(16).padStart(2, '0')} - ZWISCHENSTATUS, kein finaler Fehler`);
          // WICHTIG: Gebe false zurück, da StatusInformation keine finale Fehlermeldung ist
          // und auf eine Completion-Nachricht gewartet werden soll
        }
      }
    }
  }

  return false; // Keine finalen Fehlercodes gefunden
}

  /**
   * Prüft, ob die Daten eine StatusInformation (040F) enthalten
   * @param {Buffer} data - Die zu prüfenden Daten
   * @returns {boolean} True wenn StatusInformation gefunden, sonst False
   * @private
   */
  _containsStatusInformation(data) {
    for (let i = 0; i < data.length - 1; i++) {
      if (data[i] === 0x04 && data[i + 1] === 0x0F) {
        return true;
      }
    }
    return false;
  }

  /**
   * Sendet Erfolgsbenachrichtigungen an alle Fenster
   * @private
   */
  _broadcastPaymentSuccess() {
    try {
      const { BrowserWindow } = require("electron");
      const windows = BrowserWindow.getAllWindows();
      for (const win of windows) {
        if (!win.isDestroyed()) {
          win.webContents.send("zvt-payment-update", {
            status: "success",
            message: "Zahlung erfolgreich abgeschlossen!",
            statusText: "Erfolgreich",
            statusCode: "00",
            hasCustomerReceipt: true,
            hasMerchantReceipt: true,
          });

          win.webContents.send("zvt-payment-result", {
            success: true,
            amount: this.client.lastAmount,
            transactionId: this.client.lastTransactionId,
            statusCode: "00",
            statusMessage: "Erfolg",
            hasCustomerReceipt: true,
            hasMerchantReceipt: true,
          });
        }
      }
    } catch (err) {
      this.log.error("Fehler beim direkten Senden an alle Fenster:", err.message);
    }
  }

  /**
   * Sendet eine Bestätigung für Belegdaten
   * @private
   */
  _sendReceiptAcknowledgement() {
    try {
      if (this.client.connection.socket && this.client.connection.connected) {
        const ackCommand = Buffer.from([0x80, 0x00, 0x00]);
        this.client.connection.socket.write(ackCommand);
        this.log.info("Abschließende Bestätigung (80-00-00) für 060F00 gesendet");
      }
    } catch (ackError) {
      this.log.error("Fehler beim Senden der abschließenden Bestätigung:", ackError.message);
    }
  }

  /**
   * Prüft, ob die Daten eine Karte-Bitte-Nachricht enthalten
   * @param {Buffer} data - Die zu prüfenden Daten
   * @returns {boolean} True wenn Karte-Bitte-Nachricht, sonst False
   */
  _isCardRequestMessage(data) {
    return data.length > 2 && data[0] === 0x04 && data[1] === 0xff;
  }

  /**
   * Behandelt eine Karte-Bitte-Nachricht
   * @private
   */
  _handleCardRequestMessage() {
    this.log.info('DEBUG: "Karte bitte"-Nachricht erkannt, sende Bestätigung');

    // Sofortige Bestätigung senden - WICHTIG: Ohne auf busy zu prüfen
    try {
      if (this.client.connection.socket && this.client.connection.connected) {
        const ackCommand = Buffer.from([0x80, 0x00, 0x00]);
        this.client.connection.socket.write(ackCommand);
        this.log.info("DEBUG: Bestätigung (80-00-00) an Terminal gesendet");
      }
    } catch (error) {
      this.log.error(`DEBUG: Fehler beim Senden der Bestätigung: ${error.message}`);
    }
  }

  /**
   * Prüft, ob die Daten eine Fehlermeldung enthalten
   * @param {Buffer} data - Die zu prüfenden Daten
   * @returns {boolean} True wenn Fehlermeldung, sonst False
   */
  _isErrorMessage(data) {
    const [cmd1] = this.ZVT.MESSAGE.ERROR;
    return data.length >= 2 && data[0] === cmd1;
  }

  /**
   * Behandelt eine Fehlermeldung
   * @param {Buffer} data - Die Fehlermeldungsdaten
   * @returns {Object} Zustandsobjekt { complete, expectsMoreData }
   */
  _handleErrorMessage(data) {
    // Die nächsten Bytes enthalten den Fehlercode
    const errorCode = data.length >= 3 ? data.slice(1, 3).toString("hex").toUpperCase() : "FF";
    this.log.warn(`DEBUG: Abbruchnachricht vom Terminal empfangen, Fehlercode: ${errorCode}`);

    const errorDescription = this.statusCodes[errorCode] || "Unbekannter Fehler";
    this.log.warn(`DEBUG: Fehlerbeschreibung: ${errorDescription}`);

    return { complete: true, expectsMoreData: false };
  }

  /**
   * Prüft, ob die Daten eine Erfolgsmeldung enthalten
   * @param {Buffer} data - Die zu prüfenden Daten
   * @returns {boolean} True wenn Erfolgsmeldung, sonst False
   */
  _isSuccessMessage(data) {
    const [cmd1, cmd2, cmd3] = this.ZVT.MESSAGE.RECEIPT;
    return data.length >= 3 && data[0] === cmd1 && data[1] === cmd2 && data[2] === cmd3;
  }

  /**
   * Prüft, ob die Daten eine Befehlsbestätigung enthalten
   * @param {Buffer} data - Die zu prüfenden Daten
   * @returns {boolean} True wenn Befehlsbestätigung, sonst False
   */
  _isCommandConfirmation(data) {
    const [cmd1, cmd2, cmd3] = this.ZVT.MESSAGE.SIMPLE_SUCCESS;
    return data.length === 3 && data[0] === cmd1 && data[1] === cmd2 && data[2] === cmd3;
  }

  /**
   * Prüft, ob die Daten ein Ende-der-Übertragung-Zeichen enthalten
   * @param {Buffer} data - Die zu prüfenden Daten
   * @returns {boolean} True wenn ETX, sonst False
   */
  _isEndOfTransmission(data) {
    return data.length > 0 && data[data.length - 1] === 0x03;
  }

  /**
   * Extrahiert Kartendaten aus einer Autorisierungsnachricht
   * @param {Buffer} data Die Autorisierungsnachricht
   * @returns {Object} Extrahierte Kartendaten
   */
  _extractCardDataFromAuthorization(data) {
    try {
      this.log.info(`Extrahiere Kartendaten aus ZVT-Paket (${data.length} Bytes)`);

      // Debug-Logging der Rohdaten
      this.log.info(`DEBUG: ROHDATEN HEX: ${data.toString("hex").toUpperCase()}`);
      for (let i = 0; i < data.length; i += 20) {
        const chunk = data.slice(i, Math.min(i + 20, data.length));
        this.log.info(
          `DEBUG: BYTES ${i}-${Math.min(i + 19, data.length - 1)}: ${chunk
            .toString("hex")
            .toUpperCase()} | ASCII: ${this._bytesToAscii(chunk)}`
        );
      }

      // 1. Terminal-ID extrahieren - verbesserte Methode
      // Primäre Methode: Suche nach dem Terminal-ID Tag (0x29) im BMP-Format
      let pos = data.indexOf(Buffer.from([0x00, 0x29]));
      if (pos > 0 && pos + 8 < data.length) {
        this.client.lastTerminalId = data
          .slice(pos + 2, pos + 6)
          .toString("hex")
          .toUpperCase();
        this.log.info(`Terminal-ID extrahiert: ${this.client.lastTerminalId}`);
      } else {
        // Alternatives Terminal-ID Muster für Verifone (dynamischer Ansatz)
        for (let i = 3; i < data.length - 5; i++) {
          if ((data[i] === 0x29 || data[i] === 0x17) && i + 4 < data.length) {
            this.client.lastTerminalId = data
              .slice(i + 1, i + 5)
              .toString("hex")
              .toUpperCase();
            this.log.info(`Terminal-ID alternativ extrahiert: ${this.client.lastTerminalId}`);
            break;
          }
        }
      }

      // Verbesserte Methode: Suche nach Terminal-ID im TLV-Format
      // Suche nach dem Terminal-ID Text in den Belegdaten
      if (!this.client.lastTerminalId || this.client.lastTerminalId === "EEEE1970") {
        const asciiData = this._bytesToAscii(data);

        // Suche nach verschiedenen Formaten der Terminal-ID im Text
        const terminalIdPatterns = [
          /Terminal-ID\s*[:=]\s*([A-Za-z0-9]{8})/i,
          /Terminal-ID\s*[:=]\s*([0-9]{8})/i,
          /Terminal-ID\s*[:=]\s*\D*([0-9]{8})/i,
          /Terminal-ID\s*[:.]\s*([0-9]{8})/i,
          /Terminal-ID\s*[:.]\s*\D*([0-9]{8})/i
        ];

        for (const pattern of terminalIdPatterns) {
          const match = asciiData.match(pattern);
          if (match && match[1]) {
            const terminalId = match[1].trim();
            this.log.info(`Terminal-ID aus Belegtext extrahiert: ${terminalId}`);
            this.client.lastTerminalId = terminalId;
            break;
          }
        }
      }

      // Suche nach Terminal-ID im TLV-Format mit Tag 0x24 (Display-Texte)
      // und dann nach Terminal-ID im Text
      if (!this.client.lastTerminalId || this.client.lastTerminalId === "EEEE1970") {
        for (let i = 0; i < data.length - 10; i++) {
          if (data[i] === 0x06 && data[i+1] === 0xD3) {
            // TLV-Container gefunden, durchsuche den Inhalt
            const tlvData = this._bytesToAscii(data.slice(i+2));

            // Suche nach verschiedenen Formaten der Terminal-ID im Text
            for (const pattern of terminalIdPatterns) {
              const match = tlvData.match(pattern);
              if (match && match[1]) {
                const terminalId = match[1].trim();
                this.log.info(`Terminal-ID aus TLV-Container extrahiert: ${terminalId}`);
                this.client.lastTerminalId = terminalId;
                break;
              }
            }
          }
        }
      }

      // 2. TA-Nr extrahieren
      this.client.lastTraceNumber = this._extractTraceNumber(data) || "";

      // 3. Kartentyp extrahieren
      this.client.lastCardType = this._extractCardType(data);

      // 4. Kartennummer extrahieren
      pos = data.indexOf(Buffer.from([0x22]));
      if (pos >= 0 && pos + 2 < data.length) {
        this.log.info(`DEBUG: BMP 0x22 (PAN) Tag gefunden an Position ${pos}`);

        // Bestimme die Länge des Datenfelds
        const lengthByte1 = data[pos + 1];
        const lengthByte2 = data[pos + 2];
        const length = (lengthByte1 & 0x0f) * 10 + (lengthByte2 & 0x0f);

        this.log.info(`DEBUG: BMP 0x22 (PAN) Länge: ${length}`);

        if (length > 0 && pos + 3 + length <= data.length) {
          const panBytes = data.slice(pos + 3, pos + 3 + length);

          // Konvertiere zu Hex-String
          let cardNumber = panBytes
            .toString("hex")
            .toUpperCase()
            .replace(/F/g, "")
            .replace(/-/g, "")
            .replace(/E/g, "*");

          this.log.info(`DEBUG: BMP 0x22 (PAN) extrahiert: ${cardNumber}`);

          // Extrahiere die letzten 4 Stellen
          if (cardNumber.length >= 4) {
            this.client.lastCardNumber = cardNumber.slice(-4);
            this.log.info(`Kartennummer extrahiert (letzte 4 Stellen): ${this.client.lastCardNumber}`);
          }
        }
      } else {
        // Alternative Extraktion für Verifone und andere Terminals
        // Suche nach häufigen Kartentyp-Tags (0x8A, 0x19)
        for (let i = 4; i < data.length - 8; i++) {
          if ((data[i] === 0x8a || data[i] === 0x19) && i + 5 < data.length) {
            // Tag für Kartentyp gefunden, möglicherweise folgt die Kartennummer
            // Suche nach Hex-Mustern, die auf eine Kartennummer hindeuten (häufig 4-6 Positionen danach)
            for (let j = i + 1; j < i + 8 && j < data.length - 6; j++) {
              // Suche nach xx..xx Muster (letzte 4-6 Stellen der Karte)
              if (data[j] >= 0x30 && data[j] <= 0x39 &&
                  data[j+1] >= 0x30 && data[j+1] <= 0x39 &&
                  data[j+2] >= 0x30 && data[j+2] <= 0x39 &&
                  data[j+3] >= 0x30 && data[j+3] <= 0x39) {

                const possibleCardNumber = data.slice(j, j + 4).toString('ascii');
                this.client.lastCardNumber = possibleCardNumber;
                this.log.info(`Kartennummer alternativ extrahiert: ${this.client.lastCardNumber}`);
                break;
              }
            }
            break;
          }
        }
      }

      // 5. Autorisierungscode extrahieren
      pos = data.indexOf(Buffer.from([0x3b]));
      if (pos > 0 && pos + 1 < data.length) {
        let startPos = pos + 1;
        let endPos = startPos;
        // Finde das Ende des ASCII-Strings (nur Ziffern)
        while (
          endPos < data.length &&
          endPos < startPos + 20 &&
          data[endPos] >= 0x30 &&
          data[endPos] <= 0x39
        ) {
          endPos++;
        }
        if (endPos > startPos) {
          this.client.lastAuthCode = data.slice(startPos, endPos).toString("ascii");
          this.log.info(`Autorisierungscode extrahiert: ${this.client.lastAuthCode}`);
        }
      } else {
        // Alternative Autorisierungscode-Extraktion für Verifone
        // Suche nach typischen Mustern für Auth-Code
        for (let i = 4; i < data.length - 8; i++) {
          if (data[i] === 0x89 && i + 1 < data.length) {
            // Tag 0x89 entspricht dem Auth-Code in der C#-Implementierung
            let length = 6; // Standardlänge für Auth-Code
            if (i + 1 + length <= data.length) {
              this.client.lastAuthCode = data.slice(i + 1, i + 1 + length).toString('ascii');
              this.log.info(`Autorisierungscode alternativ extrahiert: ${this.client.lastAuthCode}`);
            }
            break;
          }
        }
      }

      // 6. EMV-Daten extrahieren und formatieren
      const emvData = this._extractEMVData(data);
      if (emvData.found) {
        this.client.lastEmvData = this._formatEMVData(emvData.data);
        this.log.info(`EMV-Daten formatiert: ${this.client.lastEmvData}`);
      } else {
        this.log.info("Keine EMV-Daten gefunden");
        this.client.lastEmvData = this._formatEMVData(""); // Leere Daten übergeben
      }

      this.client.lastReceiptNumber = this._extractReceiptNumber(data) || "";
      this.client.lastVUNumber = this._extractVUNumber(data) || "";

      return {
        terminalId: this.client.lastTerminalId || "",
        traceNumber: this.client.lastTraceNumber || "",
        cardNumber: this.client.lastCardNumber || "",
        cardType: this.client.lastCardType || "",
        authCode: this.client.lastAuthCode || "",
        emv: this.client.lastEmvData || "",
        receiptNumber: this.client.lastReceiptNumber || "",
        vuNumber: this.client.lastVUNumber || ""
      };
    } catch (error) {
      this.log.error(`Fehler beim Extrahieren der Kartendaten: ${error.message}`);
      return {};
    }
  }

  /**
 * Extrahiert die TA-Nummer aus den ZVT-Daten
 * @param {Buffer} data Die ZVT-Daten
 * @returns {string} Die TA-Nummer mit führenden Nullen (6 Stellen)
 */
_extractTraceNumber(data) {
  // Direktes Durchsuchen der Bytes nach dem Muster
  for (let i = 0; i < data.length - 5; i++) {
    if (data[i] === 0x01 &&
        data[i+1] === 0x0B &&
        data[i+2] === 0x00 &&
        data[i+3] === 0x01) {
      // Muster gefunden, nächstes Byte ist die TA-Nr
      const traceByte = data[i+4];

      // Konvertiere das Byte zu seiner hexadezimalen Darstellung (BCD)
      const traceHex = traceByte.toString(16).padStart(2, '0').toLowerCase();

      // Füge eine 1 vorne an
      const traceNumber = '1' + traceHex;

      // Formatiere mit führenden Nullen auf 6 Stellen
      const formattedTraceNumber = traceNumber.padStart(6, '0');

      this.log.info(`TA-Nr extrahiert: Byte 0x${traceHex} -> mit 1 vorangestellt: ${traceNumber} -> formatiert: ${formattedTraceNumber}`);
      return formattedTraceNumber;
    }
  }

  // Alternative TA-Nummer-Extraktion für Verifone - KORRIGIERT für 3-Byte-BCD
  for (let i = 3; i < data.length - 5; i++) {
    if (data[i] === 0x0b || data[i] === 0x88) {
      // Tag 0x0B oder 0x88 für Trace-Nummer (BMP-Parser.cs)
      // TA-Nummer ist als 3-Byte-BCD kodiert
      if (i + 3 < data.length) {
        const byte1 = data[i + 1];
        const byte2 = data[i + 2]; 
        const byte3 = data[i + 3];
        
        // Konvertiere BCD-Bytes zu Dezimalstring
        const traceNumber = byte1.toString(16).padStart(2, '0') + 
                          byte2.toString(16).padStart(2, '0') + 
                          byte3.toString(16).padStart(2, '0');
        
        // Formatiere mit führenden Nullen auf 6 Stellen
        const formattedTraceNumber = traceNumber.padStart(6, '0');
        
        this.log.info(`TA-Nr alternativ extrahiert (3-Byte-BCD): ${traceNumber} -> formatiert: ${formattedTraceNumber}`);
        return formattedTraceNumber;
      }
    }
  }

  // Verbesserte Methode: Suche nach TA-Nr im Belegtext
  const asciiData = this._bytesToAscii(data);

  // Suche nach verschiedenen Formaten der TA-Nr im Text
  const traceNumberPatterns = [
    /TA-Nr\s*[:=]\s*([0-9]{6})/i,
    /TA-Nr\s*[:=]\s*([0-9]{1,6})/i,
    /TA-Nr\s*([0-9]{6})/i,
    /TA-Nr\s*([0-9]{1,6})/i,
    /TA-Nr\s*[:=]?\s*(\d+)\s+/i,
    /TA-Nr\s*[:=]?\s*(\d+)/i
  ];

  for (const pattern of traceNumberPatterns) {
    const match = asciiData.match(pattern);
    if (match && match[1]) {
      const traceNumber = match[1].trim();
      // Formatiere mit führenden Nullen auf 6 Stellen
      const formattedTraceNumber = traceNumber.padStart(6, '0');
      this.log.info(`TA-Nr aus Belegtext extrahiert: ${formattedTraceNumber}`);
      return formattedTraceNumber;
    }
  }

  // Suche nach TA-Nr im TLV-Format mit Tag 0x06 0xD3
  for (let i = 0; i < data.length - 10; i++) {
    if (data[i] === 0x06 && data[i+1] === 0xD3) {
      // TLV-Container gefunden, durchsuche den Inhalt
      const tlvData = this._bytesToAscii(data.slice(i+2));

      // Verschiedene Muster für TA-Nr im Beleg
      for (const pattern of traceNumberPatterns) {
        const match = tlvData.match(pattern);
        if (match && match[1]) {
          const traceNumber = match[1].trim();
          // Formatiere mit führenden Nullen auf 6 Stellen
          const formattedTraceNumber = traceNumber.padStart(6, '0');
          this.log.info(`TA-Nr aus TLV-Container extrahiert: ${formattedTraceNumber}`);
          return formattedTraceNumber;
        }
      }
    }
  }

  this.log.warn("Kein TA-Nr Muster gefunden");
  return null;
}

  /**
   * Extrahiert den Kartentyp aus den ZVT-Daten
   * @param {Buffer} data Die ZVT-Daten
   * @returns {string} Der Kartentyp oder "Unbekannt"
   */
  _extractCardType(data) {
    // Methode 1: Suche nach 0x8b, 0xf0, 0xf9 (gängiges Format für girocard)
    let pos = data.indexOf(Buffer.from([0x8b, 0xf0, 0xf9]));
    if (pos >= 0) {
      let endPos = pos + 3;
      while (
        endPos < data.length &&
        data[endPos] >= 0x20 &&
        data[endPos] <= 0x7e
      ) {
        endPos++;
      }
      if (endPos > pos + 3) {
        const cardType = data.slice(pos + 3, endPos).toString("ascii");
        this.log.info(`Kartentyp über 0x8bf0f9 extrahiert: ${cardType}`);
        return cardType;
      }
    }

    // Methode 2: Suche nach 0x8b, 0xf1, 0xf1 (gängiges Format für Visa)
    pos = data.indexOf(Buffer.from([0x8b, 0xf1, 0xf1]));
    if (pos >= 0) {
      let endPos = pos + 3;
      while (
        endPos < data.length &&
        data[endPos] >= 0x20 &&
        data[endPos] <= 0x7e
      ) {
        endPos++;
      }
      if (endPos > pos + 3) {
        const cardType = data.slice(pos + 3, endPos).toString("ascii");
        this.log.info(`Kartentyp über 0x8bf1f1 extrahiert: ${cardType}`);
        return cardType;
      }
    }

    // Methode 3: Suche nach BMP-Tag 0x8A oder 0x19 (Kartentyp-ID)
    for (let i = 3; i < data.length - 2; i++) {
      if ((data[i] === 0x8a || data[i] === 0x19) && i + 1 < data.length) {
        const cardTypeId = data[i + 1];

        // Mapping für gängige Kartentypen basierend auf dem C# Code
        const cardTypeMap = {
          0x05: "girocard",
          0x06: "Mastercard",
          0x0a: "Visa",
          0x0d: "V PAY",
          0x2e: "Maestro",
        };

        if (cardTypeMap[cardTypeId]) {
          this.log.info(`Kartentyp über ID 0x${cardTypeId.toString(16)} erkannt: ${cardTypeMap[cardTypeId]}`);
          return cardTypeMap[cardTypeId];
        } else {
          this.log.info(`Unbekannter Kartentyp-ID: 0x${cardTypeId.toString(16)}`);
        }
      }
    }

    // Methode 4: Suche nach Kartentypen im ASCII-Text
    const asciiData = this._bytesToAscii(data);
    const cardTypePatterns = [
      { pattern: "girocard", type: "girocard" },
      { pattern: "visa", type: "Visa" },
      { pattern: "mastercard", type: "Mastercard" },
      { pattern: "maestro", type: "Maestro" }
    ];

    for (const { pattern, type } of cardTypePatterns) {
      if (asciiData.toLowerCase().includes(pattern)) {
        this.log.info(`Kartentyp durch Text-Analyse erkannt: ${type}`);
        return type;
      }
    }

    return "Unbekannt";
  }

  /**
   * Extrahiert die Belegnummer aus den ZVT-Daten
   * @param {Buffer} data Die ZVT-Daten
   * @returns {string} Die Belegnummer oder null falls nicht gefunden
   */
  _extractReceiptNumber(data) {
    // Alle Vorkommen des Tags 0x87 sammeln
    let positions = [];
    for (let i = 0; i < data.length - 2; i++) {
      if (data[i] === 0x87) {
        positions.push(i);
      }
    }
    if (positions.length > 0) {
      // Letztes Vorkommen verwenden
      const pos = positions[positions.length - 1];
      if (pos + 2 < data.length) {
        // Die zwei Bytes nach dem Tag enthalten die BCD-kodierte Belegnummer
        const b1 = data[pos + 1];
        const b2 = data[pos + 2];
        // Jedes Byte wird in zwei Nibbles zerlegt (BCD-Dekodierung)
        const digit1 = (b1 >> 4) & 0x0F;
        const digit2 = b1 & 0x0F;
        const digit3 = (b2 >> 4) & 0x0F;
        const digit4 = b2 & 0x0F;
        const receiptNumber = `${digit1}${digit2}${digit3}${digit4}`;
        this.log.info(`Belegnummer-Tag gefunden an Position ${pos}, extrahierte Belegnummer: ${receiptNumber}`);
        return receiptNumber;
      } else {
        this.log.warn("Belegnummer-Tag gefunden, aber unvollständig");
      }
    }

    // Verbesserte Methode: Suche nach Belegnummer im Belegtext
    const asciiData = this._bytesToAscii(data);

    // Suche nach verschiedenen Formaten der Belegnummer im Text
    const receiptNumberPatterns = [
      /Beleg-Nr\s*[:=]\s*([0-9]{4})/i,
      /Beleg-Nr\s*[:=]\s*([0-9]{1,4})/i,
      /Beleg-Nr\s*([0-9]{4})/i,
      /Beleg-Nr\s*([0-9]{1,4})/i,
      /BNr\s*[:=]?\s*([0-9]{4})/i,
      /BNr\s*[:=]?\s*([0-9]{1,4})/i,
      /BNr\s*([0-9]{4})/i,
      /BNr\s*([0-9]{1,4})/i,
      /TA-Nr\s*[0-9]+\s*BNr\s*([0-9]+)/i
    ];

    for (const pattern of receiptNumberPatterns) {
      const match = asciiData.match(pattern);
      if (match && match[1]) {
        const receiptNumber = match[1].trim();
        // Formatiere mit führenden Nullen auf 4 Stellen
        const formattedReceiptNumber = receiptNumber.padStart(4, '0');
        this.log.info(`Belegnummer aus Belegtext extrahiert: ${formattedReceiptNumber}`);
        return formattedReceiptNumber;
      }
    }

    // Suche nach Belegnummer im TLV-Format mit Tag 0x06 0xD3
    for (let i = 0; i < data.length - 10; i++) {
      if (data[i] === 0x06 && data[i+1] === 0xD3) {
        // TLV-Container gefunden, durchsuche den Inhalt
        const tlvData = this._bytesToAscii(data.slice(i+2));

        // Verschiedene Muster für Belegnummer im Beleg
        for (const pattern of receiptNumberPatterns) {
          const match = tlvData.match(pattern);
          if (match && match[1]) {
            const receiptNumber = match[1].trim();
            // Formatiere mit führenden Nullen auf 4 Stellen
            const formattedReceiptNumber = receiptNumber.padStart(4, '0');
            this.log.info(`Belegnummer aus TLV-Container extrahiert: ${formattedReceiptNumber}`);
            return formattedReceiptNumber;
          }
        }
      }
    }

    this.log.warn("Keine Belegnummer gefunden");
    return "0000";
  }

  /**
   * Extrahiert die VU-Nummer aus den ZVT-Daten
   * @param {Buffer} data Die ZVT-Daten
   * @returns {string} Die VU-Nummer oder null falls nicht gefunden
   */
  _extractVUNumber(data) {
    // Suche nach Muster für VU-Nummern (verschiedene mögliche Tags)
    const vuTags = [
      { tag: [0x2A], name: "Merchant ID" },
      { tag: [0x03, 0x01], name: "VU Number" },
      { tag: [0x10], name: "Merchant ID (alt)" },
      { tag: [0x8C], name: "Merchant ID (Kartentyp-ID)" }
    ];

    for (const { tag, name } of vuTags) {
      let pos = -1;
      for (let i = 0; i < data.length - tag.length; i++) {
        let found = true;
        for (let j = 0; j < tag.length; j++) {
          if (data[i + j] !== tag[j]) {
            found = false;
            break;
          }
        }

        if (found) {
          pos = i;
          break;
        }
      }

      if (pos >= 0) {
        pos += tag.length;

        // Länge könnte direkt folgen oder fest definiert sein
        let len = 8; // Standard-Länge für VU-Nummern

        if (pos < data.length) {
          // Wenn nächstes Byte 0-9 ist, verwende es als Länge
          const possibleLen = data[pos];
          if (possibleLen > 0 && possibleLen < 20) {
            len = possibleLen;
            pos++;
          }
        }

        if (pos + len <= data.length) {
          const vuData = data.slice(pos, pos + len);

          // VU-Nummer kann als ASCII oder Hex kodiert sein
          let vuNumber = "";

          // Versuche erst als ASCII zu interpretieren
          const vuAscii = vuData.toString('ascii').trim();
          if (/^[a-zA-Z0-9]+$/.test(vuAscii)) {
            vuNumber = vuAscii;
          } else {
            // Falls kein sauberer ASCII-String, verwende Hex
            vuNumber = vuData.toString('hex').toUpperCase();
          }

          this.log.info(`VU-Nummer (${name}) extrahiert: ${vuNumber}`);
          return vuNumber;
        }
      }
    }

    // Verbesserte Methode: Suche nach VU-Nummer im Belegtext
    const asciiData = this._bytesToAscii(data);

    // Erweiterte Liste von Mustern für VU-Nummer im Text
    const vuPatterns = [
      /VU[:\-\s\.]*(\d+)/i,
      /VU[:\-\s\.]*Nr[:\-\s\.]*(\d+)/i,
      /VU[:\-\s\.]*Nr[:\-\s\.]*([a-zA-Z0-9]+)/i,
      /VU-Nr\s*[:=]\s*([a-zA-Z0-9]+)/i,
      /Merchant[\s\.]*ID[:\-\s\.]*([a-zA-Z0-9]+)/i,
      /H[äa]ndler[\s\.]*ID[:\-\s\.]*([a-zA-Z0-9]+)/i,
      /VU-Nr\s*[:=]?\s*\D*([a-zA-Z0-9]{8})/i,
      /VU-Nr\s*[:=]?\s*\D*([a-zA-Z0-9]{1,8})/i,
      /VU-Nr\s*[:.]\s*([a-zA-Z0-9]{8})/i,
      /VU-Nr\s*[:.]\s*([a-zA-Z0-9]{1,8})/i
    ];

    for (const pattern of vuPatterns) {
      const match = asciiData.match(pattern);
      if (match && match[1]) {
        const vuNumber = match[1].trim();
        this.log.info(`VU-Nummer über Text-Pattern gefunden: ${vuNumber}`);
        return vuNumber;
      }
    }

    // Suche nach VU-Nummer im TLV-Format mit Tag 0x06 0xD3
    for (let i = 0; i < data.length - 10; i++) {
      if (data[i] === 0x06 && data[i+1] === 0xD3) {
        // TLV-Container gefunden, durchsuche den Inhalt
        const tlvData = this._bytesToAscii(data.slice(i+2));

        // Verschiedene Muster für VU-Nummer im Beleg
        for (const pattern of vuPatterns) {
          const match = tlvData.match(pattern);
          if (match && match[1]) {
            const vuNumber = match[1].trim();
            this.log.info(`VU-Nummer aus TLV-Container extrahiert: ${vuNumber}`);
            return vuNumber;
          }
        }
      }
    }

    this.log.warn("Keine VU-Nummer gefunden");
    return null;
  }

  /**
   * Extrahiert EMV-Daten aus der Antwort
   * @param {Buffer} data - Die zu extrahierenden Daten
   * @returns {Object} Extrahierte EMV-Daten { found, data }
   */
  _extractEMVData(data) {
    // Liste von bekannten EMV Tags
    const emvTags = [
      { tag: [0x9f, 0x26], name: "Application Cryptogram" },
      { tag: [0x9f, 0x27], name: "Cryptogram Information Data" },
      { tag: [0x9f, 0x10], name: "Issuer Application Data" },
      { tag: [0x9f, 0x36], name: "Application Transaction Counter" },
      { tag: [0x95], name: "Terminal Verification Results" },
      { tag: [0x9f, 0x37], name: "Unpredictable Number" },
      { tag: [0x9f, 0x33], name: "Terminal Capabilities" },
      { tag: [0x9a], name: "Transaction Date" },
      { tag: [0x9c], name: "Transaction Type" },
      { tag: [0x3c], name: "Additional Data" },
      { tag: [0x5A], name: "PAN" }, // Kartennummer
      { tag: [0x5F, 0x24], name: "Expiry Date" },
      { tag: [0x9F, 0x21], name: "Transaction Time" },
      { tag: [0x9F, 0x1E], name: "Interface Device Serial Number" },
      { tag: [0x9F, 0x41], name: "Transaction Sequence Counter" }, // Könnte TA-Nr enthalten
      { tag: [0x9F, 0x02], name: "Amount, Authorized" },
      // Spezifisch für die TLV-Struktur
      { tag: [0x46], name: "EMV-Print-Data (Customer)" },
      { tag: [0x47], name: "EMV-Print-Data (Merchant)" },
      { tag: [0x60], name: "Application" },
      { tag: [0x43], name: "Application ID" },
      { tag: [0x44], name: "Application Preferred Name" },
    ];

    let emvDataFound = false;
    let emvData = "";
    let emvPrintDataFound = false;

    // Durchsuche die gesamten Daten nach allen EMV-Tags
    for (const emvTag of emvTags) {
      const tagBuffer = Buffer.from(emvTag.tag);
      let pos = 0;

      // Suche alle Vorkommen dieses Tags
      while ((pos = data.indexOf(tagBuffer, pos)) !== -1) {
        if (pos >= 0 && pos + tagBuffer.length + 1 < data.length) {
          const lengthByte = data[pos + tagBuffer.length];
          if (
            lengthByte > 0 &&
            pos + tagBuffer.length + 1 + lengthByte <= data.length
          ) {
            const tagValue = data.slice(
              pos + tagBuffer.length + 1,
              pos + tagBuffer.length + 1 + lengthByte
            );
            this.log.info(
              `EMV-Tag ${emvTag.name} (${tagBuffer
                .toString("hex")
                .toUpperCase()}) gefunden, Länge: ${lengthByte}, Wert: ${tagValue
                .toString("hex")
                .toUpperCase()}`
            );

            // Besondere Behandlung für EMV-Print-Daten Tags (0x46 und 0x47)
            if (emvTag.tag[0] === 0x46 || emvTag.tag[0] === 0x47) {
              this.log.info(
                `DEBUG: EMV-Print-Daten für Beleg gefunden: ${tagValue
                  .toString("hex")
                  .toUpperCase()}`
              );
              emvPrintDataFound = true;
            }

            // Für TLV-konforme Verarbeitung
            const tagHex = tagBuffer.toString("hex").toUpperCase();
            const lengthHex = lengthByte.toString(16).padStart(2, "0").toUpperCase();
            const valueHex = tagValue.toString("hex").toUpperCase();

            // Füge zur EMV-Datenmenge hinzu (falls noch nicht enthalten)
            const tlvString = tagHex + lengthHex + valueHex;
            if (!emvData.includes(tlvString)) {
              emvData += tlvString;
              emvDataFound = true;
            }
          }
        }
        pos += tagBuffer.length;
      }
    }

    // Suche auch nach einfachen EMV-Daten im ASCII-Format
    // Typische Muster sind "EMV" oder "AID="
    let simpleEmvPatterns = ["EMV", "AID=", "ARQC"];
    for (const pattern of simpleEmvPatterns) {
      const patternBuffer = Buffer.from(pattern, "ascii");
      let pos = 0;
      while ((pos = data.indexOf(patternBuffer, pos)) !== -1) {
        if (pos >= 0) {
          // Extrahiere einen erweiterten Block um das Muster herum
          const startPos = Math.max(0, pos - 10);
          const endPos = Math.min(data.length, pos + pattern.length + 30);
          const context = data.slice(startPos, endPos);

          // Füge zur EMV-Datenmenge hinzu
          const contextHex = context.toString("hex").toUpperCase();
          if (!emvData.includes(contextHex)) {
            emvData += contextHex;
            emvDataFound = true;
            this.log.info(`EMV-Kontext für '${pattern}' gefunden: ${contextHex}`);
          }
        }
        pos += patternBuffer.length;
      }
    }

    // Zusätzlich: Suche explizit nach dem Autorisierungscode im 0x3C Tag (Additional Data)
    let pos = data.indexOf(Buffer.from([0x3c]));
    if (pos >= 0 && pos + 2 < data.length) {
      const lengthByte = data[pos + 1];
      if (lengthByte > 0 && pos + 2 + lengthByte <= data.length) {
        const additionalData = data.slice(pos + 2, pos + 2 + lengthByte);
        this.log.info(
          `Additional Data (0x3C) gefunden: ${additionalData
            .toString("hex")
            .toUpperCase()}`
        );

        // Suche nach dem Autorisierungscode im Additional Data
        const authCodePos = additionalData.indexOf(Buffer.from("AUTH=", "ascii"));
        if (
          authCodePos >= 0 &&
          authCodePos + 5 + 6 <= additionalData.length
        ) {
          this.client.lastAuthCode = additionalData
            .slice(authCodePos + 5, authCodePos + 5 + 6)
            .toString("ascii");
          this.log.info(
            `Autorisierungscode aus Additional Data extrahiert: ${this.client.lastAuthCode}`
          );
        }

        // Füge die gesamten Additional Data zu den EMV-Daten hinzu
        emvData += additionalData.toString("hex").toUpperCase();
        emvDataFound = true;
      }
    }

    return { found: emvDataFound, data: emvData };
  }

  /**
   * Formatiert EMV-Daten basierend auf dem Kartentyp
   * @param {string} emvHexData - Rohdaten im HEX-Format
   * @returns {string} - Formatierte EMV-Daten für den Beleg
   */
  _formatEMVData(emvHexData) {
    try {
      this.log.info(`Formatiere EMV-Daten: ${emvHexData}`);

      // Kartentyp für die korrekte Formatierung
      const cardType = this.client.lastCardType ? this.client.lastCardType.toLowerCase() : "unbekannt";

      // Spezifische Formatierung basierend auf Kartentyp
      if (cardType.includes("girocard")) {
        // Format für girocard-Karten mit AID
        const girocardEMV = "000000000220200000048001000001/0000048001/0000/A000000359/020300";
        this.log.info(`Formatierte EMV-Daten für ${cardType}: ${girocardEMV}`);
        return girocardEMV;

      } else if (cardType.includes("visa")) {
        // Format für Visa-Karten
        const visaEMV = "0000000000/0000//00000000022020000000000000000//00";
        this.log.info(`Formatierte EMV-Daten für ${cardType}: ${visaEMV}`);
        return visaEMV;

      } else if (cardType.includes("master")) {
        // Format für Mastercard
        const mastercardEMV = "0000000000/0000//00000000022020000052801000000//00";
        this.log.info(`Formatierte EMV-Daten für ${cardType}: ${mastercardEMV}`);
        return mastercardEMV;

      } else if (cardType.includes("amex") || cardType.includes("american")) {
        // Format für American Express
        const amexEMV = "0000000000/0000//00000000022020000034001000000//00";
        this.log.info(`Formatierte EMV-Daten für ${cardType}: ${amexEMV}`);
        return amexEMV;

      } else {
        // Generisches Format für andere Kartentypen (ähnlich zu Visa)
        const genericEMV = "0000000000/0000//00000000022020000000000000000//00";
        this.log.info(`Formatierte EMV-Daten für ${cardType} (generisch): ${genericEMV}`);
        return genericEMV;
      }
    } catch (error) {
      this.log.error(`Fehler bei der EMV-Datenformatierung: ${error.message}`);
      return "0000000000/0000//00000000022020000000000000000//00";
    }
  }

  /**
   * Hilfsmethode zur Umwandlung von Bytes in lesbaren ASCII-Text
   * @param {Buffer} buffer Die zu konvertierenden Bytes
   * @returns {string} Der lesbare ASCII-Text
   */
  _bytesToAscii(buffer) {
    return zvtUtils.bytesToAscii(buffer);
  }

  /**
   * Korrigiert Zeichenkodierungsprobleme in Belegtexten
   * @param {string} text Der zu korrigierende Text
   * @returns {string} Der korrigierte Text
   */
  _fixReceiptText(text) {
    // Verwende die zentrale Methode aus zvt-utils
    return zvtUtils.fixText(text);
  }

  /**
   * Extrahiert Belege aus der Antwort
   * @param {Buffer} data - Die Antwortdaten
   */
  _extractReceipts(data) {
    if (this.client.useTLV) {
      const receipts = this._extractTLVReceipts(data);
      if (receipts.customer && receipts.customer.length > 0) {
        this.log.info("Kundenbeleg in TLV-Daten gefunden");
        this.client.receipt._storeReceipts(receipts.customer, null);
      }
      if (receipts.merchant && receipts.merchant.length > 0) {
        this.log.info("Händlerbeleg in TLV-Daten gefunden");
        this.client.receipt._storeReceipts(null, receipts.merchant);
      }
    } else {
      const textReceipts = this._extractTextReceipts(data);
      if (textReceipts.customer && textReceipts.customer.length > 0) {
        this.log.info("Kundenbeleg im Text-Modus gefunden");
        this.client.receipt._storeReceipts(textReceipts.customer, null);
      }
      if (textReceipts.merchant && textReceipts.merchant.length > 0) {
        this.log.info("Händlerbeleg im Text-Modus gefunden");
        this.client.receipt._storeReceipts(null, textReceipts.merchant);
      }

      // Spezialfall für einfache Erfolgsmeldung ohne Belege
      if (this._isSimpleSuccessResponse(data) && this.client.payment.paymentInProgress) {
        this.client.receipt._createEnhancedReceipts(false);
      }
    }
  }

  /**
   * Prüft auf einfache Erfolgsmeldung
   * @param {Buffer} data - Die zu prüfenden Daten
   * @returns {boolean} True wenn einfache Erfolgsmeldung, sonst False
   */
  _isSimpleSuccessResponse(data) {
    return data.length === 3 &&
           data[0] === 0x80 &&
           data[1] === 0x00 &&
           data[2] === 0x00;
  }

  /**
   * Extrahiert Belege aus TLV-formatierten Daten
   * @param {Buffer} data TLV-Daten
   * @returns {Object} Extrahierte Belege { customer, merchant }
   */
  _extractTLVReceipts(data) {
    const receipts = { customer: [], merchant: [] };
    try {
      // Prüfe auf Standard TLV-Format
      if (data[0] !== 0x06 || data[1] !== 0x00) {
        return receipts;
      }

      const totalLength = (data[2] << 8) | data[3];
      let pos = 4;
      const endPos = Math.min(pos + totalLength, data.length);

      while (pos < endPos) {
        const tag = data[pos++];
        let length = data[pos++];
        const value = data.slice(pos, pos + length);

        // Detailliertes Logging für unbekannte Tags
        this.log.debug(
          `TLV Tag: 0x${tag
            .toString(16)
            .padStart(2, "0")}, Länge: ${length}, Wert: ${value.toString(
            "hex"
          )}`
        );

        switch (tag) {
          case this.ZVT.TLV.RECEIPT_CUSTOMER:
            {
              const receiptData = data.slice(pos, pos + length);
              const lines = this._parseReceiptData(receiptData);
              if (lines.length > 0) {
                receipts.customer = lines;
                this.log.info(`Kundenbeleg mit ${lines.length} Zeilen extrahiert`);
              }
            }
            break;
          case this.ZVT.TLV.RECEIPT_MERCHANT:
            {
              const receiptData = data.slice(pos, pos + length);
              const lines = this._parseReceiptData(receiptData);
              if (lines.length > 0) {
                receipts.merchant = lines;
                this.log.info(`Händlerbeleg mit ${lines.length} Zeilen extrahiert`);
              }
            }
            break;
          case this.ZVT.TLV.RECEIPT_DATA:
            {
              const receiptData = data.slice(pos, pos + length);
              const lines = this._parseReceiptData(receiptData);
              if (lines.length > 0) {
                const receiptText = lines.join(" ").toUpperCase();
                if (
                  receiptText.includes("KUNDENBELEG") ||
                  receiptText.includes("CARDHOLDER") ||
                  receiptText.includes("KUNDEN") ||
                  receiptText.includes("CUSTOMER")
                ) {
                  receipts.customer = lines;
                  this.log.info(
                    `Kundenbeleg (aus generischen Daten) mit ${lines.length} Zeilen extrahiert`
                  );
                } else if (
                  receiptText.includes("HÄNDLERBELEG") ||
                  receiptText.includes("MERCHANT") ||
                  receiptText.includes("HÄNDLER") ||
                  receiptText.includes("HAENDLER")
                ) {
                  receipts.merchant = lines;
                  this.log.info(
                    `Händlerbeleg (aus generischen Daten) mit ${lines.length} Zeilen extrahiert`
                  );
                } else {
                  receipts.customer = lines;
                  this.log.info(
                    `Nicht kategorisierter Beleg mit ${lines.length} Zeilen als Kundenbeleg interpretiert`
                  );
                }
              }
            }
            break;
        }
        pos += length;
      }
      return receipts;
    } catch (error) {
      this.log.error("Fehler beim Extrahieren der TLV-Belege:", error.message);
      return receipts;
    }
  }

  /**
   * Extrahiert Belege aus Text-formatierten Daten
   * @param {Buffer} data Die Daten
   * @returns {Object} Extrahierte Belege { customer, merchant }
   */
  _extractTextReceipts(data) {
    // Implementation aus dem Original-Code beibehalten
    // Diese Funktion wird im Original nicht implementiert, daher leerer Rückgabewert
    return { customer: [], merchant: [] };
  }

  /**
   * Parst Belegdaten in einzelne Zeilen
   * @param {Buffer} data Die Belegdaten
   * @returns {Array} Array mit Belegzeilen
   */
  _parseReceiptData(data) {
    try {
      // Verbesserte Zeichenkodierungsbehandlung
      let receiptText;

      // Versuche verschiedene Kodierungen
      try {
        // Versuche zuerst Latin1 (ISO-8859-1), da dies häufig für deutsche Umlaute verwendet wird
        receiptText = data.toString("latin1");

        // Prüfe, ob die Umlaute korrekt sind
        if (receiptText.includes("�") ||
            receiptText.includes("├") ||
            receiptText.includes("╝") ||
            receiptText.includes("ñ")) {
          // Wenn nicht, versuche UTF-8
          receiptText = data.toString("utf8");
        }
      } catch (e) {
        // Fallback auf ASCII, wenn andere Kodierungen fehlschlagen
        receiptText = data.toString("ascii");
      }

      // Umlaute korrigieren
      receiptText = this._fixReceiptText(receiptText);

      // Versuche, die Zeilen anhand von Zeilenumbrüchen zu trennen
      let lines = receiptText
        .split(/[\r\n]+/)
        .map((line) => line.trim())
        .filter((line) => line.length > 0);

      // Wenn keine Zeilen gefunden wurden oder nur eine Zeile, versuche alternative Methode
      if (lines.length <= 1) {
        lines = [];
        let currentLine = "";

        // Suche nach TLV-Strukturen im Verifone-Format
        // Typisches Muster: 09 01 00 07 XX (Text) 09 01 00 07 XX (Text) ...
        let i = 0;
        while (i < data.length) {
          // Prüfe auf TLV-Muster für Textzeilen
          if (i + 4 < data.length &&
              data[i] === 0x09 &&
              data[i+1] === 0x01 &&
              data[i+2] === 0x00 &&
              data[i+3] === 0x07) {

            // Länge der Textzeile
            const textLength = data[i+4];
            i += 5; // Überspringe die Header-Bytes

            // Extrahiere die Textzeile
            if (i + textLength <= data.length) {
              const lineData = data.slice(i, i + textLength);
              let lineText = "";

              // Konvertiere Bytes zu Text
              for (let j = 0; j < lineData.length; j++) {
                if (lineData[j] >= 0x20 && lineData[j] <= 0x7e) {
                  lineText += String.fromCharCode(lineData[j]);
                }
              }

              // Füge die Zeile hinzu, wenn sie nicht leer ist
              if (lineText.trim().length > 0) {
                lines.push(lineText.trim());
              }

              i += textLength; // Überspringe die Textdaten
            } else {
              i++; // Inkrementiere den Index, wenn die Daten unvollständig sind
            }
          }
          // Prüfe auf Zeilenumbrüche und andere Steuerzeichen
          else if (data[i] === 0x0a || data[i] === 0x0d || data[i] === 0x0c) {
            if (currentLine.trim().length > 0) {
              lines.push(currentLine.trim());
              currentLine = "";
            }
            i++;
          }
          // Prüfe auf druckbare ASCII-Zeichen
          else if (data[i] >= 0x20 && data[i] <= 0x7e) {
            currentLine += String.fromCharCode(data[i]);
            i++;
          }
          // Überspringe nicht-druckbare Zeichen
          else {
            i++;
          }
        }

        // Füge die letzte Zeile hinzu, wenn sie nicht leer ist
        if (currentLine.trim().length > 0) {
          lines.push(currentLine.trim());
        }
      }

      // Nachbearbeitung: Entferne leere Zeilen und führende/nachfolgende Leerzeichen
      lines = lines
        .map(line => line.trim())
        .filter(line => line.length > 0);

      // Entferne Duplikate (manchmal werden Zeilen doppelt gesendet)
      const uniqueLines = [];
      for (let i = 0; i < lines.length; i++) {
        if (i === 0 || lines[i] !== lines[i-1]) {
          uniqueLines.push(lines[i]);
        }
      }

      return uniqueLines;
    } catch (error) {
      this.log.error("Fehler beim Parsen der Belegdaten:", error.message);
      return [];
    }
  }

  /**
   * Parst die ZVT-Antwort
   * @param {Buffer} data Empfangene Daten
   * @returns {Object} Geparstes Antwortobjekt
   */
  _parseResponse(data) {
    const result = {
      success: false,
      statusCode: "FF",
      rawData: data.toString("hex"),
      receiptNumber: null,
      cardType: null,
      cardNumber: null,
      expiryDate: null,
      traceNumber: null,
      authCode: null,
    };

    try {
      this.log.debug("Antwort (Hex):", data.toString("hex"));

      // Direkte Prüfung auf Fehlermeldung im Display-Text
      if (this._checkForErrorInDisplayText(data, result)) {
        return result;
      }

      // Standard-Erfolgsprüfungen
      if (this._isSimpleSuccessResponse(data)) {
        result.success = true;
        result.statusCode = "00";
        return result;
      }

      // Standard-Fehlerprüfung
      if (this._isErrorMessage(data)) {
        result.success = false;
        result.statusCode = data.slice(1, 3).toString("hex");
        return result;
      }

      // TLV-Verarbeitung
      if (this.client.useTLV && data.length > 4 && data[0] === 0x06 && data[1] === 0x00) {
        return this._parseTLVResponse(data);
      }

      // Explizite Prüfung auf StatusInformation (040F)
      if (this._isStatusInformationMessage(data)) {
        this.log.info("DEBUG: StatusInformation (040F) gefunden, verarbeite sie");
        const statusInfo = this._parseStatusInformation(data);
        result.success = statusInfo.success;
        result.statusCode = statusInfo.success ? "00" : (statusInfo.errorCode || "FF");
        result.error = statusInfo.errorMessage;
        result.amount = statusInfo.amount;
        result.cardType = statusInfo.cardType;
        result.terminalId = statusInfo.terminalId;
        return result;
      }

      // Explizite Prüfung auf Erfolgsindikator (06-0F-00)
      if (this._isSuccessMessage(data)) {
        this.log.info("DEBUG: Erfolgsindikator 06-0F-00 gefunden");
        result.success = true;
        result.statusCode = "00";
        return result;
      }

      // Standard-Fallback
      this.log.warn("DEBUG: Unbekanntes Antwortformat, markiere vorsichtshalber als nicht erfolgreich");
      result.success = false;
      result.statusCode = "FF";
      result.error = "Unbekanntes Antwortformat";
      return result;
    } catch (error) {
      this.log.error(`Fehler beim Parsen der Antwort: ${error.message}`);
      return {
        success: false,
        statusCode: "FF",
        rawData: data.toString("hex"),
        error: error.message,
      };
    }
  }

  /**
   * Prüft auf Fehlermeldungen im Display-Text
   * @param {Buffer} data - Die zu prüfenden Daten
   * @param {Object} result - Das Ergebnisobjekt
   * @returns {boolean} True wenn Fehler gefunden, sonst False
   */
  _checkForErrorInDisplayText(data, result) {
    // Effektivere Suche nach Display-Texten im Format 04 FF
    for (let i = 0; i < data.length - 4; i++) {
      if (data[i] === 0x04 && data[i + 1] === 0xff) {
        // Versuche den Text zu extrahieren (typischerweise 10+ Bytes später)
        const startPos = i + 10;
        if (startPos < data.length) {
          let displayText = "";
          for (let j = startPos; j < Math.min(data.length, startPos + 50); j++) {
            if (data[j] >= 0x20 && data[j] <= 0x7e) {
              displayText += String.fromCharCode(data[j]);
            }
          }

          const errorKeywords = [
            "abgebrochen", "Abbruch", "Fehler", "Error", "fehlgeschlagen", "cancelled",
          ];

          if (errorKeywords.some((keyword) => displayText.toLowerCase().includes(keyword.toLowerCase()))) {
            this.log.warn(`DEBUG: Fehlermeldung im Display-Text gefunden: "${displayText}"`);
            result.success = false;
            result.statusCode = "0C"; // Abbruch-Statuscode
            result.error = displayText || "Vorgang abgebrochen oder fehlgeschlagen";
            return true;
          }
        }
      }
    }
    return false;
  }

  /**
   * Parst eine TLV-formatierte Antwort
   * @param {Buffer} data Die TLV-Daten
   * @returns {Object} Das geparste Antwortobjekt
   */
  _parseTLVResponse(data) {
    const result = {
      success: false,
      statusCode: "FF",
      rawData: data.toString("hex"),
      receiptNumber: null,
      cardType: null,
      cardNumber: null,
      expiryDate: null,
      traceNumber: null,
      authCode: null,
      merchantName: null,
      terminalId: null,
      emvData: null,
    };

    try {
      if (data[0] !== 0x06 || data[1] !== 0x00) {
        return result;
      }

      const totalLength = (data[2] << 8) | data[3];
      let pos = 4;
      const endPos = Math.min(pos + totalLength, data.length);

      while (pos < endPos) {
        const tag = data[pos++];
        let length = data[pos++];
        const value = data.slice(pos, pos + length);

        // Detailliertes Logging für unbekannte Tags
        this.log.debug(
          `TLV Tag: 0x${tag
            .toString(16)
            .padStart(2, "0")}, Länge: ${length}, Wert: ${value.toString(
            "hex"
          )}`
        );

        switch (tag) {
          case this.ZVT.TLV.RESULT_CODE:
            result.statusCode = value.toString("hex");
            result.success = result.statusCode === "00";
            break;
          case this.ZVT.TLV.CARD_TYPE:
            result.cardType = value.toString("ascii");
            this.client.lastCardType = result.cardType;
            break;
          case this.ZVT.TLV.CARD_NUMBER:
            result.cardNumber = value.toString("ascii");
            this.client.lastCardNumber = result.cardNumber;
            break;
          case this.ZVT.TLV.CARD_EXPIRY:
            result.expiryDate = value.toString("hex");
            break;
          case this.ZVT.TLV.TRACE_NUMBER:
            // Konvertiere aus Hex in Dezimal
            result.traceNumber = parseInt(value.toString("hex"), 16).toString();
            this.client.lastTraceNumber = result.traceNumber;
            break;
          case this.ZVT.TLV.AUTH_CODE:
            result.authCode = value.toString("ascii");
            this.client.lastAuthCode = result.authCode;
            break;
          case this.ZVT.TLV.RECEIPT_ID:
            result.receiptNumber = value.toString("ascii");
            this.client.lastReceiptNumber = result.receiptNumber;
            break;
          case this.ZVT.TLV.EMV_DATA: // (0x3c)
            result.emvData = value.toString("hex");
            this.log.info(`EMV-Daten gefunden: ${result.emvData}`);
            this.client.lastEmvData = result.emvData;
            break;

          // Neue Tags für zusätzliche Informationen
          // Tag für Händlername
          case 0x24:
          case 0x2a: // Alternative Tags prüfen
          case 0x3a:
            result.merchantName = value.toString("ascii");
            this.log.info(`Händlername gefunden: ${result.merchantName}`);
            break;

          // Tag für Terminal-ID
          case 0x26:
          case 0x29:
          case 0x17:
            result.terminalId = value.toString("ascii");
            this.log.info(`Terminal-ID gefunden: ${result.terminalId}`);
            break;

          case 0x3c:
          case 0x1c:
            if (!result.emvData) { // Nur setzen, wenn noch nicht gesetzt
              result.emvData = value.toString("hex");
              this.log.info(`EMV-Daten gefunden: ${result.emvData}`);
              this.client.lastEmvData = result.emvData;
            }
            break;

          // Generisches Logging für unbekannte Tags (zur Fehlersuche)
          default:
            if (length > 0 && length < 64) {
              // Vernünftige Längenbegrenzung für Log-Ausgabe
              const valueHex = value.toString("hex");
              const valueAscii = value
                .toString("ascii")
                .replace(/[^\x20-\x7E]/g, "."); // Nur druckbare ASCII-Zeichen
              this.log.debug(
                `Unbekannter TLV-Tag: 0x${tag.toString(
                  16
                )}, Länge: ${length}, Wert (HEX): ${valueHex}, ASCII: ${valueAscii}`
              );
            }
            break;
        }

        pos += length;
      }

      return result;
    } catch (error) {
      this.log.error("Fehler beim Parsen der TLV-Antwort:", error.message);
      return result;
    }
  }
}
module.exports = ZVTParser;