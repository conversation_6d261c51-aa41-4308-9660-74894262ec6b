import win32com.client

# Instanz anlegen
goTSE = win32com.client.Dispatch("EasyTSE.EpsonTSE")

# Grundeinstellungen setzen
goTSE.TSE_Puk          = "123456"
goTSE.TSE_AdminPin     = "12345"
goTSE.TSE_TimeAdminPin = "54321"
goTSE.TSE_SecretKey    = "EPSONKEY"
goTSE.TSE_DeviceID     = "local_TSE"
goTSE.TSE_ClientID     = "K001"
goTSE.TSE_UserID       = goTSE.TSE_ClientID
goTSE.TSE_IP           = "127.0.0.1"
goTSE.TSE_Port         = 8009

# Beispiel: StorageInfo abfragen
if goTSE.TSEConnectOpenSend("GetStorageInfo") == 1:
    info = goTSE.oGetStorageInfo.Output.TSEInformation
    print("cdcId:", info.cdcId)
    print("serialNumber:", info.serialNumber)
    print("certificateExpirationDate:", info.certificateExpirationDate)
    print("createdSignatures:", info.createdSignatures)
    print("softwareVersion:", info.softwareVersion)
    print("hasPassedSelfTest:", info.hasPassedSelfTest)
    print("hasValidTime:", info.hasValidTime)
    print("tseInitializationState:", info.tseInitializationState)
    # ...weitere Felder siehe Originalbeispiel
    if not info.hasPassedSelfTest:
        print("Führe Selbsttest aus...")
        goTSE.Stack_RunTSESelfTest()
        goTSE.TSEConnectOpenSend("GetStorageInfo")
elif goTSE.cErrorList:
    print("Fehler bei GetStorageInfo:", goTSE.cErrorList)

# Beispiel: Zeit abgleichen
if goTSE.Stack_UpdateTime() == 1:
    print("Zeit gesetzt.")
elif goTSE.cErrorList:
    print("Fehler bei Zeit setzen:", goTSE.cErrorList)

# Beispiel: Buchung (Start & Finish in einem)
if goTSE.Stack_StartFinishTransaction("", 1, 1, 119.00, 107.00, 0, 0, 0, 226.00, 0) == 1:
    start = goTSE.oTransActionObjectStart
    finish = goTSE.oTransActionObjectFinish
    print("Transaktion gestartet:", start.LogTime, start.TransactionNumber)
    print("Transaktion abgeschlossen:", finish.LogTime, finish.Signature)
else:
    print("Buchung fehlgeschlagen:", goTSE.cErrorList)

# Optional: Clean Up, Verbindung beenden
goTSE = None
