// MQTT-Verbindungslogik
const mqtt = require('mqtt');
const loggerService = require('./logger-service');
const log = loggerService.getModuleLogger('MQTTClient');
const path = require('path');
const fs = require('fs');
const { URL } = require('url');
const config = require('./config.json');

class ImprovedMQTTClient {
  constructor() {
    this.client = null;
    this.config = null;
    this.apiConfig = null;
    this.topics = [];
    this.messageHandlers = new Map();
    this.connected = false;
    this.connectionAttempts = 0;
    this.maxConnectionAttempts = 5;
    this.pingInterval = null;
    this.pingFailCount = 0;
  } 

  /**
   * Initialisiert den MQTT-Client
   * @param {Object} config Lokale Konfiguration
   * @param {Object} apiConfig API-Konfiguration
   * @returns {Promise} Promise, der resolved, wenn die Verbindung erfolgreich ist
   */
  async initialize(config, apiConfig) {
    this.config = config;
    this.apiConfig = apiConfig;
    const brokerUrl = config.mqtt.broker;
const urlObj = new URL(brokerUrl);
    const host = urlObj.hostname;
    const clientId = `electron-${Math.random().toString(16).substring(2, 10)}`;
    
    log.info('MQTT: Initialisiere Verbindung zu:', brokerUrl);
    log.info('MQTT: Client-ID:', clientId);
    log.info('MQTT: Verwende Username:', apiConfig.system_config?.client_id || '');

    const options = {
      clientId,
      username: '',
      password: config.api.authKey,
      clean: true,
      keepalive: 60,
      connectTimeout: 30000,
      // SSL/TLS-Einstellungen
      rejectUnauthorized: false, // Auf true setzen, wenn Zertifikatsvalidierung erforderlich
      ca: undefined, // CA-Zertifikat könnte hier hinzugefügt werden
      // WebSocket-spezifische Optionen
      wsOptions: {
        rejectUnauthorized: false,
        headers: {
          'User-Agent': 'MQTTX/electron',
          'Origin': 'https://electron-app'
        },
        ca: undefined
      }
    };

    return new Promise((resolve, reject) => {
      try {
        this.connectionAttempts++;
        log.info(`MQTT: Verbindungsversuch ${this.connectionAttempts}/${this.maxConnectionAttempts}`);
        
        // Debug-Modus für detaillierte Fehlermeldungen
        process.env.DEBUG = 'mqtt*';
        
        // Verbindung herstellen
        this.client = mqtt.connect(brokerUrl, options);

        // Event-Handler einrichten
        this.client.on('connect', () => {
          log.info('MQTT: Verbindung erfolgreich hergestellt!');
          this.connected = true;
          this._subscribeToConfiguredTopics();
          resolve(true);
        });

        this.client.on('error', (err) => {
          log.error('MQTT: Fehler:', err.message || err);
          // Zu viele Versuche - aufgeben
          if (this.connectionAttempts >= this.maxConnectionAttempts && !this.connected) {
            log.error('MQTT: Maximale Anzahl an Verbindungsversuchen erreicht');
            reject(err);
          }
        });

        this.client.on('offline', () => {
          log.warn('MQTT: Client offline');
          this.connected = false;
        });

        this.client.on('reconnect', () => {
          log.info('MQTT: Versuche Wiederverbindung...');
        });
        
        this.client.on('close', () => {
          log.warn('MQTT: Verbindung geschlossen');
          this.connected = false;
        });

        this.client.on('message', (topic, message) => {
          this._handleIncomingMessage(topic, message);
        });

        // Timeout für die Verbindung
        setTimeout(() => {
          if (!this.connected) {
            log.error('MQTT: Verbindungs-Timeout');
            // Alternativen Verbindungsversuch starten, falls möglich
            if (this.connectionAttempts < this.maxConnectionAttempts) {
              log.info('MQTT: Versuche alternative Verbindungsmethode...');
              
              // Versuche alternativen Broker ohne Pfad
              const altBrokerUrl = `wss://${host}:${port}`;
              log.info('MQTT: Alternative URL:', altBrokerUrl);
              
              if (this.client) {
                this.client.end(true);
                this.client = null;
              }
              
              this.client = mqtt.connect(altBrokerUrl, options);
              
              // Erneutes Event-Setup für den neuen Client
              this.client.on('connect', () => {
                log.info('MQTT: Alternative Verbindung erfolgreich!');
                this.connected = true;
                this._subscribeToConfiguredTopics();
                resolve(true);
              });
              
              this.client.on('error', (err) => {
                log.error('MQTT: Fehler bei alternativer Verbindung:', err);
                reject(err);
              });

            } else {
              reject(new Error('MQTT-Verbindungs-Timeout'));
            }
          }
        }, 10000); // 10 Sekunden Timeout
      } catch (error) {
        log.error('MQTT: Initialisierungsfehler:', error);
        reject(error);
      }
    });
  }

  // Gibt die MQTT-Client-Instanz zurück
  getClient() {
    return this.client;
  }
  
  _subscribeToConfiguredTopics() {
    if (!this.connected || !this.client) {
      log.warn('MQTT: Kann Topics nicht abonnieren - nicht verbunden');
      return false;
    }

    let allSubscriptionsSuccessful = true;
    const subscribedTopics = [];
    const failedTopics = [];

    try {
      const topicsConfig = this.apiConfig.mqtt_config?.topics || {};
      
      // Alle Topics aus der Konfiguration abonnieren
      Object.entries(topicsConfig).forEach(([key, topic]) => {
        log.info(`Subscribing to topic: ${topic}`);
        try {
          this.client.subscribe(topic, { qos: 1 }, (err) => {
            if (err) {
              log.error(`Failed to subscribe to topic ${topic}: ${err.message}`);
              failedTopics.push(topic);
              allSubscriptionsSuccessful = false;
            } else {
              log.info(`Successfully subscribed to topic: ${topic}`);
              subscribedTopics.push(topic);
              this.topics.push(topic);
            }
          });
        } catch (subError) {
          log.error(`Error during subscription to topic ${topic}: ${subError.message}`);
          failedTopics.push(topic);
          allSubscriptionsSuccessful = false;
        }
      });

      // Warte einen Moment, um zu sehen, ob die Subscriptions erfolgreich sind
      setTimeout(() => {
        if (failedTopics.length > 0) {
          log.warn(`Topic subscription error: ${failedTopics.length} topics failed`);
          log.warn(`Failed topics: ${failedTopics.join(', ')}`);
        }
        if (subscribedTopics.length > 0) {
          log.info(`MQTT: ${subscribedTopics.length} Topics erfolgreich abonniert`);
        }
      }, 1000);

      return allSubscriptionsSuccessful;
    } catch (error) {
      log.error('MQTT: Fehler beim Abonnieren der Topics:', error);
      return false;
    }
  }

  registerHandler(topicKey, handler) {
    const topicsConfig = this.apiConfig.mqtt_config?.topics || {};
    const topic = topicsConfig[topicKey];
    
    if (!topic) {
      log.warn(`MQTT: Unbekannter Topic-Key: ${topicKey}`);
      return;
    }
    
    this.messageHandlers.set(topic, handler);
    log.info(`MQTT: Handler für ${topicKey} registriert`);
  }

  _handleIncomingMessage(topic, message) {
    try {
      log.info(`MQTT: Nachricht empfangen auf Topic: ${topic}`);
      
      // Nachricht parsen
      let messageData;
      try {
        messageData = JSON.parse(message.toString());
        // Ausführliches Logging der gesamten Nachricht
        log.debug(`MQTT: Empfangene Nachricht:`, JSON.stringify(messageData, null, 2));
      } catch (parseError) {
        log.error('MQTT: Fehler beim Parsen der Nachricht:', parseError);
        // Auch den Rohtext der Nachricht loggen
        log.debug('MQTT: Rohtext der fehlerhaften Nachricht:', message.toString());
        return;
      }
      
      // Spezifischen Handler aufrufen, falls vorhanden
      const handler = this.messageHandlers.get(topic);
      if (handler) {
        handler(topic, messageData);
      } else {
        // Fallback: Allgemeinen Handler aufrufen
        this._defaultMessageHandler(topic, messageData);
      }
    } catch (error) {
      log.error('MQTT: Fehler bei der Verarbeitung der Nachricht:', error);
    }
  }
  _defaultMessageHandler(topic, messageData) {
    log.info(`MQTT: Verwende Standard-Handler für Topic: ${topic}`);
    
    // Print-Job-Nachrichten speziell behandeln
    if (topic.includes('PrintJob/finished')) {
      log.info('MQTT: PrintJob-Nachricht erkannt, leite weiter an Hauptprozess');
      if (global.mainWindow && !global.mainWindow.isDestroyed()) {
        global.mainWindow.webContents.send('mqtt-message', { topic, data: messageData });
      }
    }
  }

  publish(topic, message) {
    return new Promise((resolve, reject) => {
      if (!this.connected || !this.client) {
        reject(new Error('MQTT: Nicht verbunden, kann nicht veröffentlichen'));
        return;
      }
      
      try {
        const messageString = typeof message === 'string' 
          ? message 
          : JSON.stringify(message);
        
        // Ausführliches Logging vor dem Senden
        log.debug(`MQTT: Sende Nachricht an ${topic}:`, typeof message === 'string' ? message : JSON.stringify(message, null, 2));
        
        this.client.publish(topic, messageString, { qos: 1 }, (err) => {
          if (err) {
            log.error(`MQTT: Fehler beim Veröffentlichen auf ${topic}:`, err);
            reject(err);
          } else {
            log.info(`MQTT: Erfolgreich veröffentlicht auf ${topic}`);
            resolve(true);
          }
        });
      } catch (error) {
        log.error('MQTT: Fehler beim Veröffentlichen:', error);
        reject(error);
      }
    });
  }

  // Startet eine Ping-Überwachung, um zu überprüfen, ob die Verbindung noch aktiv ist
  startPingMonitor(interval = 30000) {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
    }
    
    log.info(`MQTT: Starte Ping-Überwachung alle ${interval}ms`);
    this.pingInterval = setInterval(() => {
      if (this.connected && this.client) {
        log.info('MQTT: Sende Ping');
        // Verwende die MQTT-Ping-Funktionalität
        if (typeof this.client.ping === 'function') {
          this.client.ping();
        }
      } else {
        log.warn('MQTT: Ping übersprungen - keine aktive Verbindung');
        
        // Versuche, die Verbindung nach einigen fehlgeschlagenen Pings wiederherzustellen
        if (this.pingFailCount > 3) {
          log.warn('MQTT: Mehrere Pings fehlgeschlagen, versuche Wiederverbindung');
          this.reconnect().catch(err => {
            log.error('MQTT: Fehler bei Ping-initiierter Wiederverbindung:', err);
          });
          this.pingFailCount = 0;
        } else {
          this.pingFailCount = (this.pingFailCount || 0) + 1;
        }
      }
    }, interval);
  }

  // Methode zum manuellen Wiederverbinden
  async reconnect(delay = 1000, forceNewConnection = false) {
    if (forceNewConnection && this.client) {
      log.info('MQTT: Beende bestehende Verbindung für Neuverbindung');
      this.client.end(true);
      this.client = null;
    }
    
    return new Promise((resolve, reject) => {
      setTimeout(async () => {
        try {
          log.info('MQTT: Starte Wiederverbindungsversuch');
          await this.initialize(this.config, this.apiConfig);
          resolve(true);
        } catch (error) {
          log.error('MQTT: Wiederverbindungsversuch fehlgeschlagen:', error);
          reject(error);
        }
      }, delay);
    });
  }

  disconnect() {
    if (this.client) {
      log.info('MQTT: Beende Verbindung');
      this.client.end(true);
      this.client = null;
      this.connected = false;
      
      // Ping-Überwachung beenden
      if (this.pingInterval) {
        clearInterval(this.pingInterval);
        this.pingInterval = null;
      }
    }
  }
}

module.exports = ImprovedMQTTClient;