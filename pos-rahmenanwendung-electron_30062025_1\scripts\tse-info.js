// Script zum Abrufen von TSE-Informationen
const axios = require('axios');

const CONFIG = {
    serviceUrl: 'http://localhost:8765',
    clientId: 'K001'
};

async function getTseInfo() {
    try {
        console.log("=".repeat(60));
        console.log("TSE-Informationen abrufen");
        console.log("=".repeat(60));
        
        // TSE Info abrufen
        const response = await axios.post(`${CONFIG.serviceUrl}/info`, {
            clientId: CONFIG.clientId
        }, {
            timeout: 30000
        });

        let responseData = response.data;
        if (typeof responseData === 'string') {
            try {
                responseData = JSON.parse(responseData);
            } catch (parseError) {
                console.error('Fehler beim Parsen der Antwort:', parseError.message);
                return;
            }
        }

        if (responseData && responseData.success) {
            const data = responseData.data || {};
            
            console.log("TSE-Status:");
            console.log(`- Client ID: ${CONFIG.clientId}`);
            console.log(`- Serial Number: ${data.serialNumber || 'nicht verfügbar'}`);
            console.log(`- Public Key: ${data.publicKey ? 'vorhanden' : 'nicht verfügbar'}`);
            console.log(`- Signature Algorithm: ${data.signatureAlgorithm || 'nicht verfügbar'}`);
            console.log(`- Log Time Format: ${data.logTimeFormat || 'nicht verfügbar'}`);
            console.log(`- Last Signature Counter: ${data.signatureCounter || 'nicht verfügbar'}`);
            
            if (data.lastTransactionNumber) {
                console.log(`- Letzte Transaktionsnummer: ${data.lastTransactionNumber}`);
            }
            
            if (data.openTransactions && Array.isArray(data.openTransactions)) {
                console.log(`- Offene Transaktionen: ${data.openTransactions.length}`);
                data.openTransactions.forEach((tx, index) => {
                    console.log(`  ${index + 1}. Transaktion ${tx.number} (seit ${tx.startTime})`);
                });
            } else {
                console.log("- Offene Transaktionen: Information nicht verfügbar");
            }
            
        } else {
            const errorMessage = responseData?.error || 'Unbekannter Fehler';
            console.error("Fehler beim Abrufen der TSE-Informationen:", errorMessage);
        }
        
    } catch (error) {
        console.error("Fehler beim Abrufen der TSE-Informationen:", error.message);
        
        if (error.response) {
            console.error(`HTTP Status: ${error.response.status}`);
            console.error(`HTTP Data: ${JSON.stringify(error.response.data)}`);
        }
    }
}

// Script ausführen
if (require.main === module) {
    getTseInfo().then(() => {
        console.log("");
        console.log("Script beendet.");
        process.exit(0);
    }).catch((error) => {
        console.error("Unerwarteter Fehler:", error.message);
        process.exit(1);
    });
}

module.exports = { getTseInfo };