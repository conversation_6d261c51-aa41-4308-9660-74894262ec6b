/**
 * ZVT-Verbindung und Socket-Kommunikation
 */
const net = require("net");
const loggerService = require("./logger-service");

class ZVTConnection {
  constructor(client, config) {
    this.client = client;
    this.config = config;
    this.log = loggerService.getModuleLogger("ZVTConnection");
    this.socket = null;
    this.connected = false;
    this.busy = false;
    this.currentCallback = null;
    this.timeout = null;
    this.responseData = Buffer.alloc(0);
    this.receiptComplete = false;
    this.currentReceipt = null;
    this.receiptProcessingTimeout = null;
    this.currentTimeoutId = null;
  }

  /**
   * Setzt den Response-Buffer zurück
   */
  resetResponseBuffer() {
    this.responseData = Buffer.alloc(0);
    this.client.receipt.receiptBuffer = { customer: [], merchant: [] };
    this.currentReceipt = null;
    this.receiptComplete = false;
  }

  /**
   * Verbindung zum Terminal herstellen
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async connect() {
    if (this.connected) {
      this.log.info("Terminal bereits verbunden");
      return true;
    }
    if (!this.config.zvt_ip || !this.config.zvt_port) {
      this.log.warn(
        "ZVT-Konfiguration unvollständig, Terminal wird nicht verwendet"
      );
      return false;
    }
    return new Promise((resolve) => {
      try {
        this.log.info(
          "Stelle Verbindung zum Terminal her:",
          this.config.zvt_ip,
          "Port:",
          this.config.zvt_port
        );
        if (this.socket) {
          this.socket.removeAllListeners();
          try {
            this.socket.destroy();
          } catch (e) {}
          this.socket = null;
        }
        const connectTimeout = setTimeout(() => {
          this.log.error("Timeout beim Verbindungsaufbau");
          if (this.socket) {
            this.socket.destroy();
            this.socket = null;
          }
          resolve(false);
        }, 10000);
        this.socket = new net.Socket();
        this.socket.on("connect", () => {
          clearTimeout(connectTimeout);
          this.log.info("Verbindung zum Terminal hergestellt");
          this.connected = true;
          this.socket.setKeepAlive(true, 60000); // Keep-Alive
          this.log.info("TCP Keep-Alive aktiviert.");

          // Kurze Verzögerung vor der Registrierung
          this.log.info("Warte kurz vor dem Senden des Registrierungsbefehls...");
          setTimeout(async () => {
             try {
                 this.log.info("Sende Registrierungsbefehl nach Verzögerung.");
                 const registered = await this.client.commands.registerTerminal();
                 resolve(registered);
             } catch (error) {
                 this.log.error("Fehler bei verzögerter Terminal-Registrierung:", error.message);
                 resolve(false); // Bei Fehler während der Registrierung
             }
          }, 100); // 100ms Verzögerung
        });
        this.socket.on("data", (data) => {
          this.log.debug(`Empfangene Rohdaten: ${data.toString("hex")}`);
          this._handleData(data);
        });
        this.socket.on("error", (error) => {
          clearTimeout(connectTimeout);
          this.log.error("Socket-Fehler:", error.message);
          this.client.lastError = error.message;
          this.connected = false;
          resolve(false);
        });
        this.socket.on("close", () => {
          this.log.info("Socket geschlossen");
          this.connected = false;
        });
        // Stelle sicher, dass die IP-Adresse korrekt formatiert ist
        const ip = this.config.zvt_ip.trim();
        const port = parseInt(this.config.zvt_port, 10);

        this.log.info(`Verbinde mit Terminal: IP=${ip}, Port=${port}`);

        this.socket.connect({
          host: ip,
          port: port,
        });
      } catch (error) {
        this.log.error("Fehler bei Terminal-Verbindung:", error.message);
        this.client.lastError = error.message;
        resolve(false);
      }
    });
  }

  /**
   * Daten-Handler für Socket-Kommunikation
   * @param {Buffer} data Empfangene Daten
   * @private
   */
  _handleData(data) {
    // Vollständiges Logging für alle eingehenden Daten
    this.log.debug(
      `*** RECEIVED: ${data.length} Bytes: ${data
        .toString("hex")
        .toUpperCase()}`
    );

    // Spezielle Behandlung für Abbruch (061E)
    if (this.client.parser._isAbortCommand(data)) {
      return this.client.parser._handleAbortCommand(data);
    }

    // Sofortige Behandlung von Display-Nachrichten
    if (this.client.parser._isDisplayMessage(data)) {
      this._sendDisplayAcknowledgement();
    }

    // Autorisierten Kartenvorgang bestätigen
    if (this.client.parser._isAuthorizationMessage(data)) {
      this.client.parser._handleAuthorizationMessage(data);
    }

    // TLV-Daten extrahieren, wenn vorhanden
    if (this.client.parser._isTLVData(data)) {
      this.client.parser._extractTerminalDataFromTLV(data);
    }

    // Beleg-Nachricht (060F00) speziell behandeln
    if (this.client.parser._isReceiptMessage(data)) {
      return this.client.parser._handleReceiptMessage(data);
    }

    // Antwortdaten zusammenführen
    this.responseData = Buffer.concat([this.responseData, data]);
    const responseState = this.client.parser._checkResponseState(this.responseData);

    if (responseState.complete) {
      this._processCompleteResponse(responseState);
    } else {
      this.log.debug("Antwort noch nicht vollständig, warte auf weitere Daten");
    }
  }

  /**
 * Verarbeitet eine vollständige Antwort
 * @param {Object} responseState - Der aktuelle Antwortzustand
 * @private
 */
_processCompleteResponse(responseState) {
  this.log.info("Vollständige Antwort empfangen, Länge:", this.responseData.length);
  this.client._sendToWindow("zvt-payment-update", {
    status: "processing",
    message: "Antwort vom Terminal erhalten...",
    statusText: "Terminal-Antwort wird verarbeitet",
  });

  if (responseState.expectsMoreData) {
    this.log.info("Erwarte weitere Daten...");
    return;
  }

  // Belege extrahieren
  this.client.parser._extractReceipts(this.responseData);

  // Antwort parsen und senden
  const response = this.client.parser._parseResponse(this.responseData);
  
  // Prüfen, ob es sich um eine StatusInformation (0x040F) handelt
  // und ob wir während einer Zahlung sind - in diesem Fall nicht als final behandeln
  const isStatusInfo = this.client.parser._isStatusInformationMessage(this.responseData);
  const isPaymentInProgress = this.client.payment && this.client.payment.paymentInProgress;
  
  // Prüfen, ob es sich um eine finale Nachricht handelt (Completion 0x060F oder Abort 0x061E)
  const isCompletionMessage = this.responseData.length >= 2 && this.responseData[0] === 0x06 && this.responseData[1] === 0x0F;
  const isAbortMessage = this.responseData.length >= 2 && this.responseData[0] === 0x06 && this.responseData[1] === 0x1E;
  const isFinalMessage = isCompletionMessage || isAbortMessage;

  this.log.info(`Nachrichtentyp: ${isStatusInfo ? 'StatusInformation' : isFinalMessage ? 'Final Message' : 'Other'}, Zahlung läuft: ${isPaymentInProgress}`);
  
  // Bei StatusInformation während einer Zahlung nur UI updaten, nicht als final behandeln
  if (isStatusInfo && isPaymentInProgress && !isFinalMessage) {
    this.log.info("StatusInformation während Zahlung empfangen - warte auf finale Antwort");
    this.client._sendToWindow("zvt-payment-update", {
      status: "processing",
      message: "Zahlung wird verarbeitet...",
      statusText: "In Bearbeitung",
      intermediate: true
    });
    // Puffer zurücksetzen, aber Callback und Zahlung beibehalten
    this.responseData = Buffer.alloc(0);
    return;
  }
  
  // Auch ohne aktiven Callback erfolgreich verarbeiten
  if (response.success) {
    // Verarbeitung der Antwort (unabhängig vom Callback-Status)
    this._handleSuccessfulPayment(response);
  }
  else {
    this._handleParsedResponse(response);
  }
}

  /**
   * Verarbeitet eine geparste Antwort
   * @param {Object} response - Die geparste Antwort
   * @private
   */
  _handleParsedResponse(response) {
    response.customerReceipt = this.client.receipt.receiptBuffer.customer.length > 0
        ? this.client.receipt.receiptBuffer.customer
        : null;
    response.merchantReceipt = this.client.receipt.receiptBuffer.merchant.length > 0
        ? this.client.receipt.receiptBuffer.merchant
        : null;

    this.log.info("Antwort erfolgreich geparst:", {
      success: response.success,
      statusCode: response.statusCode,
      statusMessage: this.client.constants.statusCodes[response.statusCode] || "Unbekannt",
      hasCustomerReceipt: response.customerReceipt !== null,
      hasMerchantReceipt: response.merchantReceipt !== null,
    });
    
    // Prüfen, ob es sich um eine StatusInformation (0x040F) während einer Zahlung handelt
    const isStatusInfo = this.responseData.length >= 2 && this.responseData[0] === 0x04 && this.responseData[1] === 0x0F;
    const isPaymentInProgress = this.client.payment && this.client.payment.paymentInProgress;
    
    // Prüfen, ob es sich um eine finale Nachricht handelt (Completion 0x060F oder Abort 0x061E)
    const isCompletionMessage = this.responseData.length >= 2 && this.responseData[0] === 0x06 && this.responseData[1] === 0x0F;
    const isAbortMessage = this.responseData.length >= 2 && this.responseData[0] === 0x06 && this.responseData[1] === 0x1E;
    const isFinalMessage = isCompletionMessage || isAbortMessage;
    
    // Log für bessere Nachvollziehbarkeit
    this.log.info(
      `Antwortstatus: ${response.success ? 'Erfolg' : 'Fehler'}, ` +
      `NachrichtenTyp: ${isStatusInfo ? 'StatusInfo' : isFinalMessage ? 'Final' : 'Other'}, ` +
      `Zahlung läuft: ${isPaymentInProgress}, ` +
      `StatusCode: ${response.statusCode}`
    );

    if (response.success) {
      this.client._sendToWindow("zvt-payment-update", {
        status: "success",
        message: "Zahlung erfolgreich!",
        statusText: "Erfolgreich",
        statusCode: response.statusCode,
        isFinal: isFinalMessage
      });
    } else {
      this.client._sendToWindow("zvt-payment-update", {
        status: "error",
        message: `Fehler: ${this.client.constants.statusCodes[response.statusCode] || "Unbekannter Fehler"}`,
        statusText: "Fehlgeschlagen",
        statusCode: response.statusCode,
        isFinal: isFinalMessage
      });
    }

    if (response.customerReceipt || response.merchantReceipt) {
      this.client.receipt.processPaymentReceipts(response)
        .then(() => {
          this.log.info("Belegverarbeitung abgeschlossen");
          if (response.success) {
            this.client._sendToWindow("zvt-payment-update", {
              status: "success",
              message: "Belege erfolgreich verarbeitet",
              statusText: "Abgeschlossen",
            });
          }
        })
        .catch((err) => {
          this.log.error("Fehler bei der Belegverarbeitung:", err.message);
          this.client._sendToWindow("zvt-payment-update", {
            status: response.success ? "success" : "error",
            message: "Fehler bei Belegverarbeitung: " + err.message,
            statusText: "Beleg-Fehler",
          });
        });
    } else {
      this.log.info("Keine Belege zur Verarbeitung vorhanden");
    }

    // Bei StatusInformation während einer Zahlung den Callback nicht auflösen, außer es ist eine finale Nachricht
    if (isPaymentInProgress && isStatusInfo && !isFinalMessage) {
      this.log.info("StatusInformation während Zahlung - Callback wird noch nicht ausgelöst, warte auf finale Antwort");
      
      // Puffer zurücksetzen, aber Callback und Zahlung beibehalten
      this.responseData = Buffer.alloc(0);
      this.currentReceipt = null;
      this.receiptComplete = false;
      return;
    }

    // Nur bei finalen Nachrichten oder wenn keine Zahlung läuft den Callback auflösen
    if (this.currentCallback && (!isPaymentInProgress || isFinalMessage)) {
      this.log.info(`Löse Callback auf: ${isFinalMessage ? 'Finale Nachricht' : 'Keine Zahlung läuft'}`);
      clearTimeout(this.timeout);
      const callback = this.currentCallback;
      this.currentCallback = null;
      this.busy = false;
      callback(null, response);
    }

    // Puffer zurücksetzen, aber Belege beibehalten
    this.responseData = Buffer.alloc(0);
    this.currentReceipt = null;
    this.receiptComplete = false;
  }

  /**
   * Verarbeitet eine erfolgreiche Zahlung, auch wenn der ursprüngliche Callback bereits abgelaufen ist
   * @param {Object} response - Die Erfolgsantwort
   * @private 
   */
  _handleSuccessfulPayment(response) {
    response.customerReceipt = this.client.receipt.receiptBuffer.customer.length > 0
      ? this.client.receipt.receiptBuffer.customer
      : null;
    response.merchantReceipt = this.client.receipt.receiptBuffer.merchant.length > 0
      ? this.client.receipt.receiptBuffer.merchant
      : null;

    this.log.info("Zahlung erfolgreich abgeschlossen, verarbeite Belege");
  
    // UI informieren und Zahlung als erfolgreich markieren
    this.client._sendToWindow("zvt-payment-update", {
      status: "success",
      message: "Zahlung erfolgreich!",
      statusText: "Erfolgreich",
      statusCode: response.statusCode,
    });
  
    // Zahlung freigeben
    this.busy = false;
  
    // Belege drucken, auch wenn kein Callback mehr vorhanden ist
    if (response.customerReceipt || response.merchantReceipt) {
      this.client.receipt.processPaymentReceipts(response)
        .then(() => {
          this.log.info("Belegverarbeitung abgeschlossen");
        })
        .catch((err) => {
          this.log.error("Fehler bei der Belegverarbeitung:", err.message);
        });
    }
  
    // Zahlung als erfolgreich an UI melden
    this.client._sendToWindow("zvt-payment-result", {
      success: true,
      amount: this.client.lastAmount,
      transactionId: this.client.lastTransactionId,
      statusCode: response.statusCode,
      statusMessage: this.client.constants.statusCodes[response.statusCode] || "Erfolg",
      hasCustomerReceipt: response.customerReceipt !== null,
      hasMerchantReceipt: response.merchantReceipt !== null,
    });
  
    // Ursprünglichen Callback auflösen, falls vorhanden
    if (this.currentCallback) {
      this._handleParsedResponse(response);
    }
  }

  /**
   * Sendet eine Bestätigung für Display-Nachrichten
   * @private
   */
  _sendDisplayAcknowledgement() {
    this.log.info("Display-Nachricht empfangen, sende sofortige Bestätigung");
    try {
      if (this.socket && this.connected) {
        const ackCommand = Buffer.from([0x80, 0x00, 0x00]);
        this.socket.write(ackCommand);
        this.log.info("Sofortige Bestätigung (80-00-00) für Display-Nachricht gesendet");
      }
    } catch (ackError) {
      this.log.error("Fehler beim Senden der sofortigen Bestätigung:", ackError.message);
    }
  }

  /**
 * Sendet ein Kommando an das Terminal und wartet auf Antwort
 * @param {Buffer} command Das zu sendende Kommando
 * @param {number} timeout Timeout in ms
 * @returns {Promise<Object>} Antwort vom Terminal
 */
async _sendCommand(command, timeout = 60000) {
  return new Promise((resolve, reject) => {
    if (!this.socket || !this.connected) {
      reject(new Error("Nicht mit Terminal verbunden"));
      return;
    }
    if (this.busy) {
      reject(new Error("Terminal ist beschäftigt"));
      return;
    }
    this.busy = true;
    this.resetResponseBuffer(); // Stellt sicher, dass der Puffer leer ist

    // Setze den Timeout für die Antwort
    this.timeout = setTimeout(() => {
      this.busy = false; // Terminal ist nicht mehr blockiert
      this.log.error("Timeout bei Terminal-Kommunikation nach", timeout, "ms");

      // Wenn eine Zahlung lief, diese als fehlgeschlagen markieren und UI informieren
      if (this.client.payment.paymentInProgress) {
        this.log.warn("Sende Abbruchbefehl nach Timeout");
        try {
          // Versuche einen Abbruchbefehl zu senden (könnte auch fehlschlagen)
          const abortCommands = this.client.commands.createAbortCommand();
          if (Array.isArray(abortCommands.commands)) {
            for (const cmd of abortCommands.commands) {
              this.socket.write(cmd);
              // Kurze Pause zwischen Befehlen
              setTimeout(() => {}, 100);
            }
          } else {
            this.socket.write(abortCommands); // Fallback, wenn es nur ein Befehl ist
          }
        } catch (e) {
          this.log.error("Fehler beim Senden des Abbruchbefehls:", e.message);
        }

        // Status explizit zurücksetzen
        this.client.payment.paymentInProgress = false;
        this.busy = false;

        // Auch alle anderen Timeouts löschen
        if (this.currentTimeoutId) {
          clearTimeout(this.currentTimeoutId);
          this.currentTimeoutId = null;
        }

        if (this.receiptProcessingTimeout) {
          clearTimeout(this.receiptProcessingTimeout);
          this.receiptProcessingTimeout = null;
        }

        // UI über Timeout informieren
        this.client._sendToWindow("zvt-payment-update", {
          status: "error",
          message: "Zahlung wegen Zeitüberschreitung abgebrochen",
          statusText: "Timeout",
        });

        // Fehlermeldung senden - KEINE automatische Wiederaufnahme
        this.client._sendToWindow("zvt-payment-result", {
          success: false,
          error: "Timeout bei Terminal-Kommunikation",
          amount: this.client.lastAmount,
          transactionId: this.client.lastTransactionId
        });

        // Transaktion in der Datenbank als fehlgeschlagen markieren
        try {
          if (global.tseClient && global.tseClient.revisionDb && this.client.lastTransactionId) {
            global.tseClient.revisionDb.updatePaymentTransaction(this.client.lastTransactionId, {
              status: 'FAILED',
              completed_at: Math.floor(Date.now() / 1000)
            }).then(() => {
              this.log.info(`Transaktion ${this.client.lastTransactionId} nach Timeout als FAILED markiert`);
            }).catch(dbError => {
              this.log.error(`Fehler beim Aktualisieren der Transaktion: ${dbError.message}`);
            });
          }
        } catch (dbAccessError) {
          this.log.error("Fehler beim Zugriff auf die Datenbank:", dbAccessError.message);
        }
      }

      this.currentCallback = null; // Keinen Callback mehr erwarten
      reject(new Error("Timeout bei Terminal-Kommunikation")); // Promise ablehnen
    }, timeout);

    // Callback-Funktion für die Antwort
    this.currentCallback = (error, response) => {
      this.busy = false; // Terminal ist wieder frei
      if (error) {
        reject(error); // Fehler weitergeben
      } else {
        resolve(response); // Erfolgreiche Antwort weitergeben
      }
    };

    // Sende das Kommando an das Terminal
    try {
      // Formatierte Ausgabe des gesendeten Kommandos für Debugging
      const hexDump = command
        .toString("hex")
        .match(/.{1,2}/g) // In Zweiergruppen aufteilen
        .join("-")       // Mit Bindestrichen verbinden
        .toUpperCase();  // In Großbuchstaben
      this.log.info("Sende Kommando: [" + hexDump + "]");

      // Kommando an den Socket senden
      this.socket.write(command);
    } catch (error) {
      // Fehler beim Senden
      clearTimeout(this.timeout); // Timeout löschen
      this.busy = false; // Terminal freigeben
      this.currentCallback = null; // Callback entfernen
      reject(error); // Fehler weitergeben
    }
  });
}

  /**
   * Abmeldung vom Terminal
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async disconnect() {
    if (!this.connected || !this.socket) {
      this.log.info("Terminal bereits abgemeldet");
      return true;
    }
    try {
      this.log.info("Melde vom Terminal ab");
      this.socket.removeAllListeners();
      this.socket.destroy();
      this.socket = null;
      this.connected = false;
      this.log.info("Terminal-Verbindung geschlossen");
      return true;
    } catch (error) {
      this.log.error("Fehler bei Terminal-Abmeldung:", error.message);
      this.client.lastError = error.message;
      return false;
    }
  }
}

module.exports = ZVTConnection;