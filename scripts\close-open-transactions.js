/**
 * Test-Script zum Schließen offener TSE-Transaktionen
 * 
 * Dieses Script:
 * 1. Ruft alle offenen Transaktionen von der TSE ab
 * 2. Schließt jede offene Transaktion mit 0-Euro finishTransaction
 * 
 * Verwendung:
 * node scripts/close-open-transactions.js [clientId]
 */

const http = require('http');

// Konfiguration
const TSE_SERVICE_HOST = 'localhost';
const TSE_SERVICE_PORT = 8765;
const DEFAULT_CLIENT_ID = 'K003'; // Standard Client ID aus den Logs

/**
 * HTTP-Request an den EasyTseService senden
 */
function sendRequest(endpoint, data) {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify(data);
        
        const options = {
            hostname: TSE_SERVICE_HOST,
            port: TSE_SERVICE_PORT,
            path: endpoint,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            
            res.on('data', (chunk) => {
                body += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(body);
                    if (res.statusCode === 200) {
                        resolve(response);
                    } else {
                        reject(new Error(`HTTP ${res.statusCode}: ${response.error || body}`));
                    }
                } catch (e) {
                    reject(new Error(`Fehler beim Parsen der Antwort: ${e.message}`));
                }
            });
        });

        req.on('error', (err) => {
            reject(new Error(`Request-Fehler: ${err.message}`));
        });

        req.write(postData);
        req.end();
    });
}

/**
 * Alle offenen Transaktionen abrufen
 */
async function getStartedTransactions(clientId) {
    console.log(`Rufe offene Transaktionen für Client ${clientId} ab...`);
    
    try {
        const response = await sendRequest('/getStartedTransactions', {
            clientId: clientId
        });
        
        if (response.success) {
            console.log(`✓ ${response.data.count} offene Transaktionen gefunden`);
            response.data.openTransactions.forEach((transactionNumber, index) => {
                console.log(`  ${index + 1}. Transaktion: ${transactionNumber}`);
            });
            return response.data.openTransactions;
        } else {
            throw new Error('Unerwartete Antwort vom Server');
        }
    } catch (error) {
        console.error(`✗ Fehler beim Abrufen der offenen Transaktionen: ${error.message}`);
        throw error;
    }
}

/**
 * Eine einzelne Transaktion mit 0-Euro schließen (wie die Kasse es macht)
 */
async function closeTransaction(clientId, transactionNumber) {
    console.log(`Schließe Transaktion ${transactionNumber} mit 0-Euro...`);
    
    try {
        // Exakt das gleiche Format wie in den Kassen-Logs
        const response = await sendRequest('/finishTransaction', {
            clientId: clientId,
            transactionNumber: transactionNumber,
            processData: "0.00_0.00_0.00_0.00_0.00^0.00:Bar",
            processType: 1,
            totalAmount: 0,
            taxSet: [
                { taxRate: 0, amount: 0, netAmount: 0 }
            ],
            payments: [
                { type: "bar", amount: 0, name: "Bargeld" }
            ]
        });
        
        if (response.success) {
            console.log(`✓ Transaktion ${transactionNumber} erfolgreich geschlossen`);
            return true;
        } else {
            throw new Error('Unerwartete Antwort vom Server');
        }
    } catch (error) {
        console.error(`✗ Fehler beim Schließen der Transaktion ${transactionNumber}: ${error.message}`);
        return false;
    }
}

/**
 * Hauptfunktion
 */
async function main() {
    const clientId = process.argv[2] || DEFAULT_CLIENT_ID;
    
    console.log('=== TSE Offene Transaktionen schließen ===');
    console.log(`Client ID: ${clientId}`);
    console.log(`TSE Service: http://${TSE_SERVICE_HOST}:${TSE_SERVICE_PORT}`);
    console.log('');

    try {
        // 1. Offene Transaktionen abrufen
        const openTransactions = await getStartedTransactions(clientId);
        
        if (openTransactions.length === 0) {
            console.log('✓ Keine offenen Transaktionen gefunden. Alles ist bereits geschlossen.');
            return;
        }

        console.log('');
        console.log('Beginne mit dem Schließen der offenen Transaktionen mit 0-Euro...');
        console.log('');

        // 2. Jede offene Transaktion schließen
        let successCount = 0;
        let failCount = 0;

        for (const transactionNumber of openTransactions) {
            const success = await closeTransaction(clientId, transactionNumber);
            if (success) {
                successCount++;
            } else {
                failCount++;
            }
            
            // Kurze Pause zwischen den Requests
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log('');
        console.log('=== Zusammenfassung ===');
        console.log(`Offene Transaktionen gefunden: ${openTransactions.length}`);
        console.log(`Erfolgreich geschlossen: ${successCount}`);
        console.log(`Fehler beim Schließen: ${failCount}`);

        if (failCount > 0) {
            console.log('');
            console.log('⚠️  Einige Transaktionen konnten nicht geschlossen werden.');
            console.log('Überprüfen Sie die Fehlerausgaben oben und versuchen Sie es erneut.');
            process.exit(1);
        } else {
            console.log('');
            console.log('✅ Alle offenen Transaktionen wurden erfolgreich geschlossen!');
        }

    } catch (error) {
        console.error('');
        console.error('❌ Kritischer Fehler beim Ausführen des Scripts:');
        console.error(error.message);
        console.error('');
        console.error('Mögliche Ursachen:');
        console.error('- EasyTseService ist nicht gestartet');
        console.error('- TSE ist nicht verbunden');
        console.error('- Netzwerkproblem');
        process.exit(1);
    }
}

// Script ausführen, wenn direkt aufgerufen
if (require.main === module) {
    main().catch(error => {
        console.error('Unbehandelter Fehler:', error);
        process.exit(1);
    });
}

module.exports = {
    getStartedTransactions,
    closeTransaction,
    sendRequest
};