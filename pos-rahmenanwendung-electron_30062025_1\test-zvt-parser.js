// enhanced-test-zvt-parser.js
const fs = require('fs');
const path = require('path');

// Echte Terminal-Daten aus dem Log-Fall
const REAL_TERMINAL_DATA = {
  // Erfolgreiche girocard-Transaktion ohne expliziten Auth-Code
  successfulGirocardWithoutAuthCode: {
    authResponse: Buffer.from('040F7B2700040000000017000B9094860C0941380D06030EEEEE17EEEE194022F1F0466631EEEEEEEEE0181F29555964638750358A058C002A4135453931303031303020202020208BF0F96769726F636172640006282F0C1F1001001F1101001F120102600C430AA00000035910100280014504221000001F1603002700', 'hex'),
    
    tlvReceipt: Buffer.from('06D3FF5502068202511F070101258202490901000700090100070009010007000901000717482D8E2D4E2D442D4C2D452D522D422D452D4C2D452D470901000700090100070009010007142020205A6F6F204B726566656C642067476D6248090100071520202055657264696E676572205374722E20333737090100071220202020203437383030204B726566656C64090100071120202020202030323135312F3935353230090100071220202020205669656C656E2044616E6B2021090100070009010007185465726D696E616C2D4944203A2020203535353936343633090100071854412D4E722039303934383620202020424E7220353033350901000700090100071220202020204B617274656E7A61686C756E670901000711202020202020206B6F6E74616B746C6F73090100071020202020202020206769726F636172640901000700090120070B20204555522031372C30300901000700090100071850414E20203436363633313638323130323433393031383109010007184B6172746520302067816C746967206269732031322F32370901000707454D562D4149440901000718202020204130303030303033353931303130303238303031090100071856552D4E72202020202020202020413545393130303130300901000718446174756D2030332E30362E32352030393A343120556872090100070009010007172A2A2A205A61686C756E67206572666F6C6774202A2A2A09010007000901000700090100071742495454452042454C454720415546424557414852454E09010007000901000700090100070009010007000901810700', 'hex'),
    
    finalSignal: Buffer.from('060F00', 'hex'),
    
    expectedTerminalId: '55596463',
    expectedTraceNumber: '909486',
    expectedCardType: 'girocard',
    expectedCardNumber: '0181'
  }
};

// Enhanced Mock Client mit realistischerem Verhalten
class EnhancedMockZVTClient {
  constructor() {
    this.connection = {
      responseData: Buffer.alloc(0),
      currentReceipt: null,
      receiptComplete: false,
      busy: false,
      timeout: null,
      currentTimeoutId: null,
      receiptProcessingTimeout: null,
      socket: {
        write: (data) => {
          console.log(`[SOCKET] Gesendet: ${data.toString('hex').toUpperCase()}`);
        }
      },
      connected: true
    };
    
    // Wichtig: Beginne mit leeren Werten wie in der realen Situation
    this.lastAuthCode = null;
    this.lastEmvData = null;
    this.lastTerminalId = null;
    this.lastTraceNumber = null;
    this.lastCardType = null;
    this.lastCardNumber = null;
    this.lastAmount = 1700;
    this.lastTransactionId = 'TEST_TXN_123';
    this.lastReceiptNumber = null;
    this.lastVUNumber = null;
    
    this.payment = { 
      paymentInProgress: true,
      terminalCooldownActive: false 
    };
    
    this.receipt = {
      lastReceipts: { customer: [], merchant: [] },
      receiptBuffer: { customer: [], merchant: [] },
      _createEnhancedReceipts: (isError, statusInfo) => {
        console.log(`[RECEIPT] Erstelle ${isError ? 'Fehlerbeleg' : 'Erfolgsbeleg'}`);
        // Simuliere die Beleg-Erstellung - korrigierte Referenz
        this.receipt.lastReceipts.customer = ['Muster-Kundenbeleg'];
        this.receipt.lastReceipts.merchant = ['Muster-Händlerbeleg'];
      },
      processPaymentReceipts: (data) => {
        console.log(`[RECEIPT] Verarbeite Belege`);
        return Promise.resolve({ success: true });
      }
    };
    
    this._sendToWindow = (event, data) => {
      console.log(`[EVENT] ${event}:`, JSON.stringify(data, null, 2));
      
      // Sammle Events für Testvalidierung
      if (!this.capturedEvents) this.capturedEvents = [];
      this.capturedEvents.push({ event, data });
    };
    
    this.log = new Logger();
    this.capturedEvents = [];
  }
  
  // Methode zum Zurücksetzen für neue Tests
  reset() {
    this.lastAuthCode = null;
    this.lastTerminalId = null;
    this.lastTraceNumber = null;
    this.lastCardType = null;
    this.lastCardNumber = null;
    this.lastReceiptNumber = null;
    this.lastVUNumber = null;
    this.lastEmvData = null;
    this.capturedEvents = [];
    this.connection.responseData = Buffer.alloc(0);
    this.connection.currentReceipt = null;
    this.connection.receiptComplete = false;
    this.receipt.lastReceipts = { customer: [], merchant: [] };
    this.receipt.receiptBuffer = { customer: [], merchant: [] };
  }
}

// Erweiterte Testfälle mit realen Daten
const enhancedTestCases = [
  {
    name: "Realer Fehlerfall: girocard ohne Autorisierungscode",
    description: "Simuliert den exakten Fall aus dem Log",
    setup: (client) => {
      // Beginne mit komplett leeren Daten wie im realen Fall
      client.reset();
      client.lastAmount = 1700;
      client.lastTransactionId = 'transaction_payment_8oXDvueu4rGZ2hDReZ_nN';
    },
    sequence: [
      {
        step: "Autorisierungsantwort",
        data: REAL_TERMINAL_DATA.successfulGirocardWithoutAuthCode.authResponse,
        expectExtracted: {
          terminalId: '55596463',
          traceNumber: '909486',
          cardType: 'girocard'
        }
      },
      {
        step: "TLV-Belegdaten",
        data: REAL_TERMINAL_DATA.successfulGirocardWithoutAuthCode.tlvReceipt,
        expectContains: "Zahlung erfolgt"
      },
      {
        step: "Finales Erfolgssignal",
        data: REAL_TERMINAL_DATA.successfulGirocardWithoutAuthCode.finalSignal,
        expectFinalResult: true
      }
    ]
  },
  {
    name: "Expliziter Abbruchfall",
    description: "Terminal sendet Abbruchsignal",
    setup: (client) => {
      client.reset();
    },
    sequence: [
      {
        step: "Abbruchsignal",
        data: Buffer.from('061E', 'hex'),
        expectFinalResult: false,
        expectStatusCode: '0C'
      }
    ]
  }
];

// Test-Runner mit detaillierter Validierung
async function runEnhancedTests() {
  console.log("=== Enhanced ZVT-Parser Tests ===\n");

  const ZVTParser = require('./zvt-parser');
  const ZVTConstants = require('./zvt-constants');
  
  let passedTests = 0;
  let totalTests = 0;

  for (const testCase of enhancedTestCases) {
    totalTests++;
    console.log(`\n--- Test: ${testCase.name} ---`);
    console.log(`Beschreibung: ${testCase.description}`);
    
    try {
      const client = new EnhancedMockZVTClient();
      const constants = new ZVTConstants();
      const parser = new ZVTParser(client, constants);
      
      testCase.setup(client);
      
      let testPassed = true;
      let lastResult = null;
      
      // Führe die Sequenz von Terminal-Nachrichten durch
      for (const sequenceStep of testCase.sequence) {
        console.log(`\n  Schritt: ${sequenceStep.step}`);
        console.log(`  Daten: ${sequenceStep.data.toString('hex').toUpperCase()}`);
        
        // Simuliere die Datenverarbeitung
        if (sequenceStep.step === "Autorisierungsantwort") {
          // Extrahiere Kartendaten und prüfe Ergebnis
          const extracted = parser._extractCardDataFromAuthorization(sequenceStep.data);
          
          if (sequenceStep.expectExtracted) {
            for (const [key, expectedValue] of Object.entries(sequenceStep.expectExtracted)) {
              const actualValue = client[`last${key.charAt(0).toUpperCase() + key.slice(1)}`];
              if (actualValue !== expectedValue) {
                console.error(`  ❌ ${key}: Erwartet '${expectedValue}', erhalten '${actualValue}'`);
                testPassed = false;
              } else {
                console.log(`  ✅ ${key}: ${actualValue}`);
              }
            }
          }
        } else if (sequenceStep.step === "Finales Erfolgssignal") {
          // Führe die kritische _handleReceiptMessage Funktion aus
          parser._handleReceiptMessage(sequenceStep.data);
          
          // Warte auf asynchrone Verarbeitung
          await new Promise(resolve => setTimeout(resolve, 600));
          
          // Validiere das Endergebnis
          const paymentResults = client.capturedEvents.filter(e => e.event === 'zvt-payment-result');
          if (paymentResults.length > 0) {
            lastResult = paymentResults[paymentResults.length - 1].data;
            
            if (sequenceStep.expectFinalResult !== undefined) {
              if (lastResult.success !== sequenceStep.expectFinalResult) {
                console.error(`  ❌ Endergebnis: Erwartet ${sequenceStep.expectFinalResult}, erhalten ${lastResult.success}`);
                console.error(`  Grund: ${lastResult.error || 'Kein Fehler angegeben'}`);
                testPassed = false;
              } else {
                console.log(`  ✅ Endergebnis korrekt: ${lastResult.success}`);
              }
            }
            
            if (sequenceStep.expectStatusCode) {
              if (lastResult.statusCode !== sequenceStep.expectStatusCode) {
                console.error(`  ❌ Statuscode: Erwartet '${sequenceStep.expectStatusCode}', erhalten '${lastResult.statusCode}'`);
                testPassed = false;
              } else {
                console.log(`  ✅ Statuscode korrekt: ${lastResult.statusCode}`);
              }
            }
          } else {
            console.error(`  ❌ Kein Payment-Result Event empfangen`);
            testPassed = false;
          }
        }
      }
      
      if (testPassed) {
        console.log(`\n✅ Test erfolgreich bestanden`);
        passedTests++;
      } else {
        console.log(`\n❌ Test fehlgeschlagen`);
        if (lastResult) {
          console.log(`Letztes Ergebnis:`, JSON.stringify(lastResult, null, 2));
        }
      }
      
    } catch (error) {
      console.error(`\n💥 Test-Exception: ${error.message}`);
      console.error(error.stack);
    }
    
    console.log(`${'='.repeat(50)}`);
  }
  
  console.log(`\n=== Test-Zusammenfassung ===`);
  console.log(`Bestanden: ${passedTests}/${totalTests}`);
  console.log(`Erfolgsrate: ${Math.round(passedTests/totalTests * 100)}%`);
  
  if (passedTests < totalTests) {
    console.log(`\n⚠️  ${totalTests - passedTests} Test(s) fehlgeschlagen - Code-Fixes erforderlich`);
    process.exit(1);
  } else {
    console.log(`\n🎉 Alle Tests bestanden!`);
  }
}

// Logger-Klasse (unverändert)
class Logger {
  constructor() {
    this.logs = [];
  }
  
  info(message) {
    console.log(`[INFO] ${message}`);
    this.logs.push({ level: 'info', message });
  }
  
  warn(message) {
    console.warn(`[WARN] ${message}`);
    this.logs.push({ level: 'warn', message });
  }
  
  error(message) {
    console.error(`[ERROR] ${message}`);
    this.logs.push({ level: 'error', message });
  }
  
  debug(message) {
    console.debug(`[DEBUG] ${message}`);
    this.logs.push({ level: 'debug', message });
  }
}

// Test ausführen
if (require.main === module) {
  runEnhancedTests().catch(console.error);
}

module.exports = { runEnhancedTests, enhancedTestCases, EnhancedMockZVTClient };