const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('passwordApi', {
  // Passwort zum Hauptprozess senden
  checkPassword: (password) => {
    ipcRenderer.send('check-password', password);
  },
  
  // Dialog abbrechen
  cancelDialog: () => {
    ipcRenderer.send('cancel-password-dialog');
  },
  
  // Fehlermeldung empfangen
  onPasswordError: (callback) => {
    ipcRenderer.on('password-error', (event, message) => callback(message));
  }
});