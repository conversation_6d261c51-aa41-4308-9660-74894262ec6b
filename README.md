**To-Do: Damit das Autoupdate funktioniert vor dem kompilieren einen Github [Personal Access Token gen](https://github.com/settings/tokens) anlegen.
         Und in der main.js Zeile 35 anpassen:**
          ```bash
          token: 'xxx_DF8pH0qZNqcGrzMocOXcnjDAc9rlIF2bZmkc'
          ```


-----------------------------------------------------------------------------------

## Inhaltsverzeichnis

- [Überblick](#überblick)
- [Systemübersicht und Architektur](#systemübersicht-und-architektur)
  - [Technologiestack und Abhängigkeiten](#technologiestack-und-abhängigkeiten)
  - [Modularer Aufbau](#modularer-aufbau)
- [Installation und Setup](#installation-und-setup)
- [Entwicklungsworkflow](#entwicklungsworkflow)
- [Module im Detail](#module-im-detail)
  - [MQTT-Client](#mqtt-client)
  - [Drucksystem](#drucksystem)
  - [TSE-Integration](#fiskalische-tse-integration)
  - [ZVT-Terminal](#zvt-terminal)
  - [Benutzeroberfläche](#benutzeroberfläche)
  - [Preload-Skript](#preload-skript)
  - [Hauptanwendungslogik](#hauptanwendungslogik)
- [Konfiguration und Anpassungen](#konfiguration-und-anpassungen)
- [Debugging und Fehlerbehebung](#debugging-und-fehlerbehebung)
- [Erweiterungsmöglichkeiten](#erweiterungsmöglichkeiten)

## Überblick

Der WizidPOS Receiver bietet folgende Kernfunktionen:

- **Echtzeit-Kommunikation:** Verbindung über MQTT (WebSockets) zur Integration mit externen Systemen.
- **Druckeransteuerung:** Direkter Druck von XML- und FGL-Daten an Epson-Drucker, alternativ über die Windows-Drucker-API.
- **Fiskalische TSE-Integration:** Unterstützung für fiskalische Transaktionen via Fiskaly und Epson TSE (über den EasyTseService).
- **Warenkorb-Verarbeitung:** Extraktion und Aufbereitung von Warenkorbdaten aus dem DOM des Webviews.
- **EC-Zahlungsintegration:** Anbindung an ZVT-Terminals für EC-Zahlungen.

## Systemübersicht und Architektur

### Technologiestack und Abhängigkeiten

- **Electron & Node.js:** Basis der Desktop-Anwendung.  
  Details findest du in der `package.json`.
- **MQTT:** Echtzeit-Kommunikation mit WebSocket-Unterstützung.
- **SQLite:** Lokale Speicherung fiskalischer Transaktionen.
- **HTTP/HTTPS:** Für Druckaufträge und API-Kommunikation.
- **C# (EasyTseService):** Lokaler HTTP-Service für Epson TSE-Funktionalitäten.

### Modularer Aufbau

Die Applikation ist in klar abgegrenzte Module unterteilt:

- **Logging:**  
  - `logger-service.js`  
    Zentraler Logger, der konfigurierbar ist und von allen Modulen verwendet wird.
  
- **MQTT-Kommunikation:**  
  - `mqtt-client.js`  
    Baut stabile WebSocket-Verbindungen zum MQTT-Broker auf, abonniert dynamisch konfigurierte Themen und verwaltet Wiederverbindungsversuche.
  
- **Konfiguration:**  
  - `config.json`  
    Zentrale Konfigurationsdatei mit API-Zugängen, Fenster-Einstellungen, Druckereinstellungen, TSE-Parametern, MQTT-Daten und mehr.
  - `config-editor.html`  
    Grafische Oberfläche zur Bearbeitung der Konfiguration.
  
- **Druck:**  
  - `epson-printer.js`  
    Steuert den Druck von XML- und FGL-Daten über HTTP-Aufrufe an Epson-Geräte.
  - `Direct_TicketPrinter.py`  
    Methode, um mittels der Windows-Drucker-API (win32print) im RAW-Modus zu drucken.
  
- **Warenkorbverarbeitung:**  
  - `cart-parser.js`  
    Extrahiert relevante Daten (Produktnamen, Mengen, Preise) aus dem DOM des Webviews.
  
- **Fiskalische TSE-Integration:**  
  - `fiskaly-client.js`  
    Kommuniziert mit der Fiskaly-TSE-API, übernimmt Authentifizierung, TSE-Initialisierung und speichert Transaktionen in einer lokalen SQLite-Datenbank.
  - `tse-database.js`  
    Zugriff auf die Sqlite TSE-Revisionsdatenbank.
  
- **Epson TSE-Integration:**  
  - `epson-tse-client.js`  
    Arbeitet mit dem EasyTseService (implementiert in C# – siehe `EasyTseService.cs`) und bietet Methoden zum Starten, Aktualisieren und Abschließen von Transaktionen.
  - `check-easytse.js`  
    Prüft, ob der EasyTseService vorhanden und korrekt kompiliert ist.
  
- **EC-Zahlungsintegration:**  
  - `zvt-client.js`  
    Verbindet sich über TCP mit EC-Terminals (ZVT), verarbeitet Zahlungs- und Belegdaten und übermittelt den Status an die Benutzeroberfläche.
  
- **Benutzeroberfläche:**  
  - `index.html`  
    Hauptfenster der App, in das ein Webview geladen wird, inklusive Lade- und Zahlungsmodal.
  - `customer-display.html`  
    Zeigt den aktuellen Warenkorb und die Gesamtsumme dem Kunden an.
  
- **Preload-Skript:**  
  - `preload.js`  
    Nutzt Electron's `contextBridge`, um dem Renderer sichere APIs (für Druck, MQTT-Events, Zahlungsfunktionen etc.) zur Verfügung zu stellen.
  
- **Hauptanwendungslogik:**  
  - `main.js`  
    Steuert den gesamten Anwendungsablauf, verwaltet Fenster, lädt Konfigurationen, prüft Updates und startet externe Prozesse wie den EasyTseService.

## Installation und Setup

### Systemvoraussetzungen

- **Node.js** (Version 14.x oder höher; LTS wird empfohlen)
- **npm** oder **yarn** zur Paketverwaltung
- Windows (für win32print und EasyTseService; ggf. Anpassungen für andere Plattformen notwendig)
- Visual Studio (zur Kompilierung des EasyTseService in C#)

### Repository klonen und Abhängigkeiten installieren

1. Repository klonen:
   ```bash
   git clone https://github.com/programmierbude/pos-rahmenanwendung-electron.git
   ```

2. Abhängigkeiten installieren:
   ```bash
   npm install
   ```

3. (Optional) EasyTseService kompilieren:
   - Öffne das Projekt im Ordner EasyTseService in Visual Studio.
   - Baue das Projekt im Release-Modus. Die ausführbare Datei sollte unter EasyTseService/bin/Release/net472/EasyTseService.exe verfügbar sein.
   - Alternativ prüfe mit dem Script check-easytse.js:
     ```bash
     node check-easytse.js
     ```

## Entwicklungsworkflow

### Starten der Anwendung

Im Entwicklungsmodus kannst du die App mit folgendem Befehl starten:

```bash
npm run debug
```
Dies startet Electron im Debug-Modus. Nutze die Entwicklertools (DevTools) für direktes Debugging.

### Auto-Updater und Deployment

Der Auto-Updater ist in main.js konfiguriert und nutzt GitHub als Provider.

Für den produktiven Build:

```bash
npm run build
```

Pushen eines Release für den Autoupdater:

```bash
npm run publish
```

Die Versionierung erfolgt über die package.json

### Logging

- Zentral gesteuert über den Logger in logger-service.js.
- Die Log-Konfiguration (z. B. Log-Level, Dateigrößen, Rotation) wird in config.json festgelegt.
- Im Entwicklungsmodus erscheinen die Log-Ausgaben in der Konsole.

## Module im Detail

### MQTT-Client

Funktion: Verbindet sich mit dem MQTT-Broker (URL aus config.json), abonniert Themen (definiert in api-config.json) und leitet Nachrichten an entsprechende Handler weiter.

Erweiterung: Ergänze neue Topics in api-config.json und registriere entsprechende Handler in mqtt-client.js.

### Drucksystem

#### Epson-Drucker:
Implementiert in epson-printer.js – nutzt HTTP-Aufrufe an Epson-Geräte. Die IP und der Port sind in config.json konfiguriert.

#### Direktdruck (RAW):
Direct_TicketPrinter.py verwendet die Windows-Drucker-API (win32print) als alternative Methode oder Fallback.

### TSE-Integration

#### Fiskaly-Client:
fiskaly-client.js übernimmt die Authentifizierung, TSE-Initialisierung und das Transaktionsmanagement. Transaktionen werden in einer SQLite-Datenbank (via tse-database.js) gespeichert.

#### Epson TSE-Client:
epson-tse-client.js arbeitet mit dem lokalen EasyTseService (C#-Service, siehe EasyTseService.cs) und bietet Methoden zum Starten, Aktualisieren und Abschließen von Transaktionen.

### ZVT-Terminal

#### ZVT-Client:
zvt-client.js stellt die TCP-Verbindung zu EC-Terminals her, verarbeitet Zahlungs- und Belegdaten und kommuniziert den Status an die UI.

### Benutzeroberfläche

#### Main Window:
index.html ist das Hauptfenster, das einen Webview lädt und Lade-/Zahlungsmodal einbindet.

#### Customer Display:
customer-display.html zeigt den aktuellen Warenkorb und die Gesamtsumme für den Kunden an.

#### Konfigurationseditor:
config-editor.html bietet ein Tab-basiertes Interface zur Anpassung aller relevanten Einstellungen.

### Preload-Skript

#### Sicherheit:
preload.js nutzt Electron's contextBridge, um dem Renderer sichere APIs (z. B. für Druckfunktionen, MQTT-Events, Zahlungsfunktionen) bereitzustellen.

### Hauptanwendungslogik

#### main.js:
Steuert den gesamten Applikationsablauf: Fenster-Erstellung, Konfigurationsmanagement, Update-Prüfungen und das Starten externer Prozesse (z. B. EasyTseService).

## Konfiguration und Anpassungen

### Zentrale Konfiguration:
Alle kritischen Einstellungen befinden sich in der config.json (API-Endpunkte, Auth-Keys, Fenster-Einstellungen, Druckereinstellungen, MQTT- und TSE-Parameter).

### API-Konfiguration:
api-config.json definiert die MQTT-Topics und weitere API-relevante Parameter.

## Debugging und Fehlerbehebung

### Logging:
Überprüfe die Log-Ausgaben, die zentral in logger-service.js erzeugt werden.

### MQTT-Verbindungen:
Stelle sicher, dass die Broker-URL in config.json und die abonnierten Topics in api-config.json korrekt sind.

### Druckprobleme:
Teste die Erreichbarkeit des Druckers (IP, Port) und nutze ggf. den Direktdruck als Fallback.

### TSE-Integration:
Prüfe den Authentifizierungs- und Transaktionsablauf in fiskaly-client.js und epson-tse-client.js. Achte darauf, dass der EasyTseService (EasyTseService.exe) lauffähig ist.

### UI-Probleme:
Nutze die Entwicklertools im Webview und kontrolliere die IPC-Kommunikation über preload.js.
