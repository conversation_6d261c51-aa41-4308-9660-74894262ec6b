<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self'">
  <title>EC-Belegdaten</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }

    h1, h2, h3 {
      color: #333;
    }

    h1 {
      border-bottom: 1px solid #ddd;
      padding-bottom: 10px;
      margin-bottom: 20px;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      border-radius: 5px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      padding: 20px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }

    input[type="text"],
    input[type="number"],
    select,
    textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
      font-family: monospace;
    }

    .checkbox-group {
      margin-top: 5px;
    }

    .checkbox-group label {
      font-weight: normal;
      display: inline;
      margin-left: 5px;
    }

    .button-group {
      margin-top: 20px;
      text-align: right;
    }

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-left: 10px;
      font-weight: bold;
    }

    .save-button {
      background-color: #4285f4;
      color: white;
    }

    .cancel-button {
      background-color: #f44336;
      color: white;
    }

    .alert {
      background-color: #fff3cd;
      color: #856404;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 20px;
    }

    .tooltip {
      position: relative;
      display: inline-block;
      margin-left: 5px;
      cursor: help;
    }

    .tooltip:after {
      content: "?";
      display: inline-block;
      width: 16px;
      height: 16px;
      background-color: #ccc;
      color: white;
      border-radius: 50%;
      text-align: center;
      font-size: 12px;
      line-height: 16px;
    }

    .tooltip .tooltip-text {
      visibility: hidden;
      width: 200px;
      background-color: #555;
      color: #fff;
      text-align: center;
      border-radius: 6px;
      padding: 5px;
      position: absolute;
      z-index: 1;
      bottom: 125%;
      left: 50%;
      margin-left: -100px;
      opacity: 0;
      transition: opacity 0.3s;
    }

    .tooltip:hover .tooltip-text {
      visibility: visible;
      opacity: 1;
    }

    .success-message {
      background-color: #d4edda;
      color: #155724;
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
      display: none;
    }

    .error-message {
      background-color: #f8d7da;
      color: #721c24;
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
      display: none;
    }

    small {
      display: block;
      margin-top: 5px;
      color: #666;
      font-style: italic;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>EC-Belegdaten</h1>

    <div class="alert">
      <strong>Hinweis:</strong> Die Verbindungsdaten für das Terminal werden ausschließlich über die API-Konfiguration gesteuert.
      Hier können Sie nur die Händlerdaten für die Belege konfigurieren.
    </div>

    <h2>Belegdaten</h2>

    <div class="form-group">
      <label for="zvt-merchant-name">Firmenname</label>
      <input type="text" id="zvt-merchant-name" placeholder="Meine Firma GmbH">
      <small>Name des Unternehmens, der auf den Belegen erscheint.</small>
    </div>

    <div class="form-group">
      <label for="zvt-merchant-address">Adresse</label>
      <input type="text" id="zvt-merchant-address" placeholder="Hauptstraße 123">
      <small>Straße und Hausnummer des Unternehmens.</small>
    </div>

    <div class="form-group">
      <label for="zvt-merchant-zip-city">PLZ Ort</label>
      <input type="text" id="zvt-merchant-zip-city" placeholder="12345 Musterstadt">
      <small>Postleitzahl und Ort des Unternehmens.</small>
    </div>

    <div class="form-group">
      <label for="zvt-merchant-line1">Zusatzzeile 1</label>
      <input type="text" id="zvt-merchant-line1" placeholder="Tel: 01234 / 56789">
      <small>Zusätzliche Informationen (z.B. Telefonnummer).</small>
    </div>

    <div class="form-group">
      <label for="zvt-merchant-line2">Zusatzzeile 2</label>
      <input type="text" id="zvt-merchant-line2" placeholder="Vielen Dank für Ihren Einkauf!">
      <small>Zusätzliche Informationen (z.B. Danksagung).</small>
    </div>

    <h2>Automatisierung</h2>
    <div class="form-group checkbox-group">
      <input type="checkbox" id="zvt-auto-day-end">
      <label for="zvt-auto-day-end">Autom. EC-Abschluss</label>
      <small>Automatisch einen EC-Abschluss durchführen, wenn ein Kassenabschluss empfangen wird.</small>
    </div>

    <div class="success-message" id="success-message"></div>
    <div class="error-message" id="error-message"></div>

    <div class="button-group">
      <button class="cancel-button" id="cancel-button">Abbrechen</button>
      <button class="save-button" id="save-button">Speichern</button>
    </div>
  </div>

  <script>
    // Globale Variablen
    let currentConfig = {};
    let originalConfig = {};

    // DOM-Elemente
    const saveButton = document.getElementById('save-button');
    const cancelButton = document.getElementById('cancel-button');
    const successMessage = document.getElementById('success-message');
    const errorMessage = document.getElementById('error-message');

    // Funktion zum Anzeigen von Erfolgsmeldungen
    function showSuccess(message) {
      successMessage.textContent = message || 'EC-Belegdaten erfolgreich gespeichert.';
      successMessage.style.display = 'block';
      setTimeout(() => {
        successMessage.style.display = 'none';
      }, 5000);
    }

    // Funktion zum Anzeigen von Fehlermeldungen
    function showError(message) {
      errorMessage.textContent = message;
      errorMessage.style.display = 'block';
      setTimeout(() => {
        errorMessage.style.display = 'none';
      }, 5000);
    }

    // Funktion zum Aktualisieren der Konfiguration aus dem Formular
    function updateConfigFromForm() {
      try {
        // ZVT-Einstellungen - nur Händlerdaten
        if (!currentConfig.zvt_config) {
          currentConfig.zvt_config = {};
        }

        // ZVT-Belegdaten
        currentConfig.zvt_config.merchant_name = document.getElementById('zvt-merchant-name').value;
        currentConfig.zvt_config.merchant_address = document.getElementById('zvt-merchant-address').value;
        currentConfig.zvt_config.merchant_zip_city = document.getElementById('zvt-merchant-zip-city').value;
        currentConfig.zvt_config.merchant_line1 = document.getElementById('zvt-merchant-line1').value;
        currentConfig.zvt_config.merchant_line2 = document.getElementById('zvt-merchant-line2').value;

        // ZVT-Automatisierung
        currentConfig.zvt_config.auto_day_end = document.getElementById('zvt-auto-day-end').checked;

        return true;
      } catch (error) {
        showError(`Fehler beim Aktualisieren der Konfiguration: ${error.message}`);
        return false;
      }
    }

    // Funktion zum Initialisieren der Formularwerte
    function initializeFormValues() {
      console.log('Initialisiere Formularwerte mit Konfiguration:', currentConfig);

      // ZVT-Einstellungen - nur Händlerdaten
      if (currentConfig.zvt_config) {
        console.log('ZVT-Konfiguration gefunden:', currentConfig.zvt_config);

        // ZVT-Belegdaten
        document.getElementById('zvt-merchant-name').value = currentConfig.zvt_config.merchant_name || '';
        document.getElementById('zvt-merchant-address').value = currentConfig.zvt_config.merchant_address || '';
        document.getElementById('zvt-merchant-zip-city').value = currentConfig.zvt_config.merchant_zip_city || '';
        document.getElementById('zvt-merchant-line1').value = currentConfig.zvt_config.merchant_line1 || '';
        document.getElementById('zvt-merchant-line2').value = currentConfig.zvt_config.merchant_line2 || '';

        // ZVT-Automatisierung
        document.getElementById('zvt-auto-day-end').checked = !!currentConfig.zvt_config.auto_day_end;
      } else {
        console.warn('Keine ZVT-Konfiguration gefunden');
        // Erstelle eine leere ZVT-Konfiguration, wenn keine vorhanden ist
        currentConfig.zvt_config = {
          merchant_name: '',
          merchant_address: '',
          merchant_zip_city: '',
          merchant_line1: '',
          merchant_line2: '',
          auto_day_end: false
        };
      }
    }

    // Event-Listener für Formularelemente
    const formElements = [
      'zvt-merchant-name',
      'zvt-merchant-address',
      'zvt-merchant-zip-city',
      'zvt-merchant-line1',
      'zvt-merchant-line2',
      'zvt-auto-day-end'
    ];

    formElements.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.addEventListener('change', () => {
          try {
            updateConfigFromForm();
          } catch (error) {
            showError(`Fehler beim Aktualisieren der Konfiguration: ${error.message}`);
          }
        });
      }
    });

    // Event-Listener für Speichern-Button
    saveButton.addEventListener('click', () => {
      if (updateConfigFromForm()) {
        window.ecReceiptApi.saveConfig(currentConfig);
      }
    });

    // Event-Listener für Abbrechen-Button
    cancelButton.addEventListener('click', () => {
      window.ecReceiptApi.closeEditor();
    });

    // Konfiguration vom Hauptprozess empfangen
    window.ecReceiptApi.onLoadConfig((config) => {
      console.log('Konfiguration vom Hauptprozess empfangen:', config);
      console.log('ZVT-Konfiguration:', config.zvt_config);
      currentConfig = JSON.parse(JSON.stringify(config));
      originalConfig = JSON.parse(JSON.stringify(config));
      initializeFormValues();
    });

    // Bestätigung nach dem Speichern
    window.ecReceiptApi.onConfigSaved(() => {
      // Aktualisiere die Original-Konfiguration
      originalConfig = JSON.parse(JSON.stringify(currentConfig));

      // Zeige Erfolgsmeldung
      showSuccess('EC-Belegdaten wurden erfolgreich gespeichert.');
    });
  </script>
</body>
</html>
