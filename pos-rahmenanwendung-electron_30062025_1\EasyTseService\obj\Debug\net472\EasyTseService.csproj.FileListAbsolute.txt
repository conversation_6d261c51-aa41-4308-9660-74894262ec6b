C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\bin\Debug\net472\EasyTseService.exe.config
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\bin\Debug\net472\EasyTseService.exe
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\bin\Debug\net472\EasyTseService.pdb
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\bin\Debug\net472\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\bin\Debug\net472\System.Buffers.dll
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\bin\Debug\net472\System.IO.Pipelines.dll
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\bin\Debug\net472\System.Memory.dll
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\bin\Debug\net472\System.Numerics.Vectors.dll
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\bin\Debug\net472\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\bin\Debug\net472\System.Text.Encodings.Web.dll
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\bin\Debug\net472\System.Text.Json.dll
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\bin\Debug\net472\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\bin\Debug\net472\System.ValueTuple.dll
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\obj\Debug\net472\EasyTseService.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\obj\Debug\net472\EasyTseService.csproj.SuggestedBindingRedirects.cache
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\obj\Debug\net472\EasyTseService.exe.config
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\obj\Debug\net472\EasyTseService.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\obj\Debug\net472\EasyTseService.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\obj\Debug\net472\EasyTseService.AssemblyInfo.cs
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\obj\Debug\net472\EasyTseService.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\obj\Debug\net472\EasyTseS.8F34BB43.Up2Date
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\obj\Debug\net472\EasyTseService.exe
C:\Users\<USER>\Documents\python\wpos12\wpos\EasyTseService\obj\Debug\net472\EasyTseService.pdb
c:\Users\<USER>\Documents\python\wpos14\easytseservice\bin\Debug\net472\EasyTseService.exe.config
c:\Users\<USER>\Documents\python\wpos14\easytseservice\bin\Debug\net472\EasyTseService.exe
c:\Users\<USER>\Documents\python\wpos14\easytseservice\bin\Debug\net472\EasyTseService.pdb
c:\Users\<USER>\Documents\python\wpos14\easytseservice\bin\Debug\net472\Microsoft.Bcl.AsyncInterfaces.dll
c:\Users\<USER>\Documents\python\wpos14\easytseservice\bin\Debug\net472\System.Buffers.dll
c:\Users\<USER>\Documents\python\wpos14\easytseservice\bin\Debug\net472\System.IO.Pipelines.dll
c:\Users\<USER>\Documents\python\wpos14\easytseservice\bin\Debug\net472\System.Memory.dll
c:\Users\<USER>\Documents\python\wpos14\easytseservice\bin\Debug\net472\System.Numerics.Vectors.dll
c:\Users\<USER>\Documents\python\wpos14\easytseservice\bin\Debug\net472\System.Runtime.CompilerServices.Unsafe.dll
c:\Users\<USER>\Documents\python\wpos14\easytseservice\bin\Debug\net472\System.Text.Encodings.Web.dll
c:\Users\<USER>\Documents\python\wpos14\easytseservice\bin\Debug\net472\System.Text.Json.dll
c:\Users\<USER>\Documents\python\wpos14\easytseservice\bin\Debug\net472\System.Threading.Tasks.Extensions.dll
c:\Users\<USER>\Documents\python\wpos14\easytseservice\bin\Debug\net472\System.ValueTuple.dll
c:\Users\<USER>\Documents\python\wpos14\easytseservice\obj\Debug\net472\EasyTseService.csproj.AssemblyReference.cache
c:\Users\<USER>\Documents\python\wpos14\easytseservice\obj\Debug\net472\EasyTseService.csproj.SuggestedBindingRedirects.cache
c:\Users\<USER>\Documents\python\wpos14\easytseservice\obj\Debug\net472\EasyTseService.exe.config
c:\Users\<USER>\Documents\python\wpos14\easytseservice\obj\Debug\net472\EasyTseService.GeneratedMSBuildEditorConfig.editorconfig
c:\Users\<USER>\Documents\python\wpos14\easytseservice\obj\Debug\net472\EasyTseService.AssemblyInfoInputs.cache
c:\Users\<USER>\Documents\python\wpos14\easytseservice\obj\Debug\net472\EasyTseService.AssemblyInfo.cs
c:\Users\<USER>\Documents\python\wpos14\easytseservice\obj\Debug\net472\EasyTseService.csproj.CoreCompileInputs.cache
c:\Users\<USER>\Documents\python\wpos14\easytseservice\obj\Debug\net472\EasyTseS.8F34BB43.Up2Date
c:\Users\<USER>\Documents\python\wpos14\easytseservice\obj\Debug\net472\EasyTseService.exe
c:\Users\<USER>\Documents\python\wpos14\easytseservice\obj\Debug\net472\EasyTseService.pdb
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\bin\Debug\net472\EasyTseService.exe.config
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\bin\Debug\net472\EasyTseService.exe
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\bin\Debug\net472\EasyTseService.pdb
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\bin\Debug\net472\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\bin\Debug\net472\System.Buffers.dll
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\bin\Debug\net472\System.IO.Pipelines.dll
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\bin\Debug\net472\System.Memory.dll
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\bin\Debug\net472\System.Numerics.Vectors.dll
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\bin\Debug\net472\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\bin\Debug\net472\System.Text.Encodings.Web.dll
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\bin\Debug\net472\System.Text.Json.dll
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\bin\Debug\net472\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\bin\Debug\net472\System.ValueTuple.dll
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\obj\Debug\net472\EasyTseService.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\obj\Debug\net472\EasyTseService.csproj.SuggestedBindingRedirects.cache
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\obj\Debug\net472\EasyTseService.exe.config
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\obj\Debug\net472\EasyTseService.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\obj\Debug\net472\EasyTseService.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\obj\Debug\net472\EasyTseService.AssemblyInfo.cs
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\obj\Debug\net472\EasyTseService.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\obj\Debug\net472\EasyTseService.sourcelink.json
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\obj\Debug\net472\EasyTseS.8F34BB43.Up2Date
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\obj\Debug\net472\EasyTseService.exe
C:\Users\<USER>\Documents\GitHub\pos-rahmenanwendung-electron\easytseservice\obj\Debug\net472\EasyTseService.pdb
