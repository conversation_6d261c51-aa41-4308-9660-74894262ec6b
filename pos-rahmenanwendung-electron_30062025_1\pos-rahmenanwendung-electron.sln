Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EasyTseService", "EasyTseService\EasyTseService.csproj", "{140BABEF-6447-9E83-27C3-803A08612B08}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "node_modules", "node_modules", "{4F323C00-2DAB-898A-CB37-BBC8B5F74E19}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "edge-js", "edge-js", "{8D5A1301-092D-76F2-291D-6B9E7CFBF729}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "lib", "lib", "{1C2E1CE0-836B-E4B0-4BB2-354A9F1313B3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "bootstrap", "node_modules\edge-js\lib\bootstrap\bootstrap.csproj", "{9ACCA4A8-145D-DE1B-CC49-5719FF36AF79}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{A59FA145-055C-1F79-9657-A4602E9A475B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "double", "double", "{10D14742-3294-F4A9-E835-43FAEDBF84BD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Edge.js", "node_modules\edge-js\src\double\Edge.js\Edge.js.csproj", "{6A6C3825-C367-87C9-E107-CC87DB2760A3}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{140BABEF-6447-9E83-27C3-803A08612B08}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{140BABEF-6447-9E83-27C3-803A08612B08}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{140BABEF-6447-9E83-27C3-803A08612B08}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{140BABEF-6447-9E83-27C3-803A08612B08}.Release|Any CPU.Build.0 = Release|Any CPU
		{9ACCA4A8-145D-DE1B-CC49-5719FF36AF79}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9ACCA4A8-145D-DE1B-CC49-5719FF36AF79}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9ACCA4A8-145D-DE1B-CC49-5719FF36AF79}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9ACCA4A8-145D-DE1B-CC49-5719FF36AF79}.Release|Any CPU.Build.0 = Release|Any CPU
		{6A6C3825-C367-87C9-E107-CC87DB2760A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6A6C3825-C367-87C9-E107-CC87DB2760A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6A6C3825-C367-87C9-E107-CC87DB2760A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6A6C3825-C367-87C9-E107-CC87DB2760A3}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{8D5A1301-092D-76F2-291D-6B9E7CFBF729} = {4F323C00-2DAB-898A-CB37-BBC8B5F74E19}
		{1C2E1CE0-836B-E4B0-4BB2-354A9F1313B3} = {8D5A1301-092D-76F2-291D-6B9E7CFBF729}
		{9ACCA4A8-145D-DE1B-CC49-5719FF36AF79} = {1C2E1CE0-836B-E4B0-4BB2-354A9F1313B3}
		{A59FA145-055C-1F79-9657-A4602E9A475B} = {8D5A1301-092D-76F2-291D-6B9E7CFBF729}
		{10D14742-3294-F4A9-E835-43FAEDBF84BD} = {A59FA145-055C-1F79-9657-A4602E9A475B}
		{6A6C3825-C367-87C9-E107-CC87DB2760A3} = {10D14742-3294-F4A9-E835-43FAEDBF84BD}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {21EB5D78-E429-4EB7-AF32-5E81F360F79E}
	EndGlobalSection
EndGlobal
