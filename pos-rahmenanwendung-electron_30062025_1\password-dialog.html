<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Konfigurationszugriff</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      min-height: 100%;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
    }
    
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      box-sizing: border-box;
    }
    
    .container {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 30px;
      width: 320px;
      max-width: calc(100% - 40px);
      text-align: center;
    }
    
    h2 {
      margin-top: 0;
      margin-bottom: 20px;
      color: #333;
    }
    
    p {
      margin-bottom: 20px;
      color: #555;
    }
    
    input {
      width: 100%;
      padding: 12px;
      margin: 10px 0 20px 0;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
      font-size: 14px;
    }
    
    .buttons {
      display: flex;
      justify-content: space-between;
      margin-top: 25px;
    }
    
    button {
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
      transition: background-color 0.2s;
      min-width: 120px;
    }
    
    #submit-btn {
      background-color: #4CAF50;
      color: white;
    }
    
    #submit-btn:hover {
      background-color: #3e9142;
    }
    
    #cancel-btn {
      background-color: #f44336;
      color: white;
    }
    
    #cancel-btn:hover {
      background-color: #d32f2f;
    }
    
    .error-message {
      color: #f44336;
      margin-top: 15px;
      font-size: 14px;
      min-height: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>Konfigurationszugriff</h2>
    <form id="password-form">
      <input type="password" id="password-input" placeholder="Passwort eingeben" required>
      <div class="error-message" id="error-message"></div>
      <div class="buttons">
        <button type="button" id="cancel-btn">Abbrechen</button>
        <button type="submit" id="submit-btn">Bestätigen</button>
      </div>
    </form>
  </div>

  <script>
    document.getElementById('password-form').addEventListener('submit', (event) => {
      event.preventDefault();
      const password = document.getElementById('password-input').value;
      window.passwordApi.checkPassword(password);
    });

    document.getElementById('cancel-btn').addEventListener('click', () => {
      window.passwordApi.cancelDialog();
    });

    // Fehlermeldung anzeigen
    window.passwordApi.onPasswordError((message) => {
      document.getElementById('error-message').textContent = message;
      document.getElementById('password-input').value = '';
      document.getElementById('password-input').focus();
    });

    // Submit mit Enter-Taste
    document.getElementById('password-input').addEventListener('keypress', (event) => {
      if (event.key === 'Enter') {
        event.preventDefault();
        document.getElementById('submit-btn').click();
      }
    });

    // Automatisch Fokus auf das Passwortfeld setzen
    document.addEventListener('DOMContentLoaded', () => {
      document.getElementById('password-input').focus();
    });
  </script>
</body>
</html>
