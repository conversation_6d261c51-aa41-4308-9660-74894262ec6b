/**
 * ZVT-Hilfsfunktionen
 */
const loggerService = require("./logger-service");
const log = loggerService.getModuleLogger("ZVTUtils");

/**
 * Hilfsfunktionen für ZVT-Module
 */
class ZVTUtils {
  constructor() {
    this.log = log;
  }

  /**
   * Wandelt Bytes in lesbaren Text um mit verbesserter Zeichenkodierungsbehandlung
   * @param {Buffer} buffer Die zu konvertierenden Bytes
   * @returns {string} Der lesbare Text
   */
  bytesToAscii(buffer) {
    if (!buffer || buffer.length === 0) {
      return "";
    }

    // Versuche verschiedene Kodierungen
    let result;

    try {
      // Versuche zuerst Latin1 (ISO-8859-1), da dies häufig für deutsche Umlaute verwendet wird
      result = buffer.toString("latin1");

      // Prüfe, ob die Umlaute korrekt sind
      if (result.includes("�") ||
          result.includes("├") ||
          result.includes("╝") ||
          result.includes("ñ")) {
        // Wenn nicht, versuche UTF-8
        result = buffer.toString("utf8");
      }
    } catch (e) {
      // Fallback auf ASCII, wenn andere Kodierungen fehlschlagen
      result = "";
      for (let i = 0; i < buffer.length; i++) {
        // Nur druckbare ASCII-Zeichen anzeigen, Rest durch '.' ersetzen
        if (buffer[i] >= 0x20 && buffer[i] <= 0x7e) {
          result += String.fromCharCode(buffer[i]);
        } else {
          result += ".";
        }
      }
    }

    // Umlaute korrigieren
    return this.fixText(result);
  }

  /**
   * Korrigiert Zeichenkodierungsprobleme in Texten
   * @param {string} text Der zu korrigierende Text
   * @returns {string} Der korrigierte Text
   */
  fixText(text) {
    if (typeof text !== 'string') return '';

    // Umlaute-Ersetzungstabelle für verschiedene Kodierungsprobleme
    const replacements = [
      // UTF-8 falsch interpretiert
      { pattern: /├ñ/g, replacement: 'ä' },
      { pattern: /├Ñ/g, replacement: 'Ä' },
      { pattern: /├╝/g, replacement: 'ü' },
      { pattern: /├£/g, replacement: 'Ü' },
      { pattern: /├Â/g, replacement: 'ö' },
      { pattern: /├û/g, replacement: 'Ö' },
      { pattern: /├ƒ/g, replacement: 'ß' },

      // Weitere häufige Fehlkodierungen
      { pattern: /Ã¤/g, replacement: 'ä' },
      { pattern: /Ã„/g, replacement: 'Ä' },
      { pattern: /Ã¼/g, replacement: 'ü' },
      { pattern: /Ãœ/g, replacement: 'Ü' },
      { pattern: /Ã¶/g, replacement: 'ö' },
      { pattern: /Ã–/g, replacement: 'Ö' },
      { pattern: /ÃŸ/g, replacement: 'ß' },

      // Latin-1 falsch interpretiert
      { pattern: /ä/g, replacement: 'ä' },
      { pattern: /Ä/g, replacement: 'Ä' },
      { pattern: /ü/g, replacement: 'ü' },
      { pattern: /Ü/g, replacement: 'Ü' },
      { pattern: /ö/g, replacement: 'ö' },
      { pattern: /Ö/g, replacement: 'Ö' },
      { pattern: /ß/g, replacement: 'ß' },

      // Fragezeichen-Ersetzungen für häufige Probleme
      { pattern: /f\?r/g, replacement: 'für' },
      { pattern: /H\?ndler/g, replacement: 'Händler' },
      { pattern: /K\?ufer/g, replacement: 'Käufer' },
      { pattern: /Danke f\?r/g, replacement: 'Danke für' },
      { pattern: /Best\?tigung/g, replacement: 'Bestätigung' },

      // Euro-Symbol
      { pattern: /Ôé¼/g, replacement: '€' },
      { pattern: /â‚¬/g, replacement: '€' },

      // Fehlerhafte Satzzeichen
      { pattern: /Ôêé/g, replacement: '"' },
      { pattern: /Ôêô/g, replacement: '"' }
    ];

    // Alle Ersetzungen anwenden
    let result = text;
    for (const { pattern, replacement } of replacements) {
      result = result.replace(pattern, replacement);
    }

    // Allgemeine Bereinigung für nicht druckbare Zeichen
    return result.replace(/[^\x20-\x7E\xC0-\xFF]/g, ' ');
  }

  /**
   * Markiert eine Transaktion als abgebrochen in der Datenbank
   * @param {string} transactionId - Die Transaktions-ID
   * @param {string} status - Der Status (ABORTED, FAILED, etc.)
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async markTransactionStatus(transactionId, status = 'ABORTED') {
    if (!transactionId) {
      this.log.warn(`Keine Transaktions-ID verfügbar, kann Status "${status}" nicht in Datenbank erfassen`);
      return false;
    }

    try {
      // Globalen tseClient verwenden, wenn verfügbar
      if (global.tseClient && global.tseClient.revisionDb) {
        this.log.info(`Markiere Transaktion ${transactionId} als ${status} in Datenbank`);
        try {
          await global.tseClient.revisionDb.updatePaymentTransaction(transactionId, {
            status: status,
            completed_at: Math.floor(Date.now() / 1000)
          });
          this.log.info(`Transaktion ${transactionId} erfolgreich als ${status} markiert`);
          return true;
        } catch (dbError) {
          this.log.error(`Fehler beim Aktualisieren der Transaktion in der Datenbank: ${dbError.message}`);
          // Trotz Fehler true zurückgeben, damit der Zahlungsvorgang fortgesetzt werden kann
          return true;
        }
      } else {
        // Wenn TSE nicht verfügbar ist, trotzdem erfolgreich zurückgeben
        this.log.warn(`TSE-Client oder revisionDb nicht verfügbar, kann Transaktion ${transactionId} nicht als ${status} markieren`);
        // Trotzdem true zurückgeben, damit der Zahlungsvorgang fortgesetzt werden kann
        return true;
      }
    } catch (error) {
      this.log.error(`Fehler beim Markieren der Transaktion ${transactionId} als ${status}:`, error.message);
      // Trotz Fehler true zurückgeben, damit der Zahlungsvorgang fortgesetzt werden kann
      return true;
    }
  }

  /**
   * Behandelt Fehler bei der Terminal-Kommunikation einheitlich
   * @param {Object} client - ZVT-Client-Instanz
   * @param {Error} error - Der aufgetretene Fehler
   * @param {string} transactionId - Die Transaktions-ID
   * @param {number} amount - Der Betrag in Cent
   */
  handleTerminalError(client, error, transactionId, amount) {
    this.log.error("Fehler bei Terminal-Kommunikation:", error.message);

    // Status zurücksetzen
    if (client.connection) {
      client.connection.busy = false;

      // Timeouts löschen
      if (client.connection.timeout) {
        clearTimeout(client.connection.timeout);
        client.connection.timeout = null;
      }

      if (client.connection.currentTimeoutId) {
        clearTimeout(client.connection.currentTimeoutId);
        client.connection.currentTimeoutId = null;
      }

      if (client.connection.receiptProcessingTimeout) {
        clearTimeout(client.connection.receiptProcessingTimeout);
        client.connection.receiptProcessingTimeout = null;
      }
    }

    // UI über Fehler informieren
    client._sendToWindow("zvt-payment-update", {
      status: "error",
      message: `Fehler bei Terminal-Kommunikation: ${error.message}`,
      statusText: "Fehler"
    });

    client._sendToWindow("zvt-payment-result", {
      success: false,
      error: error.message,
      transactionId: transactionId,
      amount: amount
    });

    // Transaktion in der Datenbank als fehlgeschlagen markieren
    this.markTransactionStatus(transactionId, 'FAILED');

    // Fehler für weitere Verarbeitung zurückgeben
    return {
      success: false,
      error: error.message,
      transactionId: transactionId,
      amount: amount
    };
  }
}

// Singleton-Instanz exportieren
module.exports = new ZVTUtils();
