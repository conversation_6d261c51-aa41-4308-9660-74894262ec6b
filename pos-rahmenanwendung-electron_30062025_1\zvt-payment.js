/**
 * ZVT-Zahlungsabwicklung
 */
const net = require("net");
const loggerService = require("./logger-service");
const zvtUtils = require("./zvt-utils");

class ZVTPayment {
  constructor(client) {
    this.client = client;
    this.log = loggerService.getModuleLogger("ZVTPayment");
    this.paymentInProgress = false;
    // Neue Variablen für Cooldown-Phase
    this.terminalCooldownActive = false;
    this.terminalCooldownTimer = null;
  }

  /**
   * Zahlung mit dem Terminal durchführen
   * @param {number} amount Betrag in Cent
   * @param {string} transactionId ID der Transaktion
   * @returns {Promise<object>} Ergebnis der Zahlung
   */
  async processPayment(amount, transactionId) {
    // Prüfen, ob Cooldown nach Abbruch aktiv ist
    if (this.terminalCooldownActive) {
      this.log.warn("Terminal ist im Cooldown nach Abbruch, bitte warten Sie 10 Sekunden");

      // UI aktualisieren, um den Benutzer zu informieren
      this.client._sendToWindow("zvt-payment-update", {
        status: "error",
        message: "Terminal kurzzeitig nicht verfügbar. Bitte warten Sie 10 Sekunden nach Abbruch.",
        statusText: "Terminal im Cooldown",
      });

      return { success: false, error: "Terminal ist im Cooldown nach Abbruch, bitte warten Sie 10 Sekunden" };
    }

    if (this.client.connection.busy || this.paymentInProgress) {
      this.log.warn("Terminal ist beschäftigt, Zahlung wird abgelehnt");
      return { success: false, error: "Terminal ist beschäftigt" };
    }

    // Zusätzlicher Timeout für die Gesamttransaktion
    let transactionTimeoutId = null;
    this.client.lastTerminalId = null;
    this.client.lastTraceNumber = null;
    this.client.lastCardType = null;
    this.client.lastCardNumber = null;
    this.client.lastAuthCode = null;
    this.client.lastReceiptNumber = null;

    try {
      this.paymentInProgress = true;
      this.client.lastTransactionId = transactionId;
      this.client.lastAmount = amount;

      // Neuer Transaktions-Timeout, falls sich die Zahlung aufhängt
      const transactionTimeoutPromise = new Promise((_, reject) => {
        transactionTimeoutId = setTimeout(() => {
          if (this.paymentInProgress) {
            this.log.warn("Gesamttransaktions-Timeout nach 60 Sekunden ausgelöst");
            // Versuchen, die Transaktion abzubrechen
            this.abortPayment().catch((err) => {
              this.log.error("Fehler beim Abbrechen nach Timeout:", err.message);
            });
            reject(
              new Error(
                "Timeout: Die Zahlung wurde nach 180 Sekunden automatisch abgebrochen"
              )
            );
          }
        }, 180000); // 180 Sekunden Timeout
      });

      this.client._sendToWindow("zvt-payment-started", { amount, transactionId });

      if (!this.client.connection.connected) {
        this.client._sendToWindow("zvt-payment-update", {
          status: "processing",
          message: "Verbinde mit Terminal...",
          statusText: "Verbindungsaufbau",
        });

        const connectResult = await this.client.connect();
        if (!connectResult) {
          this.log.error(
            "Terminal nicht verbunden, Zahlung kann nicht durchgeführt werden"
          );
          this.client._sendToWindow("zvt-payment-update", {
            status: "error",
            message: "Terminal nicht verbunden",
            statusText: "Verbindungsfehler",
          });
          this.paymentInProgress = false;
          clearTimeout(transactionTimeoutId);
          return { success: false, error: "Terminal nicht verbunden" };
        }
      }

      this.log.info(
        `Starte Zahlung: ${amount} Cent für Transaktion ${transactionId}`
      );
      this.client._sendToWindow("zvt-payment-update", {
        status: "processing",
        message: "Bitte Karte an Terminal halten oder einstecken...",
        statusText: "Warte auf Kartenzahlung",
        amount,
        transactionId,
      });

      // Zahlungskommando erstellen
      const command = this.client.commands.createPaymentCommand(amount);

      this.log.info(
        "Autorisierungsbefehl gesendet - warte auf Benutzeraktionen am Terminal"
      );

      // Race zwischen normalem Zahlungsablauf und Timeout
      const response = await Promise.race([
        this.client.connection._sendCommand(command, 180000),
        transactionTimeoutPromise,
      ]);

      // Timeout aufheben, da Zahlung abgeschlossen oder abgebrochen
      if (transactionTimeoutId) {
        clearTimeout(transactionTimeoutId);
        transactionTimeoutId = null;
      }

      const paymentResult = {
        success: response.success,
        transactionId: transactionId,
        amount: amount,
        statusCode: response.statusCode,
        statusMessage: this.client.constants.statusCodes[response.statusCode] || "Unbekannt",
        // Explizitere Fehlerbehandlung mit Logging
        error:
          response.error ||
          this.client.constants.statusCodes[response.statusCode] ||
          `Fehler (Code: ${response.statusCode})`,
        rawData: response.rawData,
        cardType: response.cardType,
        cardNumber: response.cardNumber,
        expiryDate: response.expiryDate,
        authCode: response.authCode,
        zvtTransactionId: response.transactionId,
        customerReceipt:
          response.customerReceipt || this.client.receipt.receiptBuffer.customer.length > 0
            ? this.client.receipt.receiptBuffer.customer
            : null,
        merchantReceipt:
          response.merchantReceipt || this.client.receipt.receiptBuffer.merchant.length > 0
            ? this.client.receipt.receiptBuffer.merchant
            : null,
      };

      if (response.success) {
        this.log.info(
          `Payment error details: statusCode=${response.statusCode}, error="${paymentResult.error}"`
        );
        this.log.info("Zahlung erfolgreich abgeschlossen");
        this.client.receipt.lastReceipts = {
          customer: response.customerReceipt || this.client.receipt.receiptBuffer.customer,
          merchant: response.merchantReceipt || this.client.receipt.receiptBuffer.merchant,
        };

        this.log.info("Belege für manuellen Druck gespeichert");

        // Automatischen Händlerbelegdruck durchführen, wenn aktiviert
        if (this.client.autoMerchantReceipt === true &&
            this.client.receipt.lastReceipts.merchant !== null &&
            this.client.receipt.lastReceipts.merchant.length > 0) {
          this.log.info("Automatischer Händlerbelegdruck ist aktiviert, drucke Händlerbeleg");
          try {
            await this.client.receipt.printMerchantReceipt();
            this.log.info("Automatischer Händlerbelegdruck erfolgreich");
          } catch (printError) {
            this.log.error("Fehler beim automatischen Händlerbelegdruck:", printError.message);
          }
        } else {
          this.log.info(`Automatischer Händlerbelegdruck ist ${this.client.autoMerchantReceipt ? 'aktiviert, aber kein Beleg vorhanden' : 'deaktiviert'}`);
        }

        this.client._sendToWindow("zvt-payment-result", {
          success: response.success, // Hier den tatsächlichen Erfolgs-Status verwenden
          amount: amount,
          transactionId: transactionId,
          statusCode: response.statusCode,
          statusMessage: this.client.constants.statusCodes[response.statusCode] || "Unbekannt",
          // Fehlermeldung hinzufügen, wenn nicht erfolgreich
          error: !response.success
            ? response.error ||
              this.client.constants.statusCodes[response.statusCode] ||
              `Fehler (Code: ${response.statusCode})`
            : undefined,
          hasCustomerReceipt:
            this.client.receipt.lastReceipts.customer !== null &&
            this.client.receipt.lastReceipts.customer.length > 0,
          hasMerchantReceipt:
            this.client.receipt.lastReceipts.merchant !== null &&
            this.client.receipt.lastReceipts.merchant.length > 0,
        });
      } else {
        this.log.warn("Zahlung fehlgeschlagen, Status:", response.statusCode);
        
        // Fehlermeldung für MQTT ermitteln
        const errorMessage = this.client.constants.statusCodes[response.statusCode] || 
                           `Fehlercode: ${response.statusCode}`;
        
        // MQTT-Nachricht für fehlgeschlagene Zahlung senden
        this.client.receipt.sendFailedPaymentMqttMessage(
          transactionId, 
          this.client.lastTerminalId || 'unbekannt',
          `ZVT-Zahlungsfehler: ${errorMessage}`,
          amount ? (amount / 100).toFixed(2) : '0.01'
        );
        
        this.client._sendToWindow("zvt-payment-result", {
          success: false,
          error: errorMessage,
          amount: amount,
          transactionId: transactionId,
        });
        
        // Protokollieren des Zahlungsergebnisses für die Konsistenz mit erfolgreichen Zahlungen
        this.log.info("ZVT-Zahlungsergebnis: Fehlgeschlagen");
        this.log.warn("ZVT-Zahlungsfehler: " + errorMessage);
      }

      this.paymentInProgress = false;
      return paymentResult;
    } catch (error) {
      this.log.error("Fehler bei Zahlung:", error.message);
      this.client.lastError = error.message;
      this.paymentInProgress = false;

      // Timeout aufheben
      if (transactionTimeoutId) {
        clearTimeout(transactionTimeoutId);
      }

      // Verwende die gemeinsame Fehlerbehandlung
      return zvtUtils.handleTerminalError(this.client, error, transactionId, amount);
    }
  }

 /**
 * Bricht eine laufende Zahlung ab
 * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
 */
async abortPayment() {
  if (!this.paymentInProgress) {
    this.log.info("Keine aktive Zahlung zum Abbrechen");
    return true;
  }

  // Speichere die Transaktions-ID für spätere Verwendung
  const currentTransactionId = this.client.lastTransactionId;
  this.log.info(`Breche aktive Zahlung für Transaktion ${currentTransactionId} ab`);

  // Verhindere weitere Bearbeitung dieser Zahlung
  this.paymentInProgress = false;

  try {
    // Alle Timeouts löschen
    if (this.client.connection.timeout) {
      clearTimeout(this.client.connection.timeout);
      this.client.connection.timeout = null;
    }

    if (this.client.connection.currentTimeoutId) {
      clearTimeout(this.client.connection.currentTimeoutId);
      this.client.connection.currentTimeoutId = null;
    }

    if (this.client.connection.receiptProcessingTimeout) {
      clearTimeout(this.client.connection.receiptProcessingTimeout);
      this.client.connection.receiptProcessingTimeout = null;
    }

    if (this.client.connection.connected && this.client.connection.socket) {
      this.log.info("Sende Abbruchbefehl an Terminal");

      // Abbruchkommando erstellen
      const abortCommands = this.client.commands.createAbortCommand();

      // Flag für echte Abbruchbestätigung vom Terminal
      let receivedActualAbortCode = false;

      // Behalte die ursprüngliche Logik bei
      if (Array.isArray(abortCommands.commands)) {
        for (const cmd of abortCommands.commands) {
          try {
            const hexDump = cmd
              .toString("hex")
              .match(/.{1,2}/g)
              .join("-")
              .toUpperCase();
            this.log.info(`Sende Abbruchbefehl: [${hexDump}]`);
            this.client.connection.socket.write(cmd);

            // Kurze Pause zwischen den Befehlen
            await new Promise((resolve) => setTimeout(resolve, 200));
          } catch (cmdError) {
            this.log.warn(
              `Fehler beim Senden eines Abbruchbefehls: ${cmdError.message}`
            );
          }
        }
      } else {
        // Fallback für den Fall, dass keine commands-Array existiert
        const simpleAbort = Buffer.from([0x06, 0xb0, 0x00]);
        this.client.connection.socket.write(simpleAbort);
        this.log.info("Einfachen Abbruchbefehl gesendet: [06-B0-00]");
      }

      // Warten auf die tatsächliche Abbruchmeldung vom Terminal
      await new Promise((resolve) => {
        // Nicht versuchen, den vorhandenen Handler zu überschreiben
        // Stattdessen einen neuen Handler hinzufügen
        const abortHandler = (data) => {
          const hexData = data.toString('hex').toUpperCase();
          this.log.info(`Antwort vom Terminal beim Abbruch: ${hexData}`);

          // 800000 ist nur die Bestätigung, dass der Abbruchbefehl empfangen wurde
          if (hexData === '800000') {
            this.log.info("Bestätigung für Empfang des Abbruchbefehls erhalten - NICHT als Zahlungserfolg interpretieren");

            // UI über den Abbruchversuch informieren
            this.client._sendToWindow("zvt-payment-update", {
              status: "aborting",
              message: "Zahlung wird abgebrochen...",
              statusText: "Wird abgebrochen"
            });
          }

          // 061E... ist der tatsächliche Abbruchcode vom Terminal
          if (hexData.startsWith('061E')) {
            this.log.info("Tatsächliche Abbruchmeldung vom Terminal erhalten - ZAHLUNG WURDE ABGEBROCHEN");
            receivedActualAbortCode = true;

            // Abschlussantwort senden (84 66 00)
            try {
              const finalResponse = Buffer.from([0x84, 0x66, 0x00]);
              this.client.connection.socket.write(finalResponse);
              this.log.info("Abschlussantwort (84-66-00) gesendet");
            } catch (finalError) {
              this.log.warn(`Fehler beim Senden der Abschlussantwort: ${finalError.message}`);
            }

            // Markiere die Transaktion in der Datenbank als abgebrochen
            try {
              if (global.tseClient && global.tseClient.revisionDb && currentTransactionId) {
                global.tseClient.revisionDb.updatePaymentTransaction(
                  currentTransactionId,
                  {
                    status: 'ABORTED',
                    completed_at: Math.floor(Date.now() / 1000)
                  }
                ).then(() => {
                  this.log.info(`Transaktion ${currentTransactionId} als ABORTED markiert`);
                }).catch(dbError => {
                  this.log.error(`Fehler beim Aktualisieren der Transaktion: ${dbError.message}`);
                });
              } else {
                this.log.warn(`TSE-Client oder revisionDb nicht verfügbar, kann Transaktion ${currentTransactionId} nicht als abgebrochen markieren`);
              }
            } catch (dbError) {
              this.log.error(`Fehler beim Aktualisieren der Transaktion: ${dbError.message}`);
            }

            // UI über erfolgreichen Abbruch informieren
            this.client._sendToWindow("zvt-payment-update", {
              status: "aborted",
              message: "Zahlung wurde abgebrochen",
              statusText: "Abgebrochen"
            });

            // MQTT-Nachricht für abgebrochene Zahlung senden
            this.client.receipt.sendFailedPaymentMqttMessage(
              currentTransactionId,
              this.client.lastTerminalId || 'unbekannt',
              "ZVT-Zahlungsfehler: Transaktion abgebrochen durch Benutzer",
              this.client.lastAmount ? (this.client.lastAmount / 100).toFixed(2) : '0.01'
            );

            this.client._sendToWindow("zvt-payment-result", {
              success: false,
              aborted: true,
              error: "Zahlung vom Benutzer abgebrochen",
              statusCode: "0C",
              statusMessage: "Zahlung abgebrochen",
              transactionId: currentTransactionId,
              amount: this.client.lastAmount
            });
            
            // Protokollieren des Zahlungsergebnisses für die Konsistenz mit erfolgreichen Zahlungen
            this.log.info("ZVT-Zahlungsergebnis: Fehlgeschlagen");
            this.log.error("ZVT-Zahlungsfehler: Transaktion abgebrochen durch Benutzer");

            // Handler entfernen und Promise auflösen
            this.client.connection.socket.removeListener('data', abortHandler);
            resolve();
          }
        };

        // Handler für Daten vom Terminal
        this.client.connection.socket.on('data', abortHandler);

        // Timeout falls keine Abbruchmeldung kommt
        setTimeout(() => {
          this.client.connection.socket.removeListener('data', abortHandler);
          this.log.warn("Timeout beim Warten auf Abbruchmeldung vom Terminal");

          // Wenn keine tatsächliche Abbruchmeldung kam, Zahlung als fehlgeschlagen markieren
          if (!receivedActualAbortCode) {
            this.log.warn("Keine Abbruchmeldung vom Terminal erhalten - Status unklar");

            // Datenbank aktualisieren - Nur versuchen, wenn sie definitiv verfügbar ist
            if (global.tseClient && global.tseClient.revisionDb && currentTransactionId) {
              try {
                global.tseClient.revisionDb.updatePaymentTransaction(
                  currentTransactionId,
                  {
                    status: 'FAILED',
                    completed_at: Math.floor(Date.now() / 1000)
                  }
                ).then(() => {
                  this.log.info(`Transaktion ${currentTransactionId} als FAILED markiert (keine Abbruchmeldung erhalten)`);
                }).catch(dbError => {
                  this.log.error(`Fehler beim Aktualisieren der Transaktion: ${dbError.message}`);
                });
              } catch (dbError) {
                this.log.error(`Fehler beim Aktualisieren der Transaktion: ${dbError.message}`);
              }
            }

            // UI über unklaren Status informieren
            this.client._sendToWindow("zvt-payment-update", {
              status: "error",
              message: "Unklar, ob die Zahlung abgebrochen wurde. Bitte prüfen Sie das Terminal.",
              statusText: "Status unklar"
            });

            this.client._sendToWindow("zvt-payment-result", {
              success: false,
              error: "Unklar, ob die Zahlung abgebrochen wurde",
              statusCode: "FF",
              statusMessage: "Status unklar",
              transactionId: currentTransactionId,
              amount: this.client.lastAmount
            });
          }

          resolve();
        }, 5000); // 5 Sekunden warten
      });

      // Pause geben, damit der Abbruch verarbeitet werden kann
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Nach dem Abbruch die Terminal-Verbindung zurücksetzen
      try {
        this.log.info("Setze Terminal-Verbindung zurück");
        // Status explizit zurücksetzen
        this.client.connection.busy = false;

        // Neu verbinden, damit ein sauberer Zustand erreicht wird
        await this.client.disconnect();
        await this.client.connect();
      } catch (resetError) {
        this.log.warn(
          `Fehler beim Zurücksetzen der Terminal-Verbindung: ${resetError.message}`
        );
      }
    }

    // Status zurücksetzen
    this.client.lastError = "Zahlung wurde vom Benutzer abgebrochen";

    // Cooldown-Phase aktivieren
    this.terminalCooldownActive = true;

    // Bestehenden Timer löschen, falls vorhanden
    if (this.terminalCooldownTimer) {
      clearTimeout(this.terminalCooldownTimer);
    }

    // Neuen Timer starten
    this.terminalCooldownTimer = setTimeout(() => {
      this.terminalCooldownActive = false;
      this.log.info("Terminal-Cooldown beendet, Terminal ist wieder bereit für neue Zahlungen");

      // UI über Ende des Cooldowns informieren
      this.client._sendToWindow("zvt-payment-update", {
        status: "info",
        message: "Terminal ist wieder bereit für Zahlungen",
        statusText: "Terminal bereit"
      });
    }, 10000); // 10 Sekunden Cooldown

    this.log.info("Zahlung abgebrochen, Terminal-Cooldown für 10 Sekunden aktiviert");

    return true;
  } catch (error) {
    // Auch im Fehlerfall Status zurücksetzen
    this.client.connection.busy = false;

    if (this.client.connection.timeout) {
      clearTimeout(this.client.connection.timeout);
      this.client.connection.timeout = null;
    }

    if (this.client.connection.currentTimeoutId) {
      clearTimeout(this.client.connection.currentTimeoutId);
      this.client.connection.currentTimeoutId = null;
    }

    if (this.client.connection.receiptProcessingTimeout) {
      clearTimeout(this.client.connection.receiptProcessingTimeout);
      this.client.connection.receiptProcessingTimeout = null;
    }

    // UI über Fehler informieren
    this.client._sendToWindow("zvt-payment-update", {
      status: "error",
      message: `Fehler beim Abbrechen der Zahlung: ${error.message}`,
      statusText: "Fehler"
    });

    this.client._sendToWindow("zvt-payment-result", {
      success: false,
      error: `Fehler beim Abbrechen der Zahlung: ${error.message}`,
      transactionId: currentTransactionId,
      amount: this.client.lastAmount
    });

    // Cooldown auch im Fehlerfall aktivieren
    this.terminalCooldownActive = true;

    if (this.terminalCooldownTimer) {
      clearTimeout(this.terminalCooldownTimer);
    }

    this.terminalCooldownTimer = setTimeout(() => {
      this.terminalCooldownActive = false;
      this.log.info("Terminal-Cooldown beendet, Terminal ist wieder bereit für neue Zahlungen");

      // UI über Ende des Cooldowns informieren
      this.client._sendToWindow("zvt-payment-update", {
        status: "info",
        message: "Terminal ist wieder bereit für Zahlungen",
        statusText: "Terminal bereit"
      });
    }, 10000); // 10 Sekunden Cooldown

    this.log.error("Fehler beim Abbrechen der Zahlung:", error.message);
    return false;
  }
}

  /**
 * Führt einen Tagesabschluss am Terminal durch
 * @param {boolean} useDirect - Ob die direkte Methode verwendet werden soll
 * @returns {Promise<object>} Ergebnis des Tagesabschlusses
 */
async performEndOfDay(useDirect = false) {
  // Prüfen, ob Cooldown nach Abbruch aktiv ist
  if (this.terminalCooldownActive) {
    this.log.warn("Terminal ist im Cooldown nach Abbruch, bitte warten Sie 10 Sekunden");

    // UI aktualisieren, um den Benutzer zu informieren
    this.client._sendToWindow("zvt-payment-update", {
      status: "error",
      message: "Terminal kurzzeitig nicht verfügbar. Bitte warten Sie 10 Sekunden nach Abbruch.",
      statusText: "Terminal im Cooldown",
    });

    return { success: false, error: "Terminal ist im Cooldown nach Abbruch, bitte warten Sie 10 Sekunden" };
  }

  // Verwende standardmäßig die direkte Methode
  if (useDirect) {
    return this.performDirectEndOfDay();
  }

  // Legacy-Methode als Fallback
  if (this.client.connection.busy) {
    this.log.warn("Terminal ist beschäftigt, Tagesabschluss wird abgelehnt");
    return { success: false, error: "Terminal ist beschäftigt" };
  }

  try {
    if (!this.client.connection.connected) {
      const connectResult = await this.client.connect();
      if (!connectResult) {
        this.log.error(
          "Terminal nicht verbunden, Tagesabschluss kann nicht durchgeführt werden"
        );
        return { success: false, error: "Terminal nicht verbunden" };
      }
    }

    this.log.info("Starte Tagesabschluss");
    this.client._sendToWindow("zvt-payment-started", {
      transactionId: "day-end-" + new Date().getTime(),
      isEndOfDay: true,
    });

    this.client._sendToWindow("zvt-payment-update", {
      status: "processing",
      message: "Tagesabschluss wird durchgeführt...",
      statusText: "Tagesabschluss",
      isEndOfDay: true,
    });

    const command = this.client.commands.createEndOfDayCommand();
    const response = await this.client.connection._sendCommand(command, 60000);
    const endOfDayResult = {
      success: response.success,
      statusCode: response.statusCode,
      statusMessage: this.client.constants.statusCodes[response.statusCode] || "Unbekannt",
      rawData: response.rawData,
      customerReceipt: response.customerReceipt,
      merchantReceipt: response.merchantReceipt,
    };

    if (
      response.success &&
      (response.customerReceipt || response.merchantReceipt)
    ) {
      // Tagesabschlussbelege immer drucken, unabhängig von der auto_merchant_receipt Einstellung
      await this.client.receipt.processPaymentReceipts({
        ...response,
        // Für Tagesabschlüsse immer den Händlerbeleg drucken
        merchantReceipt: response.merchantReceipt
      });
    }

    if (response.success) {
      this.log.info("Tagesabschluss erfolgreich durchgeführt");
      this.client._sendToWindow("zvt-payment-update", {
        status: "success",
        message: "Tagesabschluss erfolgreich durchgeführt",
        statusText: "Abgeschlossen",
        isEndOfDay: true,
      });

      // NEU: Automatische Neuregistrierung nach erfolgreichem Tagesabschluss
      this.log.info("Starte Neuregistrierung nach Tagesabschluss");

      // Verbindung trennen
      await this.client.disconnect();

      // Kurze Pause einfügen
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Neu verbinden und registrieren
      const reconnectResult = await this.client.connect();

      if (reconnectResult) {
        this.log.info("Neuregistrierung nach Tagesabschluss erfolgreich");
      } else {
        this.log.warn("Neuregistrierung nach Tagesabschluss fehlgeschlagen");
      }
    } else {
      this.log.error(
        "Tagesabschluss fehlgeschlagen, Status:",
        response.statusCode
      );
      this.client._sendToWindow("zvt-payment-update", {
        status: "error",
        message: `Tagesabschluss fehlgeschlagen: ${
          this.client.constants.statusCodes[response.statusCode] || response.statusCode
        }`,
        statusText: "Fehlgeschlagen",
        isEndOfDay: true,
      });
    }

    // Sende das Ergebnis an alle Fenster
    this.client._sendToWindow("zvt-payment-result", {
      success: response.success,
      isEndOfDay: true,
      statusCode: response.statusCode,
      statusMessage: this.client.constants.statusCodes[response.statusCode] || "Unbekannt",
    });

    return endOfDayResult;
  } catch (error) {
    this.log.error("Fehler bei Tagesabschluss:", error.message);
    this.client.lastError = error.message;

    this.client._sendToWindow("zvt-payment-update", {
      status: "error",
      message: `Fehler bei Tagesabschluss: ${error.message}`,
      statusText: "Fehler",
      isEndOfDay: true,
    });

    return { success: false, error: error.message };
  }
}

  /**
 * Führt einen Tagesabschluss direkt am Terminal durch
 * Umgeht den ZVTService und nutzt direkte TCP-Kommunikation
 * @returns {Promise<object>} Ergebnis des Tagesabschlusses mit Beleg
 */
async performDirectEndOfDay() {
  // Prüfen, ob Cooldown nach Abbruch aktiv ist
  if (this.terminalCooldownActive) {
    this.log.warn("Terminal ist im Cooldown nach Abbruch, bitte warten Sie 10 Sekunden");

    // UI aktualisieren, um den Benutzer zu informieren
    this.client._sendToWindow("zvt-payment-update", {
      status: "error",
      message: "Terminal kurzzeitig nicht verfügbar. Bitte warten Sie 10 Sekunden nach Abbruch.",
      statusText: "Terminal im Cooldown",
    });

    return { success: false, error: "Terminal ist im Cooldown nach Abbruch, bitte warten Sie 10 Sekunden" };
  }

  try {
    if (this.client.connection.busy || this.paymentInProgress) {
      this.log.warn("Terminal ist beschäftigt, Tagesabschluss wird abgelehnt");
      return { success: false, error: "Terminal ist beschäftigt" };
    }

    this.client.connection.busy = true;

    // UI informieren
    this.client._sendToWindow("zvt-payment-started", {
      transactionId: "day-end-" + new Date().getTime(),
      isEndOfDay: true,
    });

    this.client._sendToWindow("zvt-payment-update", {
      status: "processing",
      message: "Tagesabschluss wird durchgeführt...",
      statusText: "Tagesabschluss",
      isEndOfDay: true,
    });

    return new Promise((resolve, reject) => {
      const socket = new net.Socket();
      let allReceivedData = Buffer.alloc(0);
      let receiptLines = [];

      const timeout = setTimeout(() => {
        this.log.error("Timeout bei Tagesabschluss-Verbindung");
        socket.destroy();
        this.client.connection.busy = false;
        reject(new Error("Timeout bei Tagesabschluss-Verbindung"));
      }, 120000);

      socket.connect(
        parseInt(this.client.zvtConfig.zvt_port, 10),
        this.client.zvtConfig.zvt_ip
      );

      socket.on('connect', () => {
        this.log.info("Verbindung zum Terminal hergestellt für Tagesabschluss");
        const endOfDayCmd = Buffer.from([
          0x06, 0x50, 0x03, 0x00, 0x00, 0x00
        ]);

        this.log.info("Sende Tagesabschluss-Befehl: " + Buffer.from(endOfDayCmd).toString('hex'));
        socket.write(endOfDayCmd);
      });

      socket.on('data', (data) => {
        const hexResponse = data.toString('hex');
        this.log.info("Antwort vom Terminal: " + hexResponse);

        // Alle empfangenen Daten sammeln für spätere Analyse
        allReceivedData = Buffer.concat([allReceivedData, data]);

        if (hexResponse === '800000') {
          this.log.info("Befehl erfolgreich gesendet");
        }
        else if (hexResponse === '849a00') {
          socket.end();
          clearTimeout(timeout);
          this.client.connection.busy = false;
          reject(new Error("Terminal meldet Fehler: 9A00"));
        }
        // Bei 06d3 Textdaten - Beleg im TLV-Format
        else if (hexResponse.startsWith('06d3')) {
          this.log.info(`Belegdaten im TLV-Format empfangen (Format: ${hexResponse.substring(0,6)}), extrahiere Text`);

          // Text aus dem TLV-Format extrahieren
          const textData = this._extractTextFromTLV(data);
          if (textData && textData.length > 0) {
            this.log.info(`Extrahierter Text aus TLV: ${textData.length} Zeichen`);

            // Text in Zeilen aufteilen und zum Beleg hinzufügen
            const lines = textData.split(/[\n\r]+/).map(line => line.trim()).filter(line => line.length > 0);
            this.log.info(`Extrahierte Zeilen: ${lines.length}`);
            receiptLines.push(...lines);
          }

          // Bestätigung senden
          const ack = Buffer.from([0x80, 0x00, 0x00]);
          socket.write(ack);
        }
        // Bei 040f StatusInformation-Nachricht
        else if (hexResponse.startsWith('040f')) {
          this.log.info("StatusInformation (040f) für Tagesabschluss empfangen");

          // Daten aus der StatusInformation extrahieren
          let statusInfo = null;
          if (typeof this.client.parser._parseStatusInformation === 'function') {
            statusInfo = this.client.parser._parseStatusInformation(data);
            this.log.info(`Extrahierte StatusInfo: ${JSON.stringify(statusInfo)}`);
          }

          // Bestätigung senden
          const ack = Buffer.from([0x80, 0x00, 0x00]);
          socket.write(ack);

          // StatusInfo für Beleg-Erstellung speichern
          if (statusInfo) {
            // Wichtige Werte in client speichern
            if (statusInfo.terminalId) this.client.lastTerminalId = statusInfo.terminalId;
            if (statusInfo.amount !== undefined) this.client.lastAmount = statusInfo.amount * 100;
            if (statusInfo.cardType) this.client.lastCardType = statusInfo.cardType;
          }
        }
        // Bei 040f oder 060f00 - Ende der Daten
        else if (hexResponse.startsWith('040f') || hexResponse.startsWith('060f00')) {
          this.log.info("Tagesabschluss erfolgreich abgeschlossen");

          // Extrahiere StatusInfo aus der Antwort
          let statusInfo = null;
          if (hexResponse.startsWith('040f') && typeof this.client.parser._parseStatusInformation === 'function') {
            statusInfo = this.client.parser._parseStatusInformation(data);
            this.log.info(`Extrahierte StatusInfo vor Beleggenerierung: ${JSON.stringify(statusInfo)}`);
          }

          // Wenn immer noch keine Belegzeilen extrahiert wurden, versuche es aus allen gesammelten Daten
          if (receiptLines.length === 0) {
            this.log.info("Versuche Belegdaten aus gesammelten Daten zu extrahieren");
            receiptLines = this._extractReceiptLines(allReceivedData);
          }

          // Überprüfe, ob Belege vorhanden sind
          if (receiptLines.length === 0) {
            this.log.warn("Keine Belegzeilen extrahiert, erstelle Standard-Tagesabschlussbeleg");

            // Wenn StatusInfo vorhanden ist, verwende diese für den Standardbeleg
            if (statusInfo) {
              this.log.info("Verwende extrahierte StatusInfo für Standardbeleg");
              this.client.receipt._createEnhancedReceipts(false, statusInfo);
            } else {
              this.client.receipt._createEnhancedReceipts(false);
            }

            // Belege aus dem receipt-Objekt holen
            receiptLines = this.client.receipt.lastReceipts.merchant || [];
          }

          this.log.info(`Tagesabschlussbeleg mit ${receiptLines.length} Zeilen`);

          // Belege speichern
          this.client.receipt.lastReceipts.merchant = [...receiptLines];
          this.client.receipt.receiptBuffer.merchant = [...receiptLines];

          socket.end();
          clearTimeout(timeout);
          this.client.connection.busy = false;

          // UI-Updates senden
          this.client._sendToWindow("zvt-payment-update", {
            status: "success",
            message: "Tagesabschluss erfolgreich durchgeführt",
            statusText: "Abgeschlossen",
            isEndOfDay: true,
            hasMerchantReceipt: true,
          });

          this.client._sendToWindow("zvt-payment-result", {
            success: true,
            isEndOfDay: true,
            statusCode: "00",
            statusMessage: "Tagesabschluss erfolgreich durchgeführt",
            hasMerchantReceipt: true,
          });

          // Beleg auf Epson drucken - immer drucken, unabhängig von der auto_merchant_receipt Einstellung,
          // da Tagesabschlussbelege immer gedruckt werden sollten
          this.client.receipt._printReceipt(receiptLines, "merchant").catch(error => {
            this.log.error("Fehler beim Drucken des Tagesabschlussbelegs:", error.message);
          });

          // Verbesserte Neuregistrierung nach Tagesabschluss
          (async () => {
            try {
              this.log.info("Starte Neuregistrierung nach direktem Tagesabschluss");

              // Längere Pause vor dem Trennen der Verbindung
              this.log.info("Warte 3 Sekunden vor dem Trennen der Verbindung...");
              await new Promise(resolve => setTimeout(resolve, 3000));

              // Bestehende Verbindung trennen
              this.log.info("Trenne bestehende Verbindung...");
              await this.client.disconnect();

              // Längere Pause nach dem Trennen der Verbindung
              this.log.info("Warte 5 Sekunden nach dem Trennen der Verbindung...");
              await new Promise(resolve => setTimeout(resolve, 5000));

              // Neu verbinden und registrieren
              this.log.info("Stelle neue Verbindung her...");
              const reconnectResult = await this.client.connect();

              if (reconnectResult) {
                this.log.info("Neuregistrierung nach direktem Tagesabschluss erfolgreich");
              } else {
                this.log.warn("Neuregistrierung nach direktem Tagesabschluss fehlgeschlagen");

                // Zweiter Versuch nach weiterer Verzögerung
                this.log.info("Warte weitere 5 Sekunden für zweiten Verbindungsversuch...");
                await new Promise(resolve => setTimeout(resolve, 5000));

                this.log.info("Zweiter Verbindungsversuch...");
                const secondAttempt = await this.client.connect();

                if (secondAttempt) {
                  this.log.info("Zweiter Verbindungsversuch erfolgreich");
                } else {
                  this.log.error("Auch zweiter Verbindungsversuch fehlgeschlagen");
                }
              }
            } catch (reconnectError) {
              this.log.error("Fehler bei Neuregistrierung nach Tagesabschluss:", reconnectError.message);

              // Versuche nach Fehler erneut zu verbinden
              try {
                this.log.info("Versuche nach Fehler erneut zu verbinden...");
                await new Promise(resolve => setTimeout(resolve, 5000));
                await this.client.connect();
              } catch (finalError) {
                this.log.error("Finaler Verbindungsfehler:", finalError.message);
              }
            }
          })();

          resolve({
            success: true,
            message: "Tagesabschluss erfolgreich durchgeführt",
            merchantReceipt: receiptLines
          });
        }
      });

      socket.on('close', () => {
        this.log.info("Verbindung zum Terminal geschlossen");
        clearTimeout(timeout);
        this.client.connection.busy = false;
      });

      socket.on('error', (err) => {
        this.log.error("Socket-Fehler:", err.message);
        clearTimeout(timeout);
        this.client.connection.busy = false;
        reject(new Error(`Verbindungsfehler: ${err.message}`));
      });
    });
  } catch (error) {
    this.log.error("Fehler bei direktem Tagesabschluss:", error.message);
    this.client.connection.busy = false;

    this.client._sendToWindow("zvt-payment-update", {
      status: "error",
      message: `Fehler bei Tagesabschluss: ${error.message}`,
      statusText: "Fehler",
      isEndOfDay: true,
    });

    return {
      success: false,
      error: error.message
    };
  }
}
  /**
   * Text aus TLV-Format extrahieren (06d3ff...)
   * @param {Buffer} data - Die TLV-Daten
   * @returns {string} Der extrahierte Text
   * @private
   */
  _extractTextFromTLV(data) {
    try {
      // Überspringe die ersten 3 Bytes (06d3xx)
      let pos = 3;
      let result = '';

      this.log.info(`Extrahiere Text aus TLV-Format: ${data.toString('hex').substring(0, 20)}...`);

      // Unterscheide zwischen CCV (06d3ff) und Verifone (06d3e3) Format
      const isCCV = data[0] === 0x06 && data[1] === 0xd3 && data[2] === 0xff;
      const isVerifone = data[0] === 0x06 && data[1] === 0xd3 && data[2] === 0xe3;

      this.log.info(`TLV-Format erkannt: ${isCCV ? 'CCV' : (isVerifone ? 'Verifone' : 'Unbekannt')}`);

      // Extrahiere Text aus dem TLV-Format
      while (pos < data.length) {
        const byteValue = data[pos];

        // Text-Marker erkennen (0x07 bei CCV, 0x09 bei Verifone, beide unterstützen)
        if (byteValue === 0x07 || byteValue === 0x09 || byteValue === 0x01) {
          pos++;
          // Extrahiere Text bis zum nächsten Marker oder Ende
          let lineText = '';
          while (pos < data.length &&
                 data[pos] !== 0x07 &&
                 data[pos] !== 0x09 &&
                 data[pos] !== 0x01 &&
                 data[pos] !== 0x0A) {
            // Nur druckbare ASCII-Zeichen berücksichtigen
            if (data[pos] >= 0x20 && data[pos] <= 0x7e) {
              lineText += String.fromCharCode(data[pos]);
            }
            pos++;
          }
          if (lineText.trim().length > 0) {
            result += lineText.trim() + '\n';
            this.log.debug(`Extrahierte Zeile: "${lineText.trim()}"`);
          }
        } else {
          // Bei anderen Bytes fortfahren
          pos++;
        }
      }

      return result;
    } catch (error) {
      this.log.error("Fehler bei TLV-Textextraktion:", error.message);
      return '';
    }
  }

  /**
   * Belegzeilen aus allen gesammelten Daten extrahieren
   * @param {Buffer} allData - Die gesammelten Daten
   * @returns {Array} Die extrahierten Belegzeilen
   * @private
   */
  _extractReceiptLines(allData) {
    try {
      // Aus Binary nach Text konvertieren
      const asciiText = this._extractAsciiFromBuffer(allData);

      // Typische Tagesabschluss-Schlüsselwörter
      const keywords = ['Tagesabschluss', 'Summen EUR', 'Summe:', 'Gebucht', 'TA-Nr'];

      // Text in Zeilen aufteilen
      const allLines = asciiText.split(/[\n\r]+/).map(line => line.trim()).filter(line => line.length > 0);

      // Prüfen, ob es sich um einen Tagesabschlussbeleg handelt
      const isDayEndReceipt = keywords.some(keyword =>
        allLines.some(line => line.includes(keyword))
      );

      if (isDayEndReceipt) {
        return allLines;
      }

      // Wenn keine typischen Schlüsselwörter gefunden wurden, leere Liste zurückgeben
      return [];
    } catch (error) {
      this.log.error("Fehler beim Extrahieren der Belegzeilen:", error.message);
      return [];
    }
  }

  /**
   * ASCII-Text aus Binärdaten extrahieren
   * @param {Buffer} buffer - Die Binärdaten
   * @returns {string} Der extrahierte ASCII-Text
   * @private
   */
  _extractAsciiFromBuffer(buffer) {
    let result = '';
    let inTextBlock = false;
    let textBlockStart = 0;

    // Suche nach Textblöcken
    for (let i = 0; i < buffer.length; i++) {
      const byte = buffer[i];

      // Druckbares ASCII-Zeichen gefunden
      if (byte >= 0x20 && byte <= 0x7e) {
        if (!inTextBlock) {
          inTextBlock = true;
          textBlockStart = i;
        }
      }
      // Zeilenumbruch oder Formularvorschub
      else if (byte === 0x0a || byte === 0x0d || byte === 0x0c) {
        if (inTextBlock) {
          // Textblock extrahieren und hinzufügen
          const textBlock = buffer.slice(textBlockStart, i).toString('ascii');
          result += textBlock + '\n';
          inTextBlock = false;
        } else {
          result += '\n';
        }
      }
      // BEL-Zeichen als Zeilenumbruch behandeln (in ZVT-Protokoll häufig verwendet)
      else if (byte === 0x07) {
        if (inTextBlock) {
          const textBlock = buffer.slice(textBlockStart, i).toString('ascii');
          result += textBlock + '\n';
          inTextBlock = false;
        } else {
          result += '\n';
        }
      }
      // Bei anderen nicht-druckbaren Zeichen
      else {
        if (inTextBlock) {
          const textBlock = buffer.slice(textBlockStart, i).toString('ascii');
          result += textBlock;
          inTextBlock = false;
        }
      }
    }

    // Letzten Textblock hinzufügen, falls vorhanden
    if (inTextBlock) {
      const textBlock = buffer.slice(textBlockStart).toString('ascii');
      result += textBlock;
    }

    return result;
  }

  /**
   * Standard-Tagesabschlussbeleg erstellen
   * @returns {Array} Standard-Tagesabschlussbeleg als Zeilen-Array
   * @private
   */
  _createDefaultDayEndReceipt() {
    const now = new Date();
    const dateStr = now.toLocaleDateString('de-DE');
    const timeStr = now.toLocaleTimeString('de-DE');

    return [
      "==========================================",
      "          TAGESABSCHLUSS",
      "==========================================",
      `Datum: ${dateStr}`,
      `Uhrzeit: ${timeStr}`,
      "",
      "Terminal-ID: " + (this.client.lastTerminalId || this.client.zvtConfig.client_id || "K001"),
      "",
      "Summen EUR",
      "---------------------",
      "Summe:               0,0X",
      "",
      "**        GEBUCHT        **",
      "==========================================",
    ];
  }

  /**
   * Verarbeitet MQTT-Nachrichten und startet ggf. eine Kartenzahlung
   * @param {object} mqttMessage Die MQTT-Nachricht
   * @returns {Promise<object|null>} Ergebnis der Zahlungsverarbeitung oder null
   */
  async handleMqttMessage(mqttMessage) {
    try {
      if (!mqttMessage || typeof mqttMessage !== "object") {
        return null;
      }
      const isPaymentCreated =
        (mqttMessage["detail-type"] === "payment.created" ||
          mqttMessage["detail-type"] === "TransactionPayment.created") &&
        (mqttMessage.params?.detail?.payment_method === "CARD" ||
          mqttMessage.params?.detail?.type?.toUpperCase() === "CARD");
      if (isPaymentCreated) {
        const transactionDetail = mqttMessage.params?.detail;
        const transactionId =
          transactionDetail?.id ||
          transactionDetail?.transaction_id ||
          (transactionDetail.transaction && transactionDetail.transaction.id);

        // Betrag extrahieren
        let amount =
          transactionDetail?.total ||
          transactionDetail?.given ||
          (transactionDetail.transaction &&
            transactionDetail.transaction.total_amount);

        if (!transactionId || !amount) {
          this.log.warn("Unvollständige Transaktionsdaten in MQTT-Nachricht:", {
            transactionId,
            amount,
          });
          return null;
        }

        // Betrag normalisieren: Wenn der Betrag ein String ist, in eine Zahl umwandeln
        if (typeof amount === "string") {
          amount = parseFloat(amount.replace(",", "."));
        }

        this.log.info(
          `Kartenzahlung erkannt: ${amount} Cent für Transaktion ${transactionId}`
        );
        return await this.processPayment(amount, transactionId);
      }
      return null;
    } catch (error) {
      this.log.error("Fehler bei MQTT-Nachrichtenverarbeitung:", error.message);
      return null;
    }
  }
}

module.exports = ZVTPayment;