{"name": "wizid<PERSON>s", "version": "1.7.1", "description": "wizidpos receiver", "main": "main.js", "scripts": {"start": "electron .", "prebuild": "node scripts/check-easytse.js", "build": "electron-builder", "debug": "electron . --inspect=5858 --enable-logging", "postinstall": "electron-builder install-app-deps", "publish": "electron-builder -p always"}, "author": "TicketPAY Europe GmbH", "license": "", "dependencies": {"axios": "^1.8.1", "basic-auth": "^2.0.1", "commander": "^13.1.0", "edge-js": "^23.1.5", "electron-log": "^5.1.1", "electron-updater": "^6.3.9", "express": "^5.1.0", "mqtt": "^5.3.5", "sqlite3": "^5.1.7", "uuid": "^11.1.0", "ws": "^8.18.0", "xhr2": "^0.2.1", "xmlbuilder": "^15.1.1", "xmldom": "^0.6.0"}, "devDependencies": {"electron": "^29.0.0", "electron-builder": "^24.9.0", "electron-rebuild": "^3.2.9"}, "build": {"appId": "com.example.wizidpos", "compression": "maximum", "asar": true, "asarUnpack": ["node_modules/sqlite3/**/*", "Direct_TicketPrinter.py"], "publish": [{"provider": "github", "owner": "programmierbude", "repo": "pos-rahmenanwendung-electron", "private": true, "releaseType": "release"}], "win": {"target": "nsis", "icon": "favicon.png", "publisherName": "TicketPAY Europe GmbH", "certificateFile": "cert/tp-wizidpos.pfx"}, "extraResources": [{"from": "./resources/Keyboard", "to": "Keyboard", "filter": ["**/*"]}, {"from": "resources/EasyTseService", "to": "EasyTseService", "filter": ["**/*"]}, {"from": "cert", "to": "cert", "filter": ["**/*"]}], "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "perMachine": true, "include": "installer.nsh", "artifactName": "${productName} Setup ${version}.exe", "deleteAppDataOnUninstall": false}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "directories": {"buildResources": "resources", "output": "dist"}, "releaseInfo": {"releaseNotes": "ZVT Bugfixes und Übermittlung der ZVT Daten an das MQTT Topic"}}}