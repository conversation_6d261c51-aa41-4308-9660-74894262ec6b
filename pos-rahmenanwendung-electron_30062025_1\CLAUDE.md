# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Application
- `npm start` - Start the Electron application
- `npm run debug` - Start with debugging enabled (inspector on port 5858)

### Building and Distribution
- `npm run prebuild` - Runs EasyTSE service check before building
- `npm run build` - Build the application using electron-builder
- `npm run publish` - Build and publish to GitHub releases (requires GitHub token)
- `npm run postinstall` - Install app dependencies after npm install

### EasyTSE Service Management
- `node scripts/check-easytse.js` - Check if EasyTSE service is properly compiled and available

## Architecture Overview

This is a Point of Sale (POS) framework built with Electron that integrates multiple payment and fiscal systems:

### Core Components
- **Main Process** (`main.js`) - Electron main process, window management, configuration loading, auto-updater
- **Renderer Process** (`index.html`, `renderer.js`) - Main UI with embedded webview
- **Preload Scripts** - Security bridge between main and renderer processes using `contextBridge`

### Key Integrations
1. **MQTT Communication** (`mqtt-client.js`) - Real-time messaging via WebSocket to MQTT broker
2. **ZVT Payment Terminal** (`zvt-*.js` files) - EC card payment processing via TCP
3. **TSE Fiscal Integration** - Two providers:
   - Fiskaly Cloud TSE (`fiskaly-client.js`)
   - Epson Hardware TSE (`epson-tse-client.js` + `EasyTseService` C# component)
4. **Printer Integration** (`epson-printer.js`, `Direct_TicketPrinter.py`) - Receipt printing via HTTP or Windows print API
5. **Cart Processing** (`cart-parser.js`) - DOM parsing of shopping cart data from webview

### Configuration System
- `config.json` - Main configuration file (window settings, API endpoints, device settings)
- `api-config.json` - MQTT topics, TSE settings, ZVT terminal configuration
- `config-editor.html` - GUI for configuration management

### Data Storage
- SQLite database for TSE transaction storage (`tse-database.js`)
- Logging system (`logger-service.js`) with configurable levels and rotation

## File Structure Patterns

### ZVT Payment System
- `zvt-client.js` - Main ZVT client and TCP connection management
- `zvt-connection.js` - Connection handling and reconnection logic
- `zvt-commands.js` - ZVT protocol command definitions
- `zvt-parser.js` - Message parsing and protocol handling
- `zvt-payment.js` - Payment transaction processing
- `zvt-receipt.js` - Receipt data handling
- `zvt-constants.js` - Protocol constants and definitions
- `zvt-utils.js` - Utility functions

### Preload Scripts (Security Boundaries)
- `preload.js` - Main preload script for primary window
- `modal-preload.js` - Payment modal security bridge
- `password-preload.js` - Password dialog bridge
- `change-password-preload.js` - Password change functionality
- `loading-preload.js` - Loading screen bridge
- `ec-receipt-preload.js` - EC receipt handling
- `monitoring-preload.js` - System monitoring bridge

## Important Technical Details

### Auto-Updater
- Uses GitHub releases as update source
- Requires personal access token in `main.js:37` (currently hardcoded - security concern)
- Configured for private repository updates

### EasyTSE Service
- C# Windows service for Epson TSE hardware integration  
- Located in `EasyTseService/` directory
- Must be compiled before building the main application
- Communicates via HTTP on localhost:8009

### Security Architecture
- All renderer-main communication goes through preload scripts using `contextBridge`
- No direct Node.js access from renderer processes
- Certificate-based code signing configured for Windows builds

### Platform Dependencies
- Windows-specific components (EasyTSE service, Direct printer access)
- Python script for raw printer access (`Direct_TicketPrinter.py`)
- Native modules: sqlite3, edge-js for .NET interop

## Development Workflow

### Before Building
1. Ensure EasyTSE service is compiled (check with `node scripts/check-easytse.js`)
2. Update GitHub token in main.js if needed for auto-updates
3. Verify all configurations in config.json and api-config.json

### Testing ZVT Integration
- Use `test-zvt-parser.js` for protocol testing
- Configure terminal IP/port in api-config.json
- Monitor logs via logger-service for connection issues

### TSE Development
- Fiskaly: Requires API credentials in config.json
- Epson: Requires EasyTSE service running and proper certificate setup
- Test transactions are stored in local SQLite database

### Debugging
- Use `npm run debug` for main process debugging
- Renderer debugging via DevTools in the application
- All modules use centralized logging system - check log levels in config.json

## Common Patterns

### Error Handling
- All modules use the centralized logger from `logger-service.js`
- Database operations include proper error handling and rollback
- Network operations (MQTT, ZVT, HTTP) have retry logic with exponential backoff

### Configuration Loading
- Configuration is loaded at startup and cached
- Changes require application restart unless specifically handled
- Validate configuration structure before using values

### IPC Communication
- Use contextBridge in preload scripts for secure communication
- All async operations should be properly handled with promises
- Event-driven architecture for real-time updates (MQTT, ZVT status)