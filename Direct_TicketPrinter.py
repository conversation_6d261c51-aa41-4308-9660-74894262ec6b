import tkinter as tk
from tkinter import ttk, messagebox
import win32print
import arg<PERSON><PERSON>

def print_raw_data(printer_name, raw_data):
    try:
        # Drucker öffnen
        hPrinter = win32print.OpenPrinter(printer_name)
        print(f"Drucker {printer_name} erfolgreich geöffnet.")
    except Exception as e:
        print(f"Fehler beim Öffnen des Druckers: {e}")
        return

    try:
        # Druckjob starten (im RAW-Modus)
        hJob = win32print.StartDocPrinter(hPrinter, 1, ("FGL Ticket", None, "RAW"))
        print("Druckjob erfolgreich gestartet.")
        try:
            # Druckseite starten und FGL-Code schreiben
            win32print.StartPagePrinter(hPrinter)
            print("Druckseite gestartet.")
            win32print.WritePrinter(hPrinter, raw_data.encode('ascii', 'ignore'))
            print("Daten erfolgreich an den Drucker gesendet.")
            win32print.EndPagePrinter(hPrinter)
            print("Druckseite beendet.")
        except Exception as e:
            print(f"<PERSON>hler beim Schreiben auf den Drucker: {e}")
        finally:
            # Druckjob beenden
            win32print.EndDocPrinter(hPrinter)
            print("Druckjob beendet.")
    except Exception as e:
        print(f"Fehler beim Starten des Druckjobs: {e}")
    finally:
        win32print.ClosePrinter(hPrinter)
        print("Drucker geschlossen.")

class FGLPrinterApp:
    def __init__(self, master, printer_name=None, raw_data=None):
        self.master = master
        self.master.title("FGL Drucker")
        self.master.geometry("600x400")
        self.master.withdraw()  # Versteckt die GUI
        
        # Drucker Auswahl
        self.drucker_var = tk.StringVar()
        self.drucker_liste = self.get_drucker_liste()
        if self.drucker_liste:
            self.drucker_var.set(printer_name if printer_name else self.drucker_liste[0])
        else:
            self.drucker_var.set("Kein Drucker verfügbar")

        drucker_label = tk.Label(master, text="Drucker auswählen:")
        drucker_label.pack(pady=5)

        self.drucker_menu = ttk.Combobox(master, textvariable=self.drucker_var, values=self.drucker_liste)
        self.drucker_menu.pack(pady=5)

        # FGL Textbox
        fgl_label = tk.Label(master, text="FGL Code:")
        fgl_label.pack(pady=5)

        self.fgl_text = tk.Text(master, height=10, width=50)
        self.fgl_text.pack(pady=10)
        if raw_data:
            self.fgl_text.insert(tk.END, raw_data)

        # Drucken Button
        drucken_button = tk.Button(master, text="Drucken", command=self.drucken)
        drucken_button.pack(pady=20)

        # Automatisch drucken, wenn Argumente übergeben wurden
        if printer_name and raw_data:
            self.master.withdraw()  # Versteckt die GUI
            self.master.after(100, self.drucken)

    def get_drucker_liste(self):
        # Gibt eine Liste von verfügbaren Druckern zurück
        return [printer[2] for printer in win32print.EnumPrinters(2)]

    def drucken(self):
        fgl_code = self.fgl_text.get("1.0", tk.END).strip()
        if not fgl_code:
            messagebox.showerror("Fehler", "FGL-Code darf nicht leer sein.")
            return

        drucker_name = self.drucker_var.get()
        if not drucker_name:
            messagebox.showerror("Fehler", "Bitte wählen Sie einen Drucker aus.")
            return

        # Füge Steuerbefehle für den Drucker hinzu, falls erforderlich
        fgl_code_with_commands = fgl_code

        # Fügt am Ende einen Druckbefehl hinzu, um den Druck zu beenden
        fgl_code_with_commands += "<p>"

        try:
            print_raw_data(drucker_name, fgl_code_with_commands)
            # messagebox.showinfo("Druck", f"FGL-Code wurde erfolgreich an den Drucker '{drucker_name}' gesendet.")
            self.master.after(100, self.master.destroy)  # Schließt die GUI nach 1 Sekunde
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Drucken: {str(e)}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Druckt FGL-Daten auf einem angegebenen Drucker.")
    parser.add_argument("--printer_name", type=str, help="Name des Druckers")
    parser.add_argument("--raw_data", type=str, help="Zu druckende FGL-Daten")

    args = parser.parse_args()

    root = tk.Tk()
    app = FGLPrinterApp(root, printer_name=args.printer_name, raw_data=args.raw_data)
    root.mainloop()