!macro customInstall
  ; Logdatei einrichten
  !define LogFile "$TEMP\wizidpos_installer.log"
  FileOpen $0 "${LogFile}" w
  FileWrite $0 "WizidPOS Installationsprotokoll - $(^Date) $(^Time)$\r$\n"
  FileWrite $0 "----------------------------------------$\r$\n$\r$\n"
  
  SetDetailsPrint both
  DetailPrint "Installiere erforderliche Abhängigkeiten..."
  
  ; Die Installer-Dateien werden von build/installers eingebunden
  SetOutPath "$INSTDIR\resources\installers"
  
  ; Installer kopieren (aus dem korrekten Verzeichnis)
  File /nonfatal "${BUILD_RESOURCES_DIR}\..\build\installers\dotnet-sdk-8.0.407-win-x64.exe"
  File /nonfatal "${BUILD_RESOURCES_DIR}\..\build\installers\EasyTSESetup.exe"
  File /nonfatal "${BUILD_RESOURCES_DIR}\..\build\installers\EpsonTSEDriverSetup_1.0.10-4738_64Bit.exe"  
  File /nonfatal "${BUILD_RESOURCES_DIR}\..\build\installers\python-3.12.8-amd64.exe"
  
  ; .NET SDK Installation - Prüfung
  DetailPrint "Prüfe .NET SDK 8.0.407 Installation..."
  ExecWait 'cmd.exe /c "dotnet --list-sdks | findstr 8.0.407"' $1
  
  ${If} $1 == 0
    DetailPrint ".NET SDK 8.0.407 ist bereits installiert."
    FileWrite $0 ".NET SDK 8.0.407 ist bereits installiert.$\r$\n"
  ${Else}
    DetailPrint "Installiere .NET SDK 8.0.407..."
    IfFileExists "$INSTDIR\resources\installers\dotnet-sdk-8.0.407-win-x64.exe" dotnet_install dotnet_skip
    dotnet_install:
      ExecWait '"$INSTDIR\resources\installers\dotnet-sdk-8.0.407-win-x64.exe" /install /quiet /norestart' $1
      DetailPrint ".NET SDK Installation abgeschlossen (Exit-Code: $1)"
      FileWrite $0 ".NET SDK Installation Ergebnis: $1$\r$\n"
      Goto dotnet_done
    dotnet_skip:
      DetailPrint ".NET SDK Installationsdatei nicht gefunden"
      FileWrite $0 ".NET SDK Installation übersprungen: Datei nicht gefunden$\r$\n"
    dotnet_done:
  ${EndIf}
  
  ; EasyTSE Installation - Prüfung
DetailPrint "Prüfe EasyTSE Installation..."
IfFileExists "C:\EasyTSE\EasyTSEGUI.exe" easytse_exists easytse_not_exists
easytse_exists:
  DetailPrint "EasyTSE ist bereits installiert."
  FileWrite $0 "EasyTSE ist bereits installiert.$\r$\n"
  Goto easytse_copy_dll
easytse_not_exists:
  DetailPrint "Installiere EasyTSE..."
  IfFileExists "$INSTDIR\resources\installers\EasyTSESetup.exe" easytse_install easytse_skip
  easytse_install:
    ExecWait '"$INSTDIR\resources\installers\EasyTSESetup.exe" /VERYSILENT /SUPPRESSMSGBOXES /NORESTART' $2
    DetailPrint "EasyTSE Installation abgeschlossen (Exit-Code: $2)"
    FileWrite $0 "EasyTSE Installation Ergebnis: $2$\r$\n"
    Goto easytse_copy_dll
  easytse_skip:
    DetailPrint "EasyTSE Installationsdatei nicht gefunden"
    FileWrite $0 "EasyTSE Installation übersprungen: Datei nicht gefunden$\r$\n"
    Goto easytse_done

easytse_copy_dll:
  DetailPrint "Kopiere easytse.dll nach C:\EasyTSE..."
  IfFileExists "$INSTDIR\resources\EasyTseService\easytse.dll" 0 +3
    CopyFiles "$INSTDIR\resources\EasyTseService\easytse.dll" "C:\EasyTSE"
    FileWrite $0 "easytse.dll wurde nach C:\EasyTSE kopiert.$\r$\n"
  IfFileExists "C:\EasyTSE\easytse.dll" 0 +2
    DetailPrint "easytse.dll wurde erfolgreich nach C:\EasyTSE kopiert."
  
easytse_done:
  
  ; EpsonTSEDriver Installation - Prüfung
  DetailPrint "Prüfe Epson TSE Treiber Installation..."
  ; Prüfen auf Registry-Schlüssel des Treibers
  ReadRegStr $R0 HKLM "SOFTWARE\EPSON\TseDriver" "InstallDir"
  ${If} $R0 != ""
    DetailPrint "Epson TSE Treiber ist bereits installiert."
    FileWrite $0 "Epson TSE Treiber ist bereits installiert.$\r$\n"
  ${Else}
    DetailPrint "Installiere Epson TSE Treiber..."
    IfFileExists "$INSTDIR\resources\installers\EpsonTSEDriverSetup_1.0.10-4738_64Bit.exe" epson_install epson_skip
    epson_install:
      ExecWait '"$INSTDIR\resources\installers\EpsonTSEDriverSetup_1.0.10-4738_64Bit.exe" /S' $3
      DetailPrint "Epson TSE Treiber Installation abgeschlossen (Exit-Code: $3)"
      FileWrite $0 "Epson TSE Treiber Installation Ergebnis: $3$\r$\n"
      Goto epson_done
    epson_skip:
      DetailPrint "Epson TSE Treiber Installationsdatei nicht gefunden"
      FileWrite $0 "Epson TSE Treiber Installation übersprungen: Datei nicht gefunden$\r$\n"
  ${EndIf}
  epson_done:
  
  ; Python Installation - Prüfung
  DetailPrint "Prüfe Python 3.12 Installation..."
  ExecWait 'cmd.exe /c "python --version | findstr 3.12"' $4
  ${If} $4 == 0
    DetailPrint "Python 3.12 ist bereits installiert."
    FileWrite $0 "Python 3.12 ist bereits installiert.$\r$\n"
    
    ; Prüfe pywin32 Installation (nur wenn Python bereits installiert ist)
    DetailPrint "Prüfe pywin32 Installation..."
    ExecWait 'cmd.exe /c "python -m pip list | findstr pywin32"' $5
    ${If} $5 == 0
      DetailPrint "pywin32 ist bereits installiert."
      FileWrite $0 "pywin32 ist bereits installiert.$\r$\n"
    ${Else}
      DetailPrint "Installiere pywin32 Python-Paket..."
      ExecWait 'cmd.exe /c "python -m pip install pywin32"' $5
      DetailPrint "pywin32 Installation abgeschlossen (Exit-Code: $5)"
      FileWrite $0 "pywin32 Installation Ergebnis: $5$\r$\n"
    ${EndIf}
  ${Else}
    DetailPrint "Installiere Python 3.12.8..."
    IfFileExists "$INSTDIR\resources\installers\python-3.12.8-amd64.exe" python_install python_skip
    python_install:
      MessageBox MB_ICONINFORMATION|MB_OK "Die Python-Installation wird jetzt gestartet. Bitte achten Sie darauf, die Option 'Add Python to PATH' zu aktivieren, wenn der Installer erscheint."
      ExecWait '"$INSTDIR\resources\installers\python-3.12.8-amd64.exe"' $4
      DetailPrint "Python Installation abgeschlossen (Exit-Code: $4)"
      FileWrite $0 "Python Installation Ergebnis: $4$\r$\n"
      
      ; pywin32 erst nach erfolgreicher Python-Installation installieren
      ${If} $4 == 0
        DetailPrint "Installiere pywin32 Python-Paket..."
        ExecWait 'cmd.exe /c "python -m pip install pywin32"' $5
        DetailPrint "pywin32 Installation abgeschlossen (Exit-Code: $5)"
        FileWrite $0 "pywin32 Installation Ergebnis: $5$\r$\n"
      ${EndIf}
      
      Goto python_done
    python_skip:
      DetailPrint "Python Installationsdatei nicht gefunden"
      FileWrite $0 "Python Installation übersprungen: Datei nicht gefunden$\r$\n"
  ${EndIf}
  python_done:
  
  ; Zertifikatsinstallation
  DetailPrint "Installiere Anwendungszertifikat für Auto-Updates..."
  SetOutPath "$INSTDIR\resources\cert"
  
  ; Zertifikatsdateien aus dem cert-Ordner kopieren
  File /nonfatal "${BUILD_RESOURCES_DIR}\..\cert\tp-wizidpos.cer"
  File /nonfatal "${BUILD_RESOURCES_DIR}\..\cert\addcert.bat"
  
  ; Prüfe, ob Zertifikatsdateien existieren
  IfFileExists "$INSTDIR\resources\cert\tp-wizidpos.cer" cert_install cert_skip
  cert_install:
    DetailPrint "Führe Zertifikatsinstallation mit Administratorrechten aus..."
    ; Batch-Skript mit Administratorrechten ausführen
    !define SHELLEXECUTEINFO_SIZE 60
    System::Call 'shell32::ShellExecute(i 0, t "runas", t "$INSTDIR\resources\cert\addcert.bat", t "", t "$INSTDIR\resources\cert", i 0) i .r0'
    
    ; Prüfen des Ergebnisses
    ${If} $0 > 32
      DetailPrint "Zertifikatsinstallation erfolgreich gestartet."
      FileWrite $0 "Zertifikatsinstallation erfolgreich gestartet.$\r$\n"
    ${Else}
      DetailPrint "Fehler beim Starten der Zertifikatsinstallation (Code: $0)"
      FileWrite $0 "Fehler beim Starten der Zertifikatsinstallation (Code: $0)$\r$\n"
    ${EndIf}
    
    ; Kurze Wartezeit einbauen, damit das Skript Zeit zum Ausführen hat
    Sleep 3000
    Goto cert_done
  cert_skip:
    DetailPrint "Zertifikatsdateien nicht gefunden"
    FileWrite $0 "Zertifikatsinstallation übersprungen: Dateien nicht gefunden$\r$\n"
  cert_done:
  
  ; Zusammenfassung und Log-Abschluss
  DetailPrint "Installation der Abhängigkeiten abgeschlossen"
  FileWrite $0 "$\r$\nInstallation abgeschlossen.$\r$\n"
  FileClose $0
!macroend