Willkommen

Top Next



EasyTSE

Ansteuerung der technischen Sicherheitseinrichtung von Epson® für Software-Entwickler!





* * *



Copyright © by <PERSON><PERSON><PERSON> und <PERSON>. All Rights Reserved.





Einleitung

Top Previous Next

Herzlichen willkommen und vielen Dank, dass Sie die EasyTSE Anwendungsklasse verwenden möchten.



Wie alle Kassensoftwareanbieter mussten auch <PERSON><PERSON> und ich unsere Programme an die Kassensicherungsverordnung (KassSichV) anpassen. Diese Verordnung zur Bestimmung der technischen Anforderungen an elektronische Aufzeichnungs- und Sicherungssysteme im Geschäftsverkehr erweitert die Grundsätze zur ordnungsmäßigen Führung und Aufbewahrung von Bü<PERSON>n, Aufzeichnungen und Unterlagen in elektronischer Form sowie zum Datenzugriff (GoBD) und ist Folge der Digitalisierung in Deutschland. Zur KassenSichV gehört die Pflicht zur Überlassung der Daten für Außenprüfung oder Kassennachschau über eine einheitliche Einbindungs- und Export-Schnittstelle sowie die DSFinV-K.



Die DSFinV-K ist die Beschreibung einer Schnittstelle für den Export von Daten aus elektronischen Aufzeichnungssystemen für die Datenträgerüberlassung im Rahmen von Außenprüfungen sowie Kassennachschauen. Sie soll eine einheitliche Strukturierung und Bezeichnung der Dateien und Datenfelder unabhängig von dem beim Unternehmen eingesetzten elektronischen Aufzeichnungssystem sicherstellen. Das Unternehmen hat die Daten gemäß den Konventionen der DSFinV-K auf einem geeigneten Datenträger zur Verfügung zu stellen.



Zur Erfüllung dieser Anforderungen wird eine technische Sicherheitseinrichtung, kurz TSE benötigt. Dieses, vom Bundesamt für Sicherheit in der Informationstechnik (BSI) zertifizierte Sicherheitsmodul, soll in elektronischen Registrierkassen der lückenlosen und unveränderbaren Aufzeichnung aller Kassenvorgänge dienen. Nach meinem Kenntnisstand bieten Hersteller wie Swissbit, Bundesdruckerei, Diebold Nixdorf, A-Trust und Epson solche TSEs an.



Wir haben uns für Epson entschieden, da wir bereits seit vielen Jahren Bondrucker von Epson in Einsatz haben und Epson uns als kompetenter Partner bekannt ist. Der Hauptgrund ist jedoch die Vielfalt die Epson den Anwendern zur Verfügung stellt. Es gibt eine USB-TSE Variante, Drucker mit eingebauter TSE und Drucker zum Nachrüsten einer TSE. Wahrscheinlich sind die Schnittstellen zu anderen Anbietern aber recht ähnlich aufgebaut.



Die vorliegende Klasse EasyTSE wurde vollständig mit Visual Foxpro 9 entwickelt. Wir haben versucht, Ihnen als Anwender das Leben so leicht wie möglich zu gestalten und haben die Klasse mit allen Funktionen versehen, die Sie zur Erfüllung der gesetzlichen Grundlagen benötigen. Für die TCP/IP-Verbindung wird die Chilkat-Bibliothek verwendet die diesem Projekt beiliegt, desweiteren verwenden wir Funktionen von Marco Plaza, die ein Json-Objekt in ein VFP-Objekt umwandeln können. Hier muss das Rad nicht neu erfunden werden! Generell sehen wir unsere Klasse für Sie als Hilfe, da nicht jeder Entwickler hauptberuflich damit sein Geld verdient sondern vielleicht nur seine eigene Hausanwendung damit erstellt hat. Insbesondere auch der Aspekt das nicht jedem bekannt ist, wie eine TCP/IP-Verbindung hergestellt wird oder Verschlüsselungen wie hash256 oder base64-Kodierungen zu erfolgen haben.



Wir bieten Ihnen vorgefertige Funktionen, die die Socket-Verbindung, das Öffnen der TSE und das Senden des Kommandos mit einem Funktionsaufruf ausführen. Alternativ können Sie die Funktionen auch einzeln nutzen um indivdiuellen Code schreiben zu können. Außerdem stehen Ihnen Stapelfunktionen zur Verfügung, mit denen mehrere hintereinander notwendige Kommandoaufrufe ebenfalls mit einem Funktionsaufruf gestartet und die einzelnen Rückgabergebnisse weiter verarbeitet werden können. Die Klasse bietet eine Vielzahl von Funktionen und Eigenschaften die in dieser Hilfe erläutert werden.



Feedback, Anregungen und Kritik sind jederzeit gerne willkommen.



Ihr Thomas Geißler + Reinhard Müller





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Information

Top Previous Next

Die EasyTSE Klasse benötigt diese Dateien:

EasyTSE.dll

© Reinhard Müller + Thomas Geißler



EasyTSE.tlb

© Reinhard Müller + Thomas Geißler



EasyTSE.chm

© Reinhard Müller + Thomas Geißler



VFP9R.dll

© Microsoft



VRP9DEU.dll

© Microsoft



MSVCRT71.dll

© Microsoft



GDIPLUS.dll

© Microsoft



MSVRCR71.dll

© Microsoft



ChilkatAx-9.5.0-win32.dll

© Chilkat Software



RQRCodeX.ocx (optional)

© Consert.Net



EpsonTSEDriverSetup_1.0.0.2.exe

© Epson





Optional kann die graphische Benutzeroberfläche zum Testen und Konfigurieren der TSE verwendet werden.

EasyTSEGUI.exe

© Reinhard Müller + Thomas Geißler





Als Setup incl. Registrierung der Klassen

Setup_EasyTSE.exe



Für VFP: Die Instanziierung der Klasse mit Intellisense:

PUBLIC goTSE AS "EasyTSE.EpsonTSE"

goTSE = CreateObject( "EasyTSE.EpsonTSE" )



Entwickelt und bereitgestellt von:

RM-Collection

Reinhard Müller

Schwaigerstr. 2

92421 Schwandorf

09431 - 5101641

<EMAIL>

www.WaWi1.de



SoftArt

Thomas Geißler

Carl-Zeiss-Str. 16

74321 Bietigheim-Bissingen

07142 - 21943

<EMAIL>

www.bonstar.de





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Die ersten Schritte

Top Previous Next

Die ersten Schritte

Nachfolgende Code-Beispiele können direkt in Visual Foxpro so ausgeführt werden. Weitere Beispiele für VB oder Alaska xBase sind denkbar.



Puk, AdminPin und TimeAdminPin können individuell vorab angepasst werden, nachträgliche Änderungen sind möglich.

IP, Port und SecretKey sind in der Klasse mit default-Werten vorbelegt und können bei Bedarf angepasst werden.

Diese Einstellungen unbedingt notieren und auf die Feldlängen achten!



Das Setup kann in der Developer-TSE mehrfach, in der Produktiv-TSE nur einmal ausgeführt werden.

Die wichtigsten Aufrufe der Klasse sind gelb unterlegt!



VFP-Code

Weitere Code-Beispiele z.Bsp. für Alaska XBase++ oder VBA/VB6, C#, Python, Conzept16, usw. auf Anfrage.



*----------------------------------------------------------------------------------------------------------------------------

* Beschreibung: EasyTSE Beispiele

* Autor.......: Thomas Geissler

* Datum.......: 04.06.2020

*----------------------------------------------------------------------------------------------------------------------------

* Bemerkungen.: Test der folgenden Funktionen:

* Stack_Setup, Stack_RegisterNewClient, Stack_DeRegisterClient, Stack_GetRegisteredClientList

* Stack_RunTSESelfTest, Stack_Updatetime, Stack_StartFinishTransaktion, TSEConnectOpenSend(),

* Stack_GetStartedTranscationList(), GetTSEClientList(), GetSNfromPublicKey() in GetStorageInfo,

* BuildQRCode(), Gastro_Stack_Start(), Gastro_Stack_Transaction(), Gastro_Stack_Finish()

* SetTempDir(), BuildQRBMP() benötigt RQRCodeX.OCX,

* Stack_ExportArchiveData(), Stack_ExportFilteredByTransactionNumber(),

* Stack_ExportFilteredByTransactionNumberByInterval(), Stack_ExportFilteredByPeriodOfTime()

*----------------------------------------------------------------------------------------------------------------------------

* Änderungen..: 15.09.2020 / TG

*----------------------------------------------------------------------------------------------------------------------------

* BugFixes....:

*----------------------------------------------------------------------------------------------------------------------------



*----------------------------------------------------

*--- EasyTSE initialisieren

*----------------------------------------------------

PUBLIC goTSE AS EasyTSE.EpsonTSE

LOCAL lnI

goTSE = CREATEOBJECT( "EasyTSE.EpsonTSE" )



*----------------------------------------------------

*--- Individuelle Werte setzen (für Setup und weitere Funktionen)

*----------------------------------------------------

goTSE.TSE_PUK = "123456"

goTSE.TSE_AdminPin = "12345"

goTSE.TSE_TimeAdminPin = "54321"



*----------------------------------------------------

*--- Weitere Werte setzen ( optional, da default )

*----------------------------------------------------

*goTSE.TSE_IP = "127.0.0.1" && "127.0.0.1" oder "localhost" | "192.168.nnn.nnn" für Drucker

goTSE.TSE_IP = "**************"

goTSE.TSE_Port = 8009 && default

goTSE.TSE_SecretKey = "EPSONKEY" && default

goTSE.nSingleOrServerTSE = 1 && default (Single)



goTSE.tse_ClientId = "Kasse"



lnDoThis = 0

IF lnDoThis = 1

*----------------------------------------------------

*--- EPSON SERVER

*--- Gewünschtes Device auswählen

*----------------------------------------------------

IF goTSE.nSingleOrServerTSE = 1

? "------------------------------------------------------"

? "----> LOCALHOST"

? "------------------------------------------------------"

goTSE.TSE_DeviceID = "local_TSE" && default

ELSE

? "------------------------------------------------------"

? "----> EPSON SERVER LISTE"

? "------------------------------------------------------"

? "Epson Server Liste :", IIF( goTSE.GetDeviceList() = 1, "OK", "Nicht OK" )

IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ELSE

FOR lnI = 1 TO GETWORDCOUNT( goTSE.cDeviceList, ";" )

? GETWORDNUM( goTSE.cDeviceList, lnI, ";" )

NEXT

ENDIF

ENDIF

ENDIF



lnDoThis = 0

IF lnDoThis = 1 OR VARTYPE( goTSE.oGetStorageInfo ) <> 'O'

*----------------------------------------------------

*--- GetStorageInfo

*--- Enthält wichtige Informationen über die TSE

*--- Auch wichtig für das Finanzamt!

*----------------------------------------------------

? "------------------------------------------------------"

? "----> GetStorageInfo"

? "------------------------------------------------------"

? "GetStorageInfo :", IIF( goTSE.TSEConnectOpenSend( "GetStorageInfo" ) = 1, "OK", "Nicht OK" ), goTSE.cErrorList

IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF

ENDIF



lnDoThis = 0

IF lnDoThis = 1

? "cdcId :", goTSE.oGetStorageInfo.Output.TSEInformation.cdcId

? "cdcHash :", goTSE.oGetStorageInfo.Output.TSEInformation.cdcHash

? "certificateExpirationDate :", goTSE.oGetStorageInfo.Output.TSEInformation.certificateExpirationDate

? "createdSignatures :", goTSE.oGetStorageInfo.Output.TSEInformation.createdSignatures

? "hardwareVersion :", goTSE.oGetStorageInfo.Output.TSEInformation.hardwareVersion

? "hasPassedSelfTest :", goTSE.oGetStorageInfo.Output.TSEInformation.hasPassedSelfTest

? "hasValidTime :", goTSE.oGetStorageInfo.Output.TSEInformation.hasValidTime

? "isExportEnabledIfCspTestFails :", goTSE.oGetStorageInfo.Output.TSEInformation.isExportEnabledIfCspTestFails

? "isTSEUnlocked :", goTSE.oGetStorageInfo.Output.TSEInformation.isTSEUnlocked

? "lastExportExecutedDate :", goTSE.oGetStorageInfo.Output.TSEInformation.lastExportExecutedDate

? "maxRegisteredClients :", goTSE.oGetStorageInfo.Output.TSEInformation.maxRegisteredClients

? "maxSignatures :", goTSE.oGetStorageInfo.Output.TSEInformation.maxSignatures

? "maxStartedTransactions :", goTSE.oGetStorageInfo.Output.TSEInformation.maxStartedTransactions

? "maxUpdateDelay :", goTSE.oGetStorageInfo.Output.TSEInformation.maxUpdateDelay

? "registeredClients :", goTSE.oGetStorageInfo.Output.TSEInformation.registeredClients

? "remainingSignatures :", goTSE.oGetStorageInfo.Output.TSEInformation.remainingSignatures

? "serialNumber :", goTSE.oGetStorageInfo.Output.TSEInformation.serialNumber

? "signatureAlgorithm :", goTSE.oGetStorageInfo.Output.TSEInformation.signatureAlgorithm

? "softwareVersion :", goTSE.oGetStorageInfo.Output.TSEInformation.softwareVersion

? "startedTransactions :", goTSE.oGetStorageInfo.Output.TSEInformation.startedTransactions

? "tarExportSize :", goTSE.oGetStorageInfo.Output.TSEInformation.tarExportSize

? "timeUntilNextSelfTest :", goTSE.oGetStorageInfo.Output.TSEInformation.timeUntilNextSelfTest

? "tseCapacity :", goTSE.oGetStorageInfo.Output.TSEInformation.tseCapacity

? "tseCurrentSize :", goTSE.oGetStorageInfo.Output.TSEInformation.tseCurrentSize

? "tseDescription :", goTSE.oGetStorageInfo.Output.TSEInformation.tseDescription

? "tseInitializationState :", goTSE.oGetStorageInfo.Output.TSEInformation.tseInitializationState

? "tsePublicKey :", goTSE.oGetStorageInfo.Output.TSEInformation.tsePublicKey

? "vendorType :", goTSE.oGetStorageInfo.Output.TSEInformation.vendorType



?gotse.Stack_SetupForPrinter()



? "------------------------------------------------------"

? "----> Seriennummer (über GetStorageInfo)"

? "------------------------------------------------------"

? "TSE SN: ", goTSE.cSNfromPublicKey



IF goTSE.oGetStorageInfo.Output.TSEInformation.hasPassedSelfTest = .F.

*!* *----------------------------------------------------

*!* *--- Führt einen Selbsttest der TSE aus!

*!* *--- KANN 20 SEKUNDEN ODER LÄNGER DAUERN

*!* *----------------------------------------------------

*!* ? "------------------------------------------------------"

*!* ? "----> RunTSESelfTest (Bitte warten)",

*!* ? "------------------------------------------------------"

? "RunTSESelfTest :", IIF( goTSE.Stack_RunTSESelfTest() = 1, "OK", "Nicht OK" )

goTSE.TSEConnectOpenSend( "GetStorageInfo" )

IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF

ENDIF



IF goTSE.oGetStorageInfo.Output.TSEInformation.hasValidTime = .F.

*----------------------------------------------------

*--- aktuelle Zeit setzen

*--- Beim Kassenprogrammstart unbedingt ausführen

*----------------------------------------------------

? "------------------------------------------------------"

? "----> HasValidTime"

? "------------------------------------------------------"

? "HasValidTime :", IIF( goTSE.Stack_Updatetime() = 1, "OK", "Nicht OK" )

IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF

ENDIF

ENDIF



********************************************************************************************************

********************************************************************************************************

********************************************************************************************************

lnDoThis = 0

IF lnDoThis = 1

*----------------------------------------------------

*--- Neuen Client registrieren

*----------------------------------------------------

? "------------------------------------------------------"

? "----> RegisterNewClient Kasse1"

? "------------------------------------------------------"

goTSE.TSE_NewClientID = "Kasse4711"

? "RegisterNewClient :", IIF( goTSE.Stack_RegisterNewClient() = 1, "OK", "Nicht OK" )

IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF



*----------------------------------------------------

*--- Liste mit registrierten Clients ausgeben

*----------------------------------------------------

? "------------------------------------------------------"

? "----> RegisteredClientList MIT Kasse4711"

? "------------------------------------------------------"

? "RegisteredClientList :", IIF( goTSE.Stack_GetRegisteredClientList() = 1, "OK", "Nicht OK" )

FOR lnI = 1 TO GETWORDCOUNT( goTSE.cRegisteredClientList, ";" )

? GETWORDNUM( goTSE.cRegisteredClientList, lnI, ";" )

NEXT

IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF

ENDIF



********************************************************************************************************

********************************************************************************************************

********************************************************************************************************

lnDoThis = 0

IF lnDoThis = 1

*----------------------------------------------------

*--- Client deregistrieren

*----------------------------------------------------

? "------------------------------------------------------"

? "----> DeRegisteredClient Kasse 4711"

? "------------------------------------------------------"

goTSE.TSE_UserID = "Administrator"

goTSE.TSE_NewClientID = "Kasse4711"

? "DeRegisterClient :", IIF( goTSE.Stack_DeRegisterClient() = 1, "OK", "Nicht OK" )

IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF

ENDIF



********************************************************************************************************

********************************************************************************************************

********************************************************************************************************

lnDoThis = 0

IF lnDoThis = 1

*----------------------------------------------------

*--- TSE neuer AdminPin

*----------------------------------------------------

goTSE.TSE_AdminPin = "12345"

goTSE.TSE_NEWADMINPIN = "01234"



?"TSE neuer AdminPin :", IIF( goTSE.Stack_ChangeAdminPin() = 1, "OK", "Nicht OK" )

IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF

ENDIF



********************************************************************************************************

********************************************************************************************************

********************************************************************************************************

lnDoThis = 0

IF lnDoThis = 1

*----------------------------------------------------

*--- TSE neuer TimeAdminPin

*----------------------------------------------------

goTSE.TSE_USERID = "Kasse1"



goTSE.TSE_TimeAdminPin = "54321"

goTSE.TSE_NewTimeAdminPin = "43210"



?"TSE neuer TimeAdminPin :", IIF( goTSE.Stack_ChangetimeAdminPin() = 1, "OK", "Nicht OK" )

IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF

ENDIF



********************************************************************************************************

********************************************************************************************************

********************************************************************************************************

lnDoThis = 0

IF lnDoThis = 1

*----------------------------------------------------

*--- TSE neue PUK

*----------------------------------------------------

goTSE.TSE_USERID = "Kasse1"



goTSE.TSE_Puk = "54321"

goTSE.TSE_NEWPUK = "43210"



?"TSE neuer Puk :", IIF( goTSE.Stack_ChangePuk() = 1, "OK", "Nicht OK" )

IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF

ENDIF



********************************************************************************************************

********************************************************************************************************

******************************************** Kassieren *************************************************

********************************************************************************************************



lnDoThis = 0

IF lnDoThis = 1

*----------------------------------------------------

*--- Start - Ende Transaction (Scannerkassen)

*--- mit Betragsübergabe, ohne DataString

*----------------------------------------------------

goTSE.nTSETraining = 1

goTSE.TSE_ClientID = "Kasse1"

goTSE.TSE_UserID = goTSE.TSE_ClientID

*--- (= "" Datastring leer, 1 = Kassenbeleg, 10 mit MwSt-Satz1, 115 mit MwSt-Satz2, 0 mit MwSt-Satz3, 0 mit MwSt-Satz4, 30 Bar-Zahlung, 95 EC-Zahlung)

goTSE.Stack_StartFinishTransaction("", 1, 1, 10, 115, 0, 0, 0, 30, 95)

*goTSE.Stack_StartFinishTransaction("10.00_115.00_0.00_0.00_0.00^30.00_95.00", 1, 1)

IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF



CLEAR

? "------------------------------------------------------"

? "----> StartTransaktion"

? "------------------------------------------------------"

? "LogTime :", goTSE.oTransActionObjectStart.LogTime

? "SerialNumber : ", goTSE.oTransActionObjectStart.SerialNumber

? "Signature :", goTSE.oTransActionObjectStart.Signature

? "SignatureCounter :", goTSE.oTransActionObjectStart.SignatureCounter

? "TransactionNumber :", goTSE.oTransActionObjectStart.TransactionNumber



? "------------------------------------------------------"

? "----> FinishTransaktion"

? "------------------------------------------------------"

? "LogTime :", goTSE.oTransActionObjectFinish.LogTime

? "SignatureCounter :", goTSE.oTransActionObjectFinish.SignatureCounter

? "Signature :", goTSE.oTransActionObjectFinish.Signature

ENDIF





********************************************************************************************************

********************************************************************************************************

******************************************** Kassieren *************************************************

********************************************************************************************************



lnDoThis = 0

IF lnDoThis = 1

*----------------------------------------------------

*--- Start - Update - Ende Transaction

*----------------------------------------------------

goTSE.nTSETraining = 1

goTSE.TSE_ClientID = "Kasse1"

goTSE.TSE_UserID = goTSE.TSE_ClientID



goTSE.Stack_StartTransaction()

=INKEY(3, "H")



*goTse.Stack_UpdateTransaction('1;"Flasche Gelbwein";12.80', 3, 8 )



goTse.Stack_FinishTransaction("", 1, 1, 0, 12.80, 0, 0, 0, 12.80, 0)

*goTse.Stack_FinishTransaction("0.00_0.00_0.00_0.00_-5000.00^-500.00:_0.00", 1, 4)



IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF



CLEAR

? "------------------------------------------------------"

? "----> StartTransaktion"

? "------------------------------------------------------"

? "LogTime :", goTSE.oTransActionObjectStart.LogTime

? "SerialNumber : ", goTSE.oTransActionObjectStart.SerialNumber

? "Signature :", goTSE.oTransActionObjectStart.Signature

? "SignatureCounter :", goTSE.oTransActionObjectStart.SignatureCounter

? "TransactionNumber :", goTSE.oTransActionObjectStart.TransactionNumber



? "------------------------------------------------------"

? "----> FinishTransaktion"

? "------------------------------------------------------"

? "LogTime :", goTSE.oTransActionObjectFinish.LogTime

? "SignatureCounter :", goTSE.oTransActionObjectFinish.SignatureCounter

? "Signature :", goTSE.oTransActionObjectFinish.Signature

ENDIF



********************************************************************************************************

********************************************************************************************************

******************************************* Kassieren Gastro *******************************************

********************************************************************************************************



lnDoThis = 0

IF lnDoThis = 1

*----------------------------------------------------

*--- GASTRO

*--- Start - Transaction - Finish

*--- Im Gegensatz zu Stack_StartFinishTransaction

*--- können alle Positionen nacheinander signiert werden.

*----------------------------------------------------

goTSE.TSE_ClientID = "Kasse1"

goTSE.TSE_UserID = goTSE.TSE_ClientID



IF goTSE.Gastro_Stack_Connect() = 1



goTSE.Gastro_Stack_StartFinishTransaction( "1;Schnitzel m. Pommes;17.90" )

CLEAR

? "------------------------------------------------------"

? "----> StartTransaktion"

? "------------------------------------------------------"

? "LogTime :", goTSE.oTransActionObjectStart.LogTime

? "SerialNumber : ", goTSE.oTransActionObjectStart.SerialNumber

? "Signature :", goTSE.oTransActionObjectStart.Signature

? "SignatureCounter :", goTSE.oTransActionObjectStart.SignatureCounter

? "TransactionNumber :", goTSE.oTransActionObjectStart.TransactionNumber



? "------------------------------------------------------"

? "----> FinishTransaktion"

? "------------------------------------------------------"

? "LogTime :", goTSE.oTransActionObjectFinish.LogTime

? "SignatureCounter :", goTSE.oTransActionObjectFinish.SignatureCounter

? "Signature :", goTSE.oTransActionObjectFinish.Signature



goTSE.Gastro_Stack_StartFinishTransaction( "1;Salat California;12.50" )

? "------------------------------------------------------"

? "----> StartTransaktion"

? "------------------------------------------------------"

? "LogTime :", goTSE.oTransActionObjectStart.LogTime

? "SerialNumber : ", goTSE.oTransActionObjectStart.SerialNumber

? "Signature :", goTSE.oTransActionObjectStart.Signature

? "SignatureCounter :", goTSE.oTransActionObjectStart.SignatureCounter

? "TransactionNumber :", goTSE.oTransActionObjectStart.TransactionNumber



? "------------------------------------------------------"

? "----> FinishTransaktion"

? "------------------------------------------------------"

? "LogTime :", goTSE.oTransActionObjectFinish.LogTime

? "SignatureCounter :", goTSE.oTransActionObjectFinish.SignatureCounter

? "Signature :", goTSE.oTransActionObjectFinish.Signature



goTSE.Gastro_Stack_DisConnect()

ENDIF



goTSE.Stack_StartTransaction()

goTse.Stack_FinishTransaction("", 1, 1, 0, 12.80, 0, 0, 0, 12.80, 0)

? "------------------------------------------------------"

? "----> StartTransaktion"

? "------------------------------------------------------"

? "LogTime :", goTSE.oTransActionObjectStart.LogTime

? "SerialNumber : ", goTSE.oTransActionObjectStart.SerialNumber

? "Signature :", goTSE.oTransActionObjectStart.Signature

? "SignatureCounter :", goTSE.oTransActionObjectStart.SignatureCounter

? "TransactionNumber :", goTSE.oTransActionObjectStart.TransactionNumber



? "------------------------------------------------------"

? "----> FinishTransaktion"

? "------------------------------------------------------"

? "LogTime :", goTSE.oTransActionObjectFinish.LogTime

? "SignatureCounter :", goTSE.oTransActionObjectFinish.SignatureCounter

? "Signature :", goTSE.oTransActionObjectFinish.Signature



IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF

ENDIF



********************************************************************************************************

********************************************************************************************************

********************************************************************************************************

lnDoThis = 0

IF lnDoThis = 1

CLEAR

*----------------------------------------------------

*--- Liste mit gestarteten Transaktionen

*----------------------------------------------------

? "------------------------------------------------------"

? "----> GetStartedTransactions"

? "------------------------------------------------------"

goTSE.TSE_ClientID = "Kasse1"

goTSE.cStartedTransactionList = ""

? "GetStartedTransactions :", IIF( goTSE.Stack_GetStartedTransactionList() = 1, "OK", "Nicht OK" )

FOR lnI = 1 TO OCCURS( ";", goTSE.cStartedTransactionList )

? GETWORDNUM( goTSE.cStartedTransactionList, lnI, ";" )

ENDFOR

IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF

ENDIF



********************************************************************************************************

********************************************************************************************************

********************************************************************************************************

lnDoThis = 0

IF lnDoThis = 1

*----------------------------------------------------

*--- Daten der TSE exportieren

*----------------------------------------------------

lnExport = 3

gotse.nOpenDirAfterExport = 1



DO CASE

CASE lnExport = 0

?gotse.Stack_ExportArchiveData()



CASE lnExport = 1

gotse.TSE_ClientId = "Kasse1"

gotse.TSE_TransactionNumber = 88

?gotse.Stack_ExportFilteredByTransactionNumber()



CASE lnExport = 2

gotse.TSE_ClientId = "Kasse1"

gotse.TSE_StartTransactionNumber = 1

gotse.TSE_EndTransactionNumber = 55

?gotse.Stack_ExportFilteredByTransactionNumberInterval()



CASE lnExport = 3

gotse.TSE_ClientId = "Kasse1"

gotse.TSE_StartDate = DATETIME(2020,1,1,1,0,0)

gotse.TSE_EndDate = DATETIME()

?gotse.Stack_ExportFilteredByPeriodOfTime()

ENDCASE

ENDIF



********************************************************************************************************

********************************************************************************************************

********************************************************************************************************

lnDoThis = 0

IF lnDoThis = 1

*----------------------------------------------------

*--- Initialisieren (in Produktiv TSE nur 1x möglich)

*----------------------------------------------------

IF goTSE.oGetStorageInfo.Output.TSEInformation.tseInitializationState = "UNINITIALIZED"

? "------------------------------------------------------"

? "----> Setup"

? "------------------------------------------------------"

? "Setup :", IIF( goTSE.Stack_Setup() = 1, "OK", "Nicht OK" )

IF !EMPTY( goTSE.cErrorList )

? "Fehler: ", goTSE.cErrorList

RETURN

ENDIF

ENDIF

ENDIF





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Startablauf

Top Previous Next

Hier erhalten sie eine funktionierende und sichere Anleitung des ersten Aufrufs im Kurzformat zum Implementierung in ihre Software:



1. Sie starten ihr Programm



2. EasyTSE Instantiierung:

goTSE = CREATEOBJECT( "EasyTSE.EpsonTSE" )



3. Alle notwenigen Properties zuweisen

IP, Port, AdminPin, TimeAdminPin, SecretKey, Puk, DeviceID, ClientID und sonstige Einstellungen für Pfade, usw.



4. Verbindung herstellen (zuvor natürlich alle Verbindungsinformationen zuweisen)

lnResult = goTSE.TSEConnectOpenSend()

Falls lnReturn 0, dann keine TSE vorhanden



5. SetupForPrinter ausführen (damit ist sichergestellt, dass ausgewählte DevideID auch mit diesen PC funktioniert):

goTSE.Stack_SetUpForPrinter()



6. Selbsttest ausführen

goTSE.Stack_RunTSESelfTest()



7. Zeit neu setzen

goTSE.Stack_Updatetime()





Das sollte IMMER der Startaufruf in ihrer Software sein!





Funktionen

Top Previous Next

Funktionen zur Kommunikation mit der TSE

Funktionen zur Kommunikation mit der TSE als Stapel

Interne Funktionen



Bitte beachten Sie, dass die genannten Eigenschaften in den Funktionen vor dem Aufruf gesetzt sein müssen!





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE

Top Previous Next

Funktionen zur Kommunikation mit der TSE





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSEClose()

Top Previous Next

TSEClose()

Schließt die über TSEConnect() verbundene TSE ( USB oder Drucker ).



Eigenschaften

TSE_DeviceID



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

nOpened = 0 ( Nicht geöffnet )



Info

Sieh auch TSEOpen().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSEConnect()

Top Previous Next

TSEConnect()

Öffnet eine TCP/IP Verbindung über Socket.



Eigenschaften

TSE_IP

TSE_Port

TSE_TLS

TSE_MaxWaitMs



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

nConnected = 1 ( Verbunden )

nConnected = 0 ( Nicht verbunden )

TSE_SocketID = "sock##########"



Info

Vor einen TSEConnect() wird immer ein TSEReset() ausgeführt.

Siehe auch TSEDisconnect().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSEConnectOpenSend()

Top Previous Next

TSEConnectOpenSend( "Kommando" )

Sendet das übergebene Kommando an die TSE.



Parameter

TSE-Kommando.



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

z.Bsp. oGetStorageInfo



Info

Der TSEConnectOpenSend() verbindet, öffnet, sendet und schließt die TSE selbständig mit nur einem Aufruf.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSEDisconnect()

Top Previous Next

TSEDisconnect()

Schließt eine TCP/IP Verbindung über Socket.



Eigenschaften

TSE_MaxWaitMs



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

nConnected = 0 ( Nicht verbunden )



Info

Siehe auch TSEConnect().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSEOpen()

Top Previous Next

TSEOpen()

Öffnet die über TSEConnect() verbundene TSE ( USB oder Drucker ).



Eigenschaften

TSE_DeviceID



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

nOpened = 1 ( Geöffnet )

nOpened = 0 ( Nicht geöffnet )



Info

Vor einen TSEOpen() muss die Verbindung mit einem TSEConnect() erfolgt sein.

Siehe auch TSEClose().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSEReset()

Top Previous Next

TSEReset()

Setzt einige interne Eigenschaften zurück auf ihren Standardwert.



Eigenschaften

keine



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oResult = .NULL.

nOpened = 0

nConnected = 0

cErrorList = ""



Info

Der TSEReset() führt auch immer ein TSEDisconnect() durch.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSESend()

Top Previous Next

TSESend( "Kommando" )

Sendet das übergebene Kommando an die TSE.



Parameter

TSE-Kommando.



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

z.Bsp. oGetStorageInfo



Info

Der TSESend() erfordert vorab ein TSEConnect() sowie ein TSEOpen() und danach ein TSEClose() und ein TSEDisconnect().

Es wird empfohlen, die Funktion TSEConnectOpenSend() oder eine der Stack-Funktionen zu verwenden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSESend2

Top Previous Next



TSESend2( "Kommando" )

Sendet das übergebene Kommando innerhalb von TSESend an die TSE.



Parameter

TSE-Kommando.



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung



Info





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stapel

Top Previous Next

Stapelfunktionen der Klasse





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Gastro_Stack_Connect()

Top Previous Next

Gastro_Stack_Connect()

Öffnet eine TCP/IP Verbindung über Socket und öffnet die verbundene TSE ( USB oder Drucker ).

Als grafische Hilfe zum besseren Verständnis des Ablaufs, haben wir den Ablauf aus der DSFinV-K 2.1 hier als Bild eingefügt.



Die Stapelfunktionen für eine Bestellung:

Gastro_Stack_Connect(), Gastro_Stack_StartFinishTransaction(), Gastro_Stack_Disconnect()

sind zu verwenden, Block (Start/Finish "Bestellung") (lila, türkis, rote Rechtecke in der Grafik) durchzuführen!



Die Stapelfunktionen um einen Tisch abkassieren zu können (blaue Rechtecke in der Grafik):

Stack_StartTransaction() und Stack_FinishTransaction()

oder

Stack_StartFinshTransaction()





Parameter

keine



Eigenschaften

TSE_ClientID

TSE_SecretKey

TSE_TimeAdminPin



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

TSE_Hash



Info

Öffnet speziell für Gastro-Bestellungen einen Socket und verbindet sich mit dem Device.

Diese Funktion wird als Einleitung in Verbindung mit Gastro_Stack_StartFinishTransaction() und Gastro_Stack_DisConnect() verwendet.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Gastro_Stack_StartFinishTransaction()

Top Previous Next

Gastro_Stack_StartFinishTransaction( DataString )

Führt eine Gastro-Transaktion durch OHNE erneut eine Verbindung zur TSE auf- und abzubauen.

Als grafische Hilfe zum besseren Verständnis des Ablaufs, haben wir den Ablauf aus der DsfinV-K 2.1 hier als Bild eingefügt.



Die Stapelfunktionen für eine Bestellung:

Gastro_Stack_Connect(), Gastro_Stack_StartFinishTransaction(), Gastro_Stack_Disconnect()

sind zu verwenden, Block (Start/Finish "Bestellung") (lila, türkis, rote Rechtecke in der Grafik) durchzuführen!



Es ist nun auch möglich in diesen Stack Gastro_Stack_StartFinishTransaction() mehrere Positionen einer Bestellung in eiem Vorgang zu Buchen und signieren. Siehe hierzu zwingend cDataTrenner!!!



Die Stapelfunktionen um einen Tisch abkassieren zu können (blaue Rechtecke in der Grafik):

Stack_StartTransaction() und Stack_FinishTransaction()

oder

Stack_StartFinshTransaction()





Parameter

DataString



Eigenschaften

TSE_ClientID

TSE_SecretKey

TSE_TimeAdminPin



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oStartTransaction.Output.LogTime

oStartTransaction.Output.SerialNumber

oStartTransaction.Output.Signature

oStartTransaction.Output.SignatureCounter

oStartTransaction.Output.TransactionNumber



oFinishTransaction.Output.LogTime

oFinishTransaction.Output.SignatureCounter

oFinishTransaction.Output.Signature



Info

Aufruf bei einer Position: Gastro_Stack_StartFinishTransaction( "1;Schnitzel m. Pommes;17.90" )

Parameter: DataString muss im Format "Menge;"Beschreibung";Preis" übergeben werden. Trennzeichen muss ein Semikolon ein!



Aufruf bei mehreren Positionen:

Haben sie hingegen mehrere Positionen, die sie in "einem Rutsch" signieren möchten, können sie dies auch bewerkstelligen.

Der große Vorteil liegt hier ganz klar in der Geschwindigkeit und in der Anzahl der gesparten Signaturen.

Dazu verbinden sie die einzelnen Positionen mit einem CR ( Carriage Return o. auch CHR(13) ).

Beispiel: Gastro_Stack_StartFinishTransaction( "1;Schnitzel m. Pommes;17.90" + CHR(13) + "2;Bier vom Fass;4,20" + CHR(13) + "2;Burger Spezial;10.50" )



Um letztendlich den Bezahlvorgang abzuschließen, müssen sie den Stack Gastro_Stack_Finish() starten.



Für den Fall, dass lang anhaltende verkaufsvorbereitende Vorgänge (processType �Bestellung�) mit abzusichern sind,

kann als Startzeitpunkt des Vorgangs (processType �Kassenbeleg�) der Startzeitpunkt des Bezahlvorgangs genutzt werden.

Dadurch entfällt die Notwendigkeit, eine Transaktion, welche alle zugehörigen Bestellungen beinhaltet, offenzuhalten.

Die Transaktion für den prüfbaren �Kassenbeleg� wird dann erst bei Rechnungserstellung gestartet und auch gleich wieder beendet.

Wichtig: Der Start-Zeitpunkt der ersten Transaktion �Bestellung� muss zusätzlich auf dem Bon abgedruckt werden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Gastro_Stack_Disconnect()

Top Previous Next

Gastro_Stack_Disconnect()



Beendet und schließt eine TCP/IP Verbindung über Socket.

Als grafische Hilfe zum besseren Verständnis des Ablaufs, haben wir den Ablauf aus der DsfinV-K 2.1 hier als Bild eingefügt.



Die Stapelfunktionen für eine Bestellung:

Gastro_Stack_Connect(), Gastro_Stack_StartFinishTransaction(), Gastro_Stack_Disconnect()

sind zu verwenden, Block (Start/Finish "Bestellung") (lila, türkis, rote Rechtecke in der Grafik) durchzuführen!



Die Stapelfunktionen um einen Tisch abkassieren zu können (blaue Rechtecke in der Grafik):

Stack_StartTransaction() und Stack_FinishTransaction()

oder

Stack_StartFinshTransaction()





Parameter

keine



Eigenschaften

TSE_ClientID

TSE_SecretKey

TSE_TimeAdminPin



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

keine



Info

Beendet und schließt eine TCP/IP Verbindung über Socket.

Diese Funktion wird als Beendigung für eine Gastrobestellung (1 Schmitzel m. Pommes, 5.80) in Verbindung mit Gastro_Stack_Connect() und Gastro_Stack_StartFinishTransaction() verwendet.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_CancelTransaction()

Top Previous Next

Stack_CancelTransaction( tnTransactionNr, ttStartDateTime)

Schließt eine offene Transaktion mit 0 Werten = Bricht diese ab.



Parameter

tnTransactionNr = INTEGER = Nummer der Transaktionsnummer vom Stack_StartTransaction()

ttStartDateTime = DateTime = Startzeitpunkt von Stack_StartTransaction()



Eigenschaften



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung



Info & Ablauf

Die Funktion Stack_CancelTransaction() ermöglich ihnen einen "Abbruch" einer offenen, angefangenen aber noch nicht abgeschlossenen Transaktion.

Z.B.: Sie erstellen beim ersten Einbuchen einer Ware eine offene Transaktion (mit Stack_StartTransaction()). Nun will der Kunde aber noch schauen.

Den angefangenen Verkauf können sie also noch nicht abschließen, haben aber bereits eine offene Transaktionsnummer auf ihrer TSE.

Somit würden sie nun den offenen Verkauf in ihrer Software z.B. parken. Die Rückgabedaten aus dem Stack_StartTransaction() haben sie bereits in ihren lokalen Datenbanken gespeichert.

Irgendwann kommt der Kunde und will plötzlich keine Ware, was nun? Sie haben ja bereits eine TSE Transaktionsnummer angefordert.

Sie rufen nun einfach diese neue Methode auf: Stack_CancelTransaction().

Dabei übergeben sie die Transaktionsnummer und den Startzeitpunkt der angefangenen Transaktion die sie ja bereits intern in ihrer Datenbank gespeichert haben müssen!

Nun wird die Transaktion mit 0 Werten beendet und geschlossen.



Zusammengefasst:

Stack_StartTransaction() = neue Transaktion beginnen

Stack_CancelTransaction() = angefangen Transaktion (irgendwann) mit 0 schließen und beenden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_ChangeAdminPIN()

Top Previous Next

Stack_ChangeAdminPin()

Ändert den aktuellen AdminPin.



Parameter

keine



Eigenschaften

TSE_Puk

TSE_UserID

TSE_SecretKey

TSE_AdminPin

TSE_NewAdminPin



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

keine



Info

Die Funktion Stack_AdminPin() ändert den aktuellen TSE_AdminPin.

Stack_ChangeAdminPin() wird nur dann ausgeführt, wenn TSE_AdminPin und TSE_NewAdminPin unterschiedlich sind!

Ggf. erhalten sie in cErrorList eine Meldung "Alter AdminPin und neuer AdminPin müssen unterschiedlich sein!"





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_ChangeTimeAdminPIN()

Top Previous Next

Stack_ChangeTimeAdminPin()

Ändert den aktuellen TimeAdminPin.



Parameter

keine



Eigenschaften

TSE_Puk

TSE_UserID

TSE_SecretKey

TSE_AdminPin

TSE_NewTimeAdminPin



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

keine



Info

Die Funktion Stack_TimeAdminPin() ändert den aktuellen TSE_TimeAdminPin.

Stack_ChangeAdminPin() wird nur dann ausgeführt, wenn TSE_TimeAdminPin und TSE_NewTimeAdminPin unterschiedlich sind!

Ggf. erhalten sie in cErrorList eine Meldung "Alter TimeAdminPin und neuer TimeAdminPin müssen unterschiedlich sein!"





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_ChangePUK()

Top Previous Next

Stack_ChangePuk()

Ändert den aktuellen Puk.



Parameter

keine



Eigenschaften

TSE_Puk

TSE_UserID

TSE_SecretKey

TSE_AdminPin

TSE_NewPuk



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

keine



Info

Die Funktion Stack_ChangePuk() ändert den aktuellen TSE_Puk.

Stack_ChangeAdminPin() wird nur dann ausgeführt, wenn TSE_Puk und TSE_NewPuk unterschiedlich sind!

Ggf. erhalten sie in cErrorList eine Meldung "Alter Puk und neuer Puk müssen unterschiedlich sein!"





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_DeRegisterClient()

Top Previous Next

Stack_DeRegisterClient()

Deregistriert einen Client aus der TSE.



Parameter

keine



Eigenschaften

TSE_AdminPin

TSE_TimeAdminPin

TSE_UserID

TSE_SecretKey

TSE_NewClientID



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oDeRegisterClient



Info

Diese Funktion entfernt einen Client (Kasse) aus der TSE.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_ExportArchiveData()

Top Previous Next

Stack_ExportArchiveData( Numerisch )

Ermöglicht es, das komplette Archive (alle Daten) auf einen zuvor festgelegten Pfad (Property: cExportDir) als .tar zu exportieren



Parameter

0 = Keine Löschung (Standard)

1 = Löschung der Daten

Wenn nichts übergeben wurde, wird automatisch das Property nExportArchiveFinalize als 0 gewertet.



Eigenschaften

keine



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

Durch den übergebenen Parameter wird das interne Property nExportArchiveFinalize an der Klasse gesetzt.

Dies bietet die Möglichkei,t nach einen erfolgreichen Daten-Komplettexport die Dateien auf der TSE zu löschen.

0 = nicht löschen, 1 = löschen



Das endgültige Löschen der Dateien auf der TSE (wenn Property nExportArchiveFinalize = 1 ist ) erfolgt auch nur dann,

wenn die zuvor ermittelten Exportbytes mit den tatsächlich exportierten Bytes übereinstimmen.



Info

Wenn kein Ausgabepfad in der Klasse expliziet angegeben wurde, wird der Installationspfad von EasyTSE verwendet.

Beim Programmstart wird zudem überprüft, ob der Verzeichnis: TSE_ExportedData vorhanden ist.

Wenn dies nicht der Fall ist erzeugt EasyTSE das Ausgabeverzeichnis automatisch.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_ExportFilteredByPeriodOfTime()

Top Previous Next

Stack_ExportFilteredByPeriodOfTime()

Exportiert Daten der TSE für einen beliebigen Zeitraum.



Es wird hier explizit ein Startdatum und ein Enddatum benötig.

Diese sind bei den Properties: TSE_StartDate und TSE_EndDate zu finden.

Der Dateityp MUSS zwingend ein Wert vom Typ DateTime oder Character sein.

Die Umwandlung in das benötigte UTC-Time macht EasyTSE automatisch und alleine!



ALTERNATIV können sie auch Startdatum und Enddatum als String übergeben.

z.B.: "TT.MM.YYYY hh:mm:ss"

im Karbeipsiel: "30.11.2020 08:50:10"

Dann würde EasyTSE die Umwandlung in ein gültiges DateTime format selbst durchführen.



Übergeben sie keinesfalls die Datumswerte in UTC Time, denn EasyTSE wandelt diese selbst um!

Also immer das tatsächliche Datum und die gewünschte Uhrzeit übergeben.



Als weiteres Property muss der TSE_ClientId gesetz werden: z.B. "Kasse1"

Denn nur so kann für den gewünschen Client auch ein erfolgreicher Export ausgeführt werden.



Parameter

keine



Eigenschaften

TSE_AdminPin

TSE_TimeAdminPin

TSE_UserID

TSE_SecretKey

TSE_StartDate

TSE_EndDate



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oExportFilteredByPeriodOfTime



Info

Wie Stack_ExportArchiveData() jedoch mit Filter auf Zeitraum.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_ExportFilteredByTransactionNumber()

Top Previous Next

Stack_ExportFilteredByTransactionNumber()

Exportiert Daten der TSE für eine bestimmte Transaktionsnummer.



Parameter

keine



Eigenschaften

TSE_AdminPin

TSE_TimeAdminPin

TSE_UserID

TSE_SecretKey

TSE_ClientID

TSE_TransactionNumber



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oExportFilteredByTransactionNumber



Info

Wie Stack_ArchiveData() jedoch mit Filter auf Transaktionsnummern.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_ExportFilteredByTransactionNumberInterval()

Top Previous Next



Stack_ExportFilteredByTransactionNumberInterval()

Exportiert Daten der TSE für einen definierten Transaktionsnummer-Bereich.



Es müssen die Properties:

TSE_StartTransactionNumber (Integer) und TSE_EndTransactionNumber (Integer) für den Start und dem Ende gesetzt werden.

TSE_ClientId (z.B. Kasse1) wird benötigt um den Client zu lokalisieren.



Parameter

keine



Eigenschaften

TSE_AdminPin

TSE_TimeAdminPin

TSE_UserID

TSE_SecretKey

TSE_ClientID

TSE_StartTransactionNumber

TSE_EndTransactionNumber



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oExportFilteredByTransactionNumberInterval



Info

Wie Stack_ExportArchiveData() jedoch mit Filter auf Transaktionsnummern.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_FactoryReset()

Top Previous Next

Stack_FactoryReset()

Ermöglicht einen Reset in den Fabrikzustand (nur in der Developer-TSE möglich).



Parameter

keine



Eigenschaften

TSE_AdminPin

TSE_UserID



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oFactoryReset



Info

Die Funktion Stack_DeRegisterClient() deregistriert einen Client in der TSE.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_FinishTransaction()

Top Previous Next

Stack_FinishTransaction( DataString, ProcessType, VorgangsType,

BetragGesamtMwSt1, BetragGesamtMwSt2, BetragGesamtMwSt3,

BetragGesamtMwSt4, BetragGesamtMwSt0,

BetragTotalBar, BetragTotalUnbar )



Schließt eine, mit Stack_StartTransaction -> Stack_UpdateTransaction() angestoßene Transaktion ab.





Wichtig:

Um eine gestartete Transaktion abzuschließen, muss die zu schließende Transaktionsnummer dem internen Property TSE_Transactionnumber zugewiesen werden!

Denn es könnte sein, dass sie eine Transaktion starten, aber erst später abschließen, oder mehrere Transaktionen starten und somit mehrere offene Transaktionen aktiv haben.



Wenn sie den Stack_StartFinishTransaction() verwenden müssen diese Zuweisung nicht vornehmen, da EasyTSE sich die Starttransaktionsnummer merkt.





Als grafische Hilfe zum besseren Verständnis des Ablaufs, haben wir den Ablauf aus der DSFinV-K 2.1 hier als Bild eingefügt.



Dieser Stack ist inhaltlich exakt gleich zum Stack: Stack_FinishTransactionDouble(),

jedoch sind hier die Betragsparameter von Typ Decimal und nicht vom Typ DOUBLE, wie im Stack: Stack_FinishTransactionDouble().

Dadurch wird eine gewisse Typsicherheit gewährleistet.





Parameter

DataString

ProcessType

VorgangsType

BetragGesamtMwSt1

BetragGesamtMwSt2

BetragGesamtMwSt3

BetragGesamtMwSt4

BetragGesamtMwSt0

BetragTotalBar

BetragTotalUnbar



Eigenschaften

TSE_AdminPin

TSE_UserID

TSE_SecretKey



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oTransActionObjectFinish.LogTime

oTransActionObjectFinish.SignatureCounter

oTransActionObjectFinish.Signature



Info

In C# würde man sagen, dass diese Funktion ist 2fach überladen ist.

Dies bedeutet, dass der Aufruf auf zweierlei Arten mit unterschiedlicher Funktionsweise aufgerufen werden kann.



Variante 1 (Komma separierte Parameter)

Aufruf: Stack_FinishTransaction( "",1, 1, 119.00, 107.00, 0.00, 0.00, 0.00, 26.00, 200.00 )

1. Parameter: DataString muss hier leer geliefert werden.

2. Parameter: ProcessType, siehe KindOfProcessTypes()

3. Parameter: VorgangsType, siehe KindOfVorgangsTypes()

4. Parameter: BetragGesamtMwSt1, Gesamtbruttobetrag des Vorgangs mit allgemeinem Steuersatz (19 o. 16%)

5. Parameter: BetragGesamtMwSt2, Gesamtbruttobetrag des Vorgangs mit ermäßigten Steuersatz (7 o. 5 %)

6. Parameter: BetragGesamtMwSt3, Gesamtbruttobetrag des Vorgangs mit Durchschnittsatz §24(1)Nr.3 UStG (10.7%)

7. Parameter: BetragGesamtMwSt4, Gesamtbruttobetrag des Vorgangs mit Durchschnittsatz §24(1)Nr.3 UStG (5,5%)

8. Parameter: BetragGesamtMwSt0, Gesamtbruttobetrag des Vorgangs ohne Steuer (0%)

9. Parameter: BetragTotalBar, Betrag in Bar

10. Parameter: BetragTotalUnbar, Betrag in Unbar (EC, Kreditkarte, auf Rechnung, usw.)





Variante 2 (Eigenes ProcessData als Parameter)

Aufruf: Stack_FinishTransaction( "119.00_107.00_0.00_0.00_0.00^10.00:BAR:EUR_20.00:BAR:CHF_50.00:UNBAR:USD" )

1. Parameter: DataString muss im definierten Format (siehe Beispiel 2, Zeile darüber) übergeben werden. (siehe DSFinV-K)

2. (ProcessType) + 3. (VorgangsType) Parameter müssen übergeben werden

4. bis 11. Parameter werden ignoriert



Hinweis:

Die Klasse verwendet aktuell keine Währungseinheiten, so dass EUR als Standardwährung verwendet wird.

Sollten Sie anderen Währungen wie CHF oder USD benötigen, müssen Sie den DataString entsprechend mit allen notwendigen Informationen übergeben.



Info



Zwingende Ablaufreihenfolge bei Verwendung dieser Funktion:

Stack_StartTransaction -> Stack_UpdateTransaction -> Stack_FinishTransaction

oder

Stack_StartTransaction -> Stack_FinishTransaction

oder

Stack_StartTransaction -> Stack_CancelTransaction



* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.Reserved.





Stack_FinishTransactionDouble()

Top Previous Next

Stack_FinishTransaction( DataString, ProcessType, VorgangsType, TransactionNumber,

BetragGesamtMwSt1, BetragGesamtMwSt2, BetragGesamtMwSt3,

BetragGesamtMwSt4, BetragGesamtMwSt0,

BetragTotalBar, BetragTotalUnbar )



Schließt eine, mit Stack_StartTransaction -> Stack_UpdateTransaction() angestoßene Transaktion ab.





Wichtig:

Um eine gestartete Transaktion abzuschließen, muss die zu schließende Transaktionsnummer dem internen Property TSE_Transactionnumber zugewiesen werden!

Denn es könnte sein, dass sie eine Transaktion starten, aber erst später abschließen, oder mehrere Transaktionen starten und somit mehrere offene Transaktionen aktiv haben.



Wenn sie den Stack_StartFinishTransactionDouble() verwenden müssen diese Zuweisung nicht vornehmen, da EasyTSE sich die Starttransaktionsnummer merkt.





Als grafische Hilfe zum besseren Verständnis des Ablaufs, haben wir den Ablauf aus der DSFinV-K 2.1 hier als Bild eingefügt.



Dieser Stack ist inhaltlich exakt gleich zum Stack: Stack_FinishTransaction(),

jedoch sind hier die Betragsparameter von Typ DOUBLE und nicht vom Typ Decimal, wie im Stack: Stack_FinishTransaction().

Dadurch wird eine gewisse Typsicherheit gewährleistet.





Parameter

DataString (String)

ProcessType (Numerisch, Integer)

VorgangsType (Numerisch, Integer)

TransactionNumber (Numerisch, Integer)

BetragGesamtMwSt1 (Double)

BetragGesamtMwSt2 (Double)

BetragGesamtMwSt3 (Double)

BetragGesamtMwSt4 (Double)

BetragGesamtMwSt0 (Double)

BetragTotalBar (Double)

BetragTotalUnbar (Double)



Eigenschaften

TSE_AdminPin

TSE_UserID

TSE_SecretKey



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oTransActionObjectFinish.LogTime

oTransActionObjectFinish.SignatureCounter

oTransActionObjectFinish.Signature



Info

In C# würde man sagen, dass diese Funktion ist 2fach überladen ist.

Dies bedeutet, dass der Aufruf auf zweierlei Arten mit unterschiedlicher Funktionsweise aufgerufen werden kann.



Variante 1 (Komma separierte Parameter)

Aufruf: Stack_FinishTransactionDouble( "",1, 1, 0, 119.00, 107.00, 0.00, 0.00, 0.00, 26.00, 200.00 )

1. Parameter: DataString muss hier leer geliefert werden.

2. Parameter: ProcessType, siehe KindOfProcessTypes()

3. Parameter: VorgangsType, siehe KindOfVorgangsTypes()

4. Parameter: Transaktionsnummer von einem gestarteten Vorgang

5. Parameter: BetragGesamtMwSt1, Gesamtbruttobetrag des Vorgangs mit allgemeinem Steuersatz (19 o. 16%)

6. Parameter: BetragGesamtMwSt2, Gesamtbruttobetrag des Vorgangs mit ermäßigten Steuersatz (7 o. 5 %)

7. Parameter: BetragGesamtMwSt3, Gesamtbruttobetrag des Vorgangs mit Durchschnittsatz §24(1)Nr.3 UStG (10.7%)

8. Parameter: BetragGesamtMwSt4, Gesamtbruttobetrag des Vorgangs mit Durchschnittsatz §24(1)Nr.3 UStG (5,5%)

9. Parameter: BetragGesamtMwSt0, Gesamtbruttobetrag des Vorgangs ohne Steuer (0%)

10. Parameter: BetragTotalBar, Betrag in Bar

11. Parameter: BetragTotalUnbar, Betrag in Unbar (EC, Kreditkarte, auf Rechnung, usw.)





Variante 2 (Eigenes ProcessData als Parameter)

Aufruf: Stack_FinishTransactionDouble( "119.00_107.00_0.00_0.00_0.00^10.00:BAR:EUR_20.00:BAR:CHF_50.00:UNBAR:USD" )

1. Parameter: DataString muss im definierten Format (siehe Beispiel 2, Zeile darüber) übergeben werden. (siehe DSFinV-K)

2. (ProcessType) + 3. (VorgangsType) Parameter müssen übergeben werden

4. bis 11. Parameter werden ignoriert



Hinweis:

Die Klasse verwendet aktuell keine Währungseinheiten, so dass EUR als Standardwährung verwendet wird.

Sollten Sie anderen Währungen wie CHF oder USD benötigen, müssen Sie den DataString entsprechend mit allen notwendigen Informationen übergeben.



Info



Zwingende Ablaufreihenfolge bei Verwendung dieser Funktion:

Stack_StartTransaction -> Stack_UpdateTransaction -> Stack_FinishTransactionDouble

oder

Stack_StartTransaction -> Stack_FinishTransactionDouble

oder

Stack_StartTransaction -> Stack_CancelTransaction



* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.Reserved.





Stack_GetLogMessageCertificate()

Top Previous Next

Stack_GetLogMessageCertificate()

Liefert einen String mit den, auf der TSE hinterlegten, gebrandeten Zertifikat



Parameter

keine



Eigenschaften

cCertificate



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

cCertificate enthält das Zertifikat als String



Info





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_GetRegisteredClientList()

Top Previous Next

Stack_GetRegisteredClientList()

Liefert einen String mit allen registrierten Kassen (Clients).



Parameter

keine



Eigenschaften

TSE_AdminPin

TSE_UserID



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

cRegisteredClientList (Liste aller Clients getrennt durch Semikolon)



Info

Bereits beim Setup legt die TSE automatisch einen Client an, welcher standardmäßig EPSON<PCName> benannt wird.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_GetStartedTransactionList()

Top Previous Next

Stack_GetStartedTransactionList()

Liefert einen String mit allen gestarteten Transaktionensnummern.



Parameter

keine



Eigenschaften

TSE_AdminPin

TSE_UserID

TSE_ClientID



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

cStartedTransactionList (Liste aller gestarteten Transaktionen getrennt durch Semikolon)



Info

Wir empfehlen keine Transaktionen offen zu halten sondern jede Buchung durch eine Transaktion mit Stack_StartFinishTransaction() abzusichern.

Dies insbesondere dann, wenn mehrere TSEs eingesetzt werden und der startende Client nicht der Client ist der den Vorgang abschließt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_LockTSE()

Top Previous Next

Stack_LockTSE()

Sperrt eine TSE, es können keine Transaktionen mehr durchgeführt werden.



Parameter

keine



Eigenschaften

TSE_AdminPin

TSE_UserID

TSE_SecretKey



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oLockTSE



Info

Eine gesperrte TSE kann nur durch das Kommando UnlockTSE oder durch den Stack_UnlockTSE() entsperrt werden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_OpenTransaction()

Top Previous Next

Stack_OpenTransaction()

Startet eine Transaktion, und schließ diese wieder. Muss expliziet mit Stack_CloseOpenTransaction() geschlossen werden.



Parameter

keine



Eigenschaften



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung





Info





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_RegisterNewClient()

Top Previous Next

Stack_RegisterNewClient()

Registriert einen neuen Client in der TSE.



Parameter

keine



Eigenschaften

TSE_AdminPin

TSE_TimeAdminPin

TSE_UserID

TSE_SecretKey

TSE_NewClientID



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oRegisterClient



Info

Diese Funktion registriert einen neuen Client in der TSE. Dies ist notwendig, um Transaktionen einer "Kasse" erstellen zu können.

Beim "normalen" Setup wird standardmäßig der Client EPSON<PCName> angelegt.

Alternativ kann man über den Stack_Setup() auch vorab einen Client-Namen im Property TSE_NewClientID setzen.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_RegisterSecretKey()

Top Previous Next

Stack_RegisterSecretKey()

Registriert einen neuen SecretKey in der TSE.



Parameter

keine



Eigenschaften

TSE_AdminPin

TSE_SecretKey



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oRegisterSecretKey



Info

Der default-SecretKey der TSE ist "EPSONKEY" und kann bei Bedarf überschrieben werden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_RunTSESelfTest()

Top Previous Next

Stack_RunTSESelftest()

Startet einen Selbsttest der TSE.



Parameter

keine



Eigenschaften

keine



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oRunTSESelftest



Info

Die TSE muss regelmäßig einen Selbsttest durchführen, um das ordnungsgemäße Funktionieren der Signaturfunktionalität zu gewährleisten.

Beim Einschalten des Epson TSE-Treibers wird der Selbsttest intern durchgeführt. Der Vorgang kann bis zu 20 Sekunden dauern!

Nach 25 Stunden ist der Status "hasPassedSelfTest" in der GetStorageInfo falsch und die meisten Funktionen schlagen mit "TSE_ERROR_WRONG_STATUS_SELFTEST_NEEDED" fehl.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_Setup()

Top Previous Next

Stack_Setup()

Einmalige Initialisierung der TSE.



Parameter

keine



Eigenschaften

TSE_Puk

TSE_AdminPin

TSE_TimeAdminPin

TSE_UserID



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oSetup



Info

Die Funktion Stack_Setup() kann nur ausgeführt werden, solange der Status der TSE auf UNINITIALIZED steht.

Sie muss einmalig zu Beginn als Initialisierung der TSE erfolgen.

Nur in der Developer-TSE besteht die Möglichkeit, die TSE für neue Tests zurückzusetzen.



Bitte beachten Sie, dass die TSE standardmäßig beim Setup einen User anlegt.

Wenn die Eigenschaft TSE_NewClientID vorab gesetzt wird, verwendet der Stack_Setup() diesen User zur Initialisierung.



Es besteht auch eine Abhänigkeit zu Property nAutoSetup, dass standardmäßig 0 ist.

Sollten sie das Property nAutoSetup auf 1 setzen, übernimmt EasyTSE automatisch bei einen UNINITIALIZED-State der TSE den Aufruf des Stack_Setup().

Die ist natürlich sehr komfortabel und praktisch.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_SetupForPrinter()

Top Previous Next

Stack_SetupForPrinter()

Möglichkeit schaffen, damit z.B., die TSE A von Computer A nun auf Computer B betrieben werden kann.



Parameter

keine



Eigenschaften



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oSetupForPrinter



Info

Da die TSE bei der Initialisierung mit einen Compouter "verbunden" wird, kann eine bestehende TSE nicht einfach ausgesteckt und an einen anderen PC eingesteckt werden.

Dieser Stack bietet ihnen die Möglichkeit die TSE A von Computer A nun auf Computer B zu verwenden.

Einfaches Umstecken, ohne den Stapel Stack_SetupForPrinter auszuführen hat zur Folge, dass die TSE das Arbeiten strikt verweigert.

Wir raten ihnen jedoch davon ab, eine bereits zugeordnete TSE zu einen PC einfach und vor allem ständig zu wechseln.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_StartFinishTransactionDouble()

Top Previous Next

Stack_StartFinishTransactionDouble( DataString, ProcessType, VorgangsTyp,

BetragGesamtMwSt1, BetragGesamtMwSt2, BetragGesamtMwSt3,

BetragGesamtMwSt4, BetragGesamtMwSt0,

BetragTotalBar, BetragTotalUnbar )



Startet und beendet eine neue Transaktion auf einfachste, schnellste Art und Weise.

Als grafische Hilfe zum besseren Verständnis des Ablaufs, haben wir den Ablauf aus der DSFinV-K 2.1 hier als Bild eingefügt.



Dieser Stack ist inhaltlich exakt gleich zum Stack: Stack_StartFinishTransaction(),

jedoch sind hier die Betragsparameter von Typ DOUBLE und nicht vom Typ Decimal, wie im Stack: Stack_StartFinishTransaction().

Dadurch wird eine gewisse Typsicherheit gewährleistet.





Parameter

DataString (String)

ProcessType (integer) erlaubt =

1 = Kassenbeleg

2 = Bestellung

3 = SonstigerVorgang

VorgangsTyp (Integer)

BetragGesamtMwSt1 (Double)

BetragGesamtMwSt2 (Double)

BetragGesamtMwSt3 (Double)

BetragGesamtMwSt4 (Double)

BetragGesamtMwSt0 (Double)

BetragTotalBar (Double)

BetragTotalUnbar (Double)



Eigenschaften

TSE_UserID

TSE_ClientID

TSE_SecretKey

TSE_TimeAdminPin



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oTransActionObjectStart.LogTime

oTransActionObjectStart.SerialNumber

oTransActionObjectStart.Signature

oTransActionObjectStart.SignatureCounter

oTransActionObjectStart.TransactionNumber



oTransActionObjectFinish.LogTime

oTransActionObjectFinish.SignatureCounter

oTransActionObjectFinish.Signature



Info

In C# würde man sagen, dass diese Funktion ist 2fach überladen ist.

Dies bedeutet, dass der Aufruf auf zweierlei Arten mit unterschiedlicher Funktionsweise aufgerufen werden kann.



Variante 1 (Komma separierte Parameter, ProcessData leer)

Aufruf: Stack_StartFinishTransactionDouble( "",1, 1, 119,107.50,0,0 ,0, 26.50, 200.00 )

1. Parameter: DataString muss hier leer geliefert werden.

2. Parameter: PorcessType, siehe KindOfPocessTypes()

3. Parameter: VorgangsTyp, siehe KindOfVorgangTypes()

4. Parameter: BetragGesamtMwSt1, Gesamtbruttobetrag des Vorgangs mit allgemeinen Steuersatz (19 o. 16%)

5. Parameter: BetragGesamtMwSt2, Gesamtbruttobetrag des Vorgangs mit ermäßigten Steuersatz (7 o. 5 %)

6. Parameter: BetragGesamtMwSt3, Gesamtbruttobetrag des Vorgangs mit Durchschnittsatz §24(1)Nr.3 UStG (10.7%)

7. Parameter: BetragGesamtMwSt4, Gesamtbruttobetrag des Vorgangs mit Durchschnittsatz §24(1)Nr.3 UStG (5,5%)

8. Parameter: BetragGesamtMwSt0, Gesamtbruttobetrag des Vorgangs ohne Steuer (0%)

9. Parameter: BetragTotalBar, Zahlbetrag in Bar

10. Parameter: BetragTotalUnbar, Zahlbetrag in Unbar (EC, Kreditkarte, auf Rechnung, usw.)



Variante 2 (Eigenes ProcessData als Parameter)

Aufruf: Stack_StartFinishTransactionDouble( "119.00_107.00_0.00_0.00_0.00^10.00:BAR:EUR_20.00:BAR:CHF_50.00:UNBAR:USD", 1, 1 )

1. Parameter: DataString muss im definierten Format (siehe Beispiel 2, Zeile darüber) übergeben werden. (siehe DSFinV-K)

2. (ProcessType) + 3. (VorgangsType) Parameter müssen übergeben werden

4. bis 10. Parameter werden ignoriert



Hinweis:

Die Klasse verwendet aktuell keine Währungseinheiten, so dass EUR als Standardwährung verwendet wird.

Sollten Sie anderen Währungen wie CHF oder USD benötigen, müssen Sie den DataString entsprechend mit allen notwendigen Informationen übergeben.



Auch ist es möglich im Zuge der Implementierung "Trainingsbuchungen" durchzuführen.

Dazu weisen die dem Property nTSETraining den Wert 1 zu.

Dadurch wird in diesen Call "Stack_StartFinishTransaction" nicht der ProcessType 1 (Kassenbeleg), sondern der ProcessType "Training" verwendet.

Dieser Typ ist bei Prüfungen nicht relevant und bleibt unberücksichtigt.

Dieses Property muss vor jeder Trainingsbuchung wieder neu gesetzt werden (nTSETraining = 1).



Für Gastro-Bestellungen verwenden Sie bitte die Funktionen Gastro_Stack_Start(), Gastro_Stack_Transaction() und Gastro_Stack_Finish().



Für Buchungen von "anderen Bewegungen" verwenden Sie bitte die Funktionen: Stack_StartTransaction() -> Stack_UpdateTransaction() -> Stack_FinishTransactionD()



* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_StartFinishTransaction()

Top Previous Next

Stack_StartFinishTransaction( DataString, ProcessType, VorgangsTyp,

BetragGesamtMwSt1, BetragGesamtMwSt2, BetragGesamtMwSt3,

BetragGesamtMwSt4, BetragGesamtMwSt0,

BetragTotalBar, BetragTotalUnbar )



Startet und beendet eine neue Transaktion auf einfachste, schnellste Art und Weise.

Als grafische Hilfe zum besseren Verständnis des Ablaufs, haben wir den Ablauf aus der DSFinV-K 2.1 hier als Bild eingefügt.



Dieser Stack ist inhaltlich exakt gleich zum Stack: Stack_StartFinishTransactionDouble(),

jedoch sind hier die Betragsparameter von Typ Decimal und nicht vom Typ DOUBLE, wie im Stack: Stack_StartFinishTransactionDouble().

Dadurch wird eine gewisse Typsicherheit gewährleistet.





Parameter

DataString (String)

ProcessType (integer) erlaubt =

1 = Kassenbeleg

2 = Bestellung

3 = SonstigerVorgang

VorgangsTyp (Integer)

BetragGesamtMwSt1 (Numerisch)

BetragGesamtMwSt2 (Numerisch)

BetragGesamtMwSt3 (Numerisch)

BetragGesamtMwSt4 (Numerisch)

BetragGesamtMwSt0 (Numerisch)

BetragTotalBar (Numerisch)

BetragTotalUnbar (Numerisch)



Eigenschaften

TSE_UserID

TSE_ClientID

TSE_SecretKey

TSE_TimeAdminPin



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oTransActionObjectStart.LogTime

oTransActionObjectStart.SerialNumber

oTransActionObjectStart.Signature

oTransActionObjectStart.SignatureCounter

oTransActionObjectStart.TransactionNumber



oTransActionObjectFinish.LogTime

oTransActionObjectFinish.SignatureCounter

oTransActionObjectFinish.Signature



Info

In C# würde man sagen, dass diese Funktion ist 2fach überladen ist.

Dies bedeutet, dass der Aufruf auf zweierlei Arten mit unterschiedlicher Funktionsweise aufgerufen werden kann.



Variante 1 (Komma separierte Parameter, ProcessData leer)

Aufruf: Stack_StartFinishTransaction( "",1, 1, 119,107.50,0,0 ,0, 26.50, 200.00 )

1. Parameter: DataString muss hier leer geliefert werden.

2. Parameter: PorcessType, siehe KindOfPocessTypes()

3. Parameter: VorgangsTyp, siehe KindOfVorgangTypes()

4. Parameter: BetragGesamtMwSt1, Gesamtbruttobetrag des Vorgangs mit allgemeinen Steuersatz (19 o. 16%)

5. Parameter: BetragGesamtMwSt2, Gesamtbruttobetrag des Vorgangs mit ermäßigten Steuersatz (7 o. 5 %)

6. Parameter: BetragGesamtMwSt3, Gesamtbruttobetrag des Vorgangs mit Durchschnittsatz §24(1)Nr.3 UStG (10.7%)

7. Parameter: BetragGesamtMwSt4, Gesamtbruttobetrag des Vorgangs mit Durchschnittsatz §24(1)Nr.3 UStG (5,5%)

8. Parameter: BetragGesamtMwSt0, Gesamtbruttobetrag des Vorgangs ohne Steuer (0%)

9. Parameter: BetragTotalBar, Zahlbetrag in Bar

10. Parameter: BetragTotalUnbar, Zahlbetrag in Unbar (EC, Kreditkarte, auf Rechnung, usw.)



Variante 2 (Eigenes ProcessData als Parameter)

Aufruf: Stack_StartFinishTransaction( "119.00_107.00_0.00_0.00_0.00^10.00:BAR:EUR_20.00:BAR:CHF_50.00:UNBAR:USD", 1, 1 )

1. Parameter: DataString muss im definierten Format (siehe Beispiel 2, Zeile darüber) übergeben werden. (siehe DSFinV-K)

2. (ProcessType) + 3. (VorgangsType) Parameter müssen übergeben werden

4. bis 10. Parameter werden ignoriert



Hinweis:

Die Klasse verwendet aktuell keine Währungseinheiten, so dass EUR als Standardwährung verwendet wird.

Sollten Sie anderen Währungen wie CHF oder USD benötigen, müssen Sie den DataString entsprechend mit allen notwendigen Informationen übergeben.



Auch ist es möglich im Zuge der Implementierung "Trainingsbuchungen" durchzuführen.

Dazu weisen die dem Property nTSETraining den Wert 1 zu.

Dadurch wird in diesen Call "Stack_StartFinishTransaction" nicht der ProcessType 1 (Kassenbeleg), sondern der ProcessType "Training" verwendet.

Dieser Typ ist bei Prüfungen nicht relevant und bleibt unberücksichtigt.

Dieses Property muss vor jeder Trainingsbuchung wieder neu gesetzt werden (nTSETraining = 1).



Für Gastro-Bestellungen verwenden Sie bitte die Funktionen Gastro_Stack_Start(), Gastro_Stack_Transaction() und Gastro_Stack_Finish().



Für Buchungen von "anderen Bewegungen" verwenden Sie bitte die Funktionen: Stack_StartTransaction() -> Stack_UpdateTransaction() -> Stack_FinishTransaction()



* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_StartTransaction()

Top Previous Next

Stack_StartTransaction()

Startet einen neuen Vorgang zur Absicherung von Daten

Als grafische Hilfe zum besseren Verständnis des Ablaufs, haben wir den Ablauf aus der DSFinV-K 2.1 hier als Bild eingefügt.





,

Diese Funktion startet einen neuen Absicherungsvorgang, in dem mehrere Buchungen/Transaktionen über Stack_UpdateTransaction unter der selben Transaktionsnummer eingebucht und signiert werden können.

Dies hat den charmanten Vorteil, daß sie z.B. eine Bankeinzahlung, eine Privateinnahme, eine Kassenentnahme (z.B. Tanken), usw. signieren können und nur 1 Transaktionsnummer verbrauchen.

Getätigte Transaktionen müssen zum Schluß mit Stack_FinishTransaction beendet werden!



Sollten sie mehrere Stack_UpdateTransaction() nach einem Stack_StartTransaction() ausführen (z.B. Signierung von 5 Einzelpositionen eines Verkaufs), gibt es auch die charmante Option,

die bestehende Verbindung von Stack_StartTransaction() offen zu halten und dann die Einzelpositionen in einer Schleife mit Stack_UpdateTransaction() zu signieren und letztendlich die offene Verbindung mit Stack_FinishTransaction() zu schließen.

Dazu müssen sie lediglich das Property nHoldConnection auf 1 setzten.

Parameter

keine



Eigenschaften

TSE_TimeAdminPin

TSE_UserID

TSE_SecretKey



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oStartTransaction



Info

Zwingende Ablauffolge bei Verwendung dieser Funktion:

Stack_StartTransaction - > x mal Stack_UpdateTransaction - Stack_FinishTransaction

oder bei Scannerkassen:

Stack_StartTransaction - > SetTransactionObjectStart - Stack_FinishTransaction





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_UnblockUserForAdmin()

Top Previous Next

Stack_UnblockUserForAdmin()

Setzt eine neue PIN für Admin-User



Parameter

keine



Eigenschaften

TSE_AdminPin

TSE_NewAdminPin

TSE_Puk



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oUnblockUserForAdmin



Info

Setzt eine neue PIN für Admin-User





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_UnblockUserForTimeAdmin()

Top Previous Next

Stack_UnblockUserForTimeAdmin()

Setzt eine neue PIN für TimeAdmin-User



Parameter

keine



Eigenschaften

TSE_TimeAdminPin

TSE_NewTimeAdminPin

TSE_Puk



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oUnblockUserForTimeAdmin



Info

Setzt eine neue PIN für TimeAdmin-User





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_UnlockTSE()

Top Previous Next

Stack_UnlockLockTSE()

Entsperrt eine TSE, Transaktionen können wieder durchgeführt werden.



Parameter

keine



Eigenschaften

TSE_AdminPin

TSE_UserID

TSE_SecretKey



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oUnlockTSE



Info

Eine vorab gesperrte TSE z.Bsp. durch Stack_LockTSE() entsperren.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_UpdateTime()

Top Previous Next

Stack_UpdateTime( Numerisch )

Ermittelt und setzt unter Berücksichtigung der Zeitzone sowie der Sommer/Winterzeit die aktuelle UTC-Zeit.

Der Parameter dient ausschließlich zur Weitergabe der Zeitsetzung an die Methode SetUTCTime().



Parameter

0 oder leer = Versucht über eine Internetverbindung die UTC-Time zu ermitteln. Falls das nicht funktioniert, wird die lokale PC-Zeit verwendet.

1 = Lokale PC-Zeit verwenden.



Eigenschaften

TSE_AdminPin

TSE_TimeAdminPin

TSE_UserID



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oUpdateTime

TSE_NewTime = "2020-01-01T05:23:17Z"



Info

Die Funktion prüft zunächst, ob eine Internetverbindung besteht und ruft von einer öffentlichen Stelle die Atomzeit ab und wandelt diese in das o.g. Zeitformat um.

Dabei wird auch die Sommer/Winterzeit berücksichtigt. Sollte keine Verbindung möglich sein, wird der Zeitstempel vom aktuellen Windows-PC verwendet.



Das Setzen des Zeitstempels ist innerhalb der TSE ein elementarer Vorgang und sollte mind. 1 Mal am Tag gesendet werden.

Über das Kommando GetStorageInfo und der Eigenschaft hasValidTime kann geprüft werden, ob das Setzen der Zeit erforderlich ist.



Über den Parameter DateTime kann ein Zeitstempel vorgegeben werden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Stack_UpdateTransaction()

Top Previous Next

Stack_UpdateTransaction( DataString, ProcessType, VorgangsTyp )



Ermöglicht es beliebige Transaktionstypen und Vorgangstypen innerhalb einer Transaktionsnummer (gestartet nit Stack_StartTransaction) zu starten und zu signieren.

Als grafische Hilfe zum besseren Verständnis des Ablaufs, haben wir den Ablauf aus der DSFinV-K 2.1 hier als Bild eingefügt.



Sollten sie mehrere Stack_UpdateTransaction() nach einem Stack_StartTransaction() ausführen (z.B. Signierung von 5 Einzelpositionen eines Verkaufs), gibt es auch die charmante Option,

die bestehende Verbindung von Stack_StartTransaction() offen zu halten und dann die Einzelpositionen in einer Schleife mit Stack_UpdateTransaction() zu signieren und letztendlich die offene Verbindung mit Stack_FinishTransaction() zu schließen.

Dazu müssen sie lediglich das Property nHoldConnection auf 1 setzten.





Parameter

1. Parameter: DataString

2. Parameter: ProcessType, siehe KindOfProcessTypes()

3. Parameter: VorgangsType, siehe KindOfVorgangsTypes()





Eigenschaften

TSE_AdminPin

TSE_UserID

TSE_SecretKey



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

oTransActionObjectUpdate.LogTime

oTransActionObjectUpdate.SignatureCounter

oTransActionObjectUpdate.Signature



Info

Beispiele für einen Aufruf:

- Stack_UpdateTransaction("1;Bankeinzahlung;500.00",3, 8)

- Stack_UpdateTransaction("1;Privateinlage;150.00",3, 8)

- Stack_UpdateTransaction("1;Tanken;30.00",3, 8)

usw.

Das Trennzeichen im Datastring MUSS ein Selikonom sein!



Zwingende Ablaufreihenfolge bei Verwendung dieser Funktion:

Stack_StartTransaction - > x mal Stack_UpdateTransaction - Stack_FinishTransaction





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved. Thomas Geißler. All Rights Reserved.





Intern

Top Previous Next

Interne Funktionen der Klasse





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Base64ToString()

Top Previous Next

Base64ToString( "String" )

Wandelt eine base64-Zeichenfolge in eine hexadezimale Zeichenfolge um.



Parameter

base64 codierte Zeichenfolge.



Rückgabe

Base64 codierter String.



Eigenschaften nach Ausführung

keine



Info

Siehe auch StringToBase64().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





BuildSNfromPublicKey()

Top Previous Next

BuildSNfromPublicKey()

Errechnet anhand des PublicKeys die Seriennummer der TSE.



Parameter

keine



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

cSNfromPublicKey



Info

Bei der Entwicklung dieser Klasse sind wir interessanterweise auf mehrere Seriennummern gestoßen.

Bei den USB-Sticks direkt auf der Verpackung, innerhalb der TSE gibt es eine SerialNumber und eine Seriennummer die aus dem PublicKey generiert werden kann.

Auf der QR-Code-Testseite https://kassen-qr-code-test.de/ kann die errechnete SN angezeigt werden. An dieser haben wir uns orientiert und wird im o.g. Property gespeichert.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





BuildQRBMP()

Top Previous Next

BuildQRBMP( "String" )

Erstellt anhand des Parameters (QR-Code) ein BMP.



Parameter

QRCode als String



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

cQRCodePath



Info

BuildQRBMP() erstellt anhand des Parameters und den Einstellungen von cTempDir eine QR-Code-BMP-Datei.

Beim Beenden der Klasse werden alle BMP-Dateien aus diesem Ordner automatisch gelöscht.



Für diese Erstellung wird das registrierte RQRCodeX.OCX benötigt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





BuildQRCode()

Top Previous Next

BuildQRCode( "String" )

Erstellt anhand des Parameters und der Transaktionsdaten einen QR-Code.



Parameter

tcProcessData



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

cQRCode



Info

Für die optionale Erstellung eines QR-Codes wird hier der String erzeugt. Diese Funktion wird immer vom Stack_StartFinishTransaction() aufgerufen.

Aus diesem Code kann dann z.Bsp. ein JPG erstellt und auf dem Kassenbon ausgedruckt werden.

Der QR-Code kann auf der Seite https://kassen-qr-code-test.de/ überprüft werden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





FormatDTStamp()

Top Previous Next

FormatDTStamp( DateTime, "Format" )

Wandelt ein DateTime in eine beliebige Zeichenfolge um.



Parameter

DateTime

Format



Rückgabe

String im angegebenen "Format".



Eigenschaften nach Ausführung

keine



Info

Diese Funktion wird intern zur Umwandlung des Datums in das notwendige UTC-Format verwendet.

Sie kann aber auch vom Anwender zur Erstellung eigener Formate verwenden werden.



Für die Formatierungsschablone können definierte Platzhalter übergeben werden:

YYYY = Jahr (2020)

YY = Jahr (20)

MM = Monat (05)

DD = Tag (31)



hh = Stunden (11)

mm = Minuten (33)

ss = Sekunden (22)



Andere Zeichen werden exakt so wie angegeben wieder zurückgegeben.



Beispiel:

FormatDTStamp( DateTime, "YYYY-MM-DDThh:mm:ss.000Z" ) liefert z.Bsp. "2020-05-31T11:13:22.000Z" zurück.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





GetDevicetList()

Top Previous Next

GetDeviceList()

Liefert einen String mit allen Device-IDs eines 3er oder 8er Servers.



Parameter

keine



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung

cDeviceList



Info

Liefert alle DeviceIDs eines 3er oder 8er Servers getrennt durch Semikolon.

Diese DeviceID kann dann TSE_DeviceID zugeordnet werden.



Die Adresse zum Auslesen ist: IP/epson_eposdevice/getDeviceList.cgi

Im Gegensatz zum lokalen Name (local_tse) wird dieser Name aus den Komponenten des TSE Servers per Slot benannt.

Ein Beispiel wäre somit TSE_D74A8C2F27F6F7A4360E69A4CBEC05EA54257_1

Das Property enthält eine mit Semikolon getrennte Liste mit allen verfügbaren TSE-Modulen.



Bitte beachten Sie, dass das Umstecken in einen anderen Slot den TSE-Device-Namen umbenennt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Intern_ConnetOpenLogin

Top Previous Next

Intern_ConnectOpenLogin( Numerisch )

Zentrale Stelle zum Ausführen von Standardfunktionen in einen Stapel von:

TSEConnet()

TSEOpen()

GetChallange

AuthenticateUserForTimeAdmin ( bei Übergabe von 0 als Parameter )

AuthenticateUserForAdmin ( bei Übergabe von 1 als Parameter )





Parameter

Numerisch (0, 1)



Rückgabe

Numerisch, 1 = ok, 0 = nicht ok



Eigenschaften nach Ausführung

keine

Info

Bequemer Stapelaufruf vom Connect bis zum Login.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Intern_LogoutCloseDisconnect

Top Previous Next

Intern_LogoutCloseDisconnect( Numerisch, StringUser, StringClient )

Zentrale Stelle zum Ausführen von Standardfunktionen in einen Stapel von:

Kein Logout, da kein Login erfolgte LogOut ( bei Übergabe von 0 als Parameter )

LogOutUserForTimeAdmin ( bei Übergabe von 1 als Parameter )

LogOutUserForAdmin ( bei Übergabe von 2 als Parameter )



Da es u.U. vorkommen kann, dass eine Anforderung aufgeführt werden muss, die eine höhere Berechtigung erfordert, wurde dieser Call um den eigentlichen User und Client erweitert!

StringUser, Original User

StringClient, Original Client

Verwenden sie diese Funktione NICHT, wenn sie nicht genau wissen, wie diese zu Verwenden ist.

Da dies eine Interne Funktion ist, wird diese der Vollstänigkeit halber aber auch dokumentiert.



TSEClose()

TSEDisconnet()



Parameter

Numerisch (0, 1)



Rückgabe

Numerisch, 1 = ok, 0 = nicht ok



Eigenschaften nach Ausführung

keine

Info

Bequemer Stapelaufruf vom Logout bis zum Disconnect.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





KindOfProcessTypes()

Top Previous Next

KindOfProcessTypes( Numerisch oder Zeichen )

Enumeration der möglichen Processtypen.



Parameter

Numerisch (1, 2, 3) oder Zeichen ja nach Geschäftsvorfall in DSFINV-K V2 beschrieben



Anhang C GV_TYP (Geschäftsvorfalltypen)

Es werden die folgenden Geschäftsvorfalltypen unterschieden:

o Umsatz



o Pfand



o PfandRueckzahlung



o Rabatt



o Aufschlag



o ZuschussEcht



o ZuschussUnecht



o TrinkgeldAG



o TrinkgeldAN



o EinzweckgutscheinKauf



o EinzweckgutscheinEinloesung



o MehrzweckgutscheinKauf



o MehrzweckgutscheinEinloesung



o Forderungsentstehung



o Forderungsaufloesung



o Anzahlungseinstellung



o Anzahlungsaufloesung



o Anfangsbestand



o Privatentnahme



o Privateinlage





Rückgabe

keine



Eigenschaften nach Ausführung

TSE_ProcessType =

1 = Kassenbeleg-V1

2 = AVBestellung-V1

3 = SonstigerVorgang

Info

Der TSE_ProcessType kennzeichnet das Format der in TSE_VorgangsType dargestellten Inhalte und wird in ASCII-Codierung dargestellt

Zur Versionierung wird ein Suffix verwendet (derzeit �V1).

Eine detaillierte Beschreibung zur den ProcessTypes finden Sie auch in der DSFinV-K Dokumentation



Im Stack_StartFinishTransaction() sind erlaubte Parameter 1 oder 3





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





KindOfVorgangsTypes()

Top Previous Next

KindOfVorgangsTypes( Numerisch )

Enumeration der möglichen VorgangsTypen.



Parameter

Numerisch (1-9)



Rückgabe

keine



Eigenschaften nach Ausführung

TSE_VorgangsTypes =

1 = Beleg

2 = AVTransfer

3 = AVBestellung

4 = AVTraining

5 = AVBelegstorno (ab DSFinV-K Version 2.1 gestrichen)

6 = AVBelegabbruch

7 = AVSachbezug

8 = AVSonstige

9 = AVRechnung



Info

Eine detaillierte Beschreibung zur den VorgangsTypen finden Sie auch in der DSFinV-K Dokumentation





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





KindOfSelling()

Top Previous Next

KindOfSelling( Numerisch )

Enumeration der möglichen Zahlarten.



Parameter

Numerisch (1-7)



Rückgabe

Zahlart als String =

"Bar"

"Unbar"

"Keine"

"ECKarte"

"Kreditkarte"

"ElZahlungsdienstleister"

"Guthabenkarte"



Eigenschaften nach Ausführung

keine



Info

Aktuell wird diese Funktion nicht verwendet. Sie kann aber für den Export der DSFinV-K verwendet werden.

Eine detaillierte Beschreibung zur den ProcessTypes finden Sie auch in der DSFinV-K Dokumentation.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





LocalTimeToUTC()

Top Previous Next

LocalTimeToUTC( DateTime )

Konvertiert den übergebenen Datumszeitwert in die eine UTC-Zeit.



Parameter

DateTime



Rückgabe

DateTime im System-Datetime-Format



Eigenschaften nach Ausführung

keine



Info

Siehe auch Stack_UpdateTime().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





SetTransactionObjectStart()

Top Previous Next

SetTransactionObjectStart( Numerisch, DateTime )

Erstellt eine Kopie des Startobjekts, damit sie eine offene Transaktion mit Stack_CancelTransaction() abbrechen können!



Parameter

1. Transaktionsnummer der zu schließenden, noch offenen Transaktion (Integer)

2. DateTime der zu schließenden, noch offenen Transaktion (DateTime)



Rückgabe

1 = OK

0 = Nicht OK



Eigenschaften nach Ausführung



Info

Wird nur dann benötigt, wenn sie eine bereits gestartete, aber noch nicht abgeschlossen Transaktion schließen wollen und anschließend einen Bon mit QR-Code drucken möchten.

Ohne dem Drucken eines QR-Codes reicht das Setzen des Properties: TSE_StartTransactionNumber und anschließenden Aufruf von Stack_FinishTransaction() oder Stack_FinishTransactionDouble() um diese Transaktion zu schließen.



Idealerweise ergänzen sie noch die Werte des erstellen Objekts nach Aufruf dieser Funktion

ihrInstanzName.oTransActionObjectStart.SerialNumber

ihrInstanzName.oTransActionObjectStart.Signature

ihrInstanzName.oTransActionObjectStart.SignatureCounter



* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





SetSelfTest()

Top Previous Next

SetSelfTest()

Prüft, ob ein Selbsttest der TSE notwendig ist. Denn bei einen fehlenden oder abgelaufenen Selbsttest sind keine Aktionen, wie z.B. Export o. Signierung von Transaktionen möglich.

Sie können das Verhalten steuern, indem sie das Property nLastSelfTest auf 1 setzen.

Sollten sie 0 als Wert wählen, müssen sie sich in ihrer Software selbst um das Setzten eines Selbsttests kümmern.



Parameter

keine



Rückgabe

0 o. 1



Eigenschaften nach Ausführung



Info





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





SetTempDir()

Top Previous Next

SetTempDir()

Erstellt im aktuellen User-Temp-Verzeichnis ein Unterverzeichnis mit Namen EasyTSE (C:\Users\<USER>\AppData\Local\Temp\EasyTSE)



Parameter

keine



Rückgabe

keine



Eigenschaften nach Ausführung

cTempDir



Info

Die Methode SetTempDir() wird im INIT der Klasse aufgerufen. Sie befüllt die Eigenschaft cTempDir mit einem eigenen lokalen Ordnernamen.

In diesem werden die erstellen QR-BMPs Dateien abgelegt. Bei Bedarf kann dieser Pfadname nach dem INIT überschrieben werden.

Beim Beenden der Klasse werden alle BMP-Dateien aus diesem Ordner automatisch gelöscht.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





SetUTCTime()

Top Previous Next

SetUTCTime( Numerisch )

Ermittelt und setzt unter Berücksichtigung der Zeitzone sowie der Sommer/Winterzeit die aktuelle UTC-Zeit.



Parameter

0 = Versucht über eine Internetverbindung die UTC-Time zu ermitteln. Falls das nicht funktioniert, wird die lokale PC-Zeit verwendet.

1 = Lokale PC-Zeit verwenden.



Rückgabe

Immer 1



Eigenschaften nach Ausführung

TSE_NewTime = "2020-01-01T05:23:17Z"



Info

Die Funktion prüft zunächst, ob eine Internetverbindung besteht und ruft von einer öffentlichen Stelle die Atomzeit der in cTimeServers hinterlegten NTP-Server ab und wandelt diese in das o.g. Zeitformat um.

Sollte keine Verbindung möglich sein, wird der Zeitstempel vom aktuellen Windows-PC erstellt.



Das Setzen des Zeitstempels ist innerhalb der TSE ein elmentarer Vorgang und muss mind. alle 9 Stunden gesendet werden.

Siehe auch Stack_UpdateTime().



Über den Parameter 1 wird generell die PC-Systemzeit verwendet.

Sollten sie dennoch explizit wünschen dass immer die lokale PC Zeit verwendet wird, und keine Prüfung der Zeitabholung der hinterlegen Zeitserver online erfolgen soll

können sie auch das Property der Zeitserver (cTimeServers) leeren ODER das Property nOnlineTimeSync (Standardwert = 1) mit einer 0 zuweisen.

Unserer Meinung nach, ist die letztere Variante die wohl eleganteste Variante.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





StringToBase64()

Top Previous Next

StringToBase64( "String" )

Wandelt eine hexadezimale Zeichenfolge in eine base64-Zeichenfolge um.



Parameter

Hexadezimale Zeichenfolge.



Rückgabe

base64 codierter String.



Eigenschaften nach Ausführung

keine



Info

Siehe auch Base64ToString().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





StringToSHA256()

Top Previous Next

StringToHash256( "String" )

Erstellt für eine Zeichenfolge den hash-Wert mit einer SHA-256 Kodierung.



Parameter

Hexadezimale Zeichenfolge.



Rückgabe

hash256-codierte Zeichenfolge.



Eigenschaften nach Ausführung

keine



Info

keine





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





StringToSHA384()

Top Previous Next

StringToHash384( "String" )

Erstellt für eine Zeichenfolge den hash-Wert mit einer SHA-384 Kodierung.



Parameter

Hexadezimale Zeichenfolge.



Rückgabe

hash384-codierte Zeichenfolge.



Eigenschaften nach Ausführung

keine



Info

Wird aktuell nicht verwendet!





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





WriteProtocol

Top Previous Next

WriteProtocol()

Erstellt eine Textdatei des Sendeprotokolls in den Temp-Ordner wenn Property cWorkerTexteListe gefüllt ist und wenn Property nWriteProtocol = 1 gesetzt ist auf die Festplatte.

Parameter

keine



Rückgabe

keine



Eigenschaften nach Ausführung

keine



Info

Achtung: Bitte während eines Exports unbedingt die Protokollierung maneull ausschalten!

Da hier riesige Datenmengen während eines Receit´s empfangen werden, was im schlimmsten Fall einen Memory overflow verursachen könnte!

Also nWriteProtocol = 0 setzen und danach ggf. wieder einschalten.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Eigenschaften

Top Previous Next



Eigenschaften zur Kommunikation mit der TSE

Interne Eigenschaften





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE

Top Previous Next

Eigenschaften zur Kommunikation mit der TSE



TSE_AdminPin

TSE_ClientID

TSE_DataID

TSE_DeviceID

TSE_EndDate

TSE_EndTransactionNumber

TSE_Hash

TSE_IP

TSE_MaxWaitMs

TSE_NewClientID

TSE_NewPin

TSE_NewTime

TSE_Port

TSE_ProcessData

TSE_ProcessType

TSE_ProcessTypeData

TSE_Puk

TSE_SecretKey

TSE_SocketID

TSE_StartDate

TSE_StartTransactionNumber

TSE_TimeAdminPin

TSE_TLS

TSE_Type

TSE_UserID

TSE_Vendor





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_AdminPin

Top Previous Next

TSE_AdminPin

AdminPin für Admininstations-Funktionen.



Standardwert

TSE_AdminPin = keiner

(5-stellig)



Info

Muss beim Initialisierung z.Bsp. bei Stack_Setup() einmalig gesetzt werden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_ClientID

Top Previous Next

TSE_ClientID

ID bzw. Name der Kasse die auf die TSE zugreifen möchte.



Standardwert

TSE_ClientID = keiner



Info

Nur Kassen die in der TSE registriert wurden, können Transaktionen ausführen.

Die ClientID kann bei der Initialisierung vorab in der Eigenschaft TSE_NewClientID gesetzt und dann der Stack_Setup() ausgeführt werden.

Alternativ können die Kassen auch über die Funktion Stack_NewClientID() hinzugefügt werden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_DataID

Top Previous Next

TSE_DataID

Rückgabe einer ID nach dem Öffnen der TSE



Standardwert

TSE_DataID = keiner



Info

Speichert die Rückgabe-ID nach einem TSEOpen().

Diese ID könnte für einen Reconnect verwenden werden. Bislang wird diese Funktion in der Easy-TSE nicht untersützt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_DeviceID

Top Previous Next

TSE_DeviceID

Interner Name der TSE, ist einer USB-TSE und bei einem Drucker mit TSE ist immer local_tse.



Bei einen TSE Server beginnt die DeviceID mit TSE_ danach kommt die Seriennummer xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx und abschließend die Slotnummer/Steckplatz am Server _1

Beispiel: TSE_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx_1



Ändern sie NIEMALS den Namen der DeviceID auf einen, für sie passenden Namen, denn dadurch schlagen alle Logins und Verifizierungen fehlt!

Standardwert

TSE_DeviceID = local_TSE



Info

Bei den Epson Servern muss die DeviceID aus dem Servermodul mit GetTSEClientList() ausgelesen werden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_EndDate

Top Previous Next

TSE_EndDate

Ende-Datum für Zeitraum beim Exportieren der Daten von der TSE.



Standardwert

TSE_EndDate = keiner



Info

Datum wird beim Stack_ExportFilteredPeriodOfTime() verwendet.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_EndTransactionNumber

Top Previous Next

TSE_EndTransactionNumber

Ende-Transaktionsnummer für Intervall beim Exportieren der Daten von der TSE.



Standardwert

TSE_EndTransactionNumber = keiner



Info

Wird beim Kommando ExportFilteredByTransactionNumberInterval verwendet.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_Hash

Top Previous Next

TSE_Hash

Enthält für eine Zeichenfolge den hash-Wert mit einer SHA-256 Kodierung.



Standardwert

TSE_Hash = keiner



Info

Diese Eigenschaft wird in allen Stack-Funktionen aus der TSE-Challenge und dem TSE_SecretKey() berechnet.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_IP

Top Previous Next

TSE_IP

IP-Adresse der TSE-USB oder des TSE-Druckers.



Standardwert

TSE_IP = 127.0.0.1



Info

TSE-USB = 127.0.0.1 oder localhost

TSE-Drucker = 192.168.###.###





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_MaxWaitMs

Top Previous Next

TSE_MaxWaitMs

Maximale Wartezeit bei Verbindungsproblemen mit der TSE in Millisekunden.



Standardwert

TSE_MaxWaitMs = 40000



Info

Wartezeit, falls die TSE nicht betriebsbereit ist oder anderweitig benutzt wird.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_NewAdminPin

Top Previous Next

TSE_NewAdminPin

Setzen einer neuen Pin für den Admin.



Standardwert

TSE_NewAdminPin = keiner



Info

TSE_AdminPin wird gefüllt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_NewClientID

Top Previous Next

TSE_NewClientID

ID bzw. Name einer neuen Kasse die angemeldet werden soll.



Standardwert

TSE_NewClientID = keiner



Info

Nur Kassen die in der TSE registriert wurden, können Transaktionen ausführen.

Es können bis zu 100 Kassen in der TSE registriert werden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_NewPin

Top Previous Next

TSE_NewPin

Setzen einer neuen Pin für den Admin.



Standardwert

TSE_NewPin = keiner



Info

Über die Kommandos UnblockUserForAdmin und UnblockUserForTimeAdmin kann eine neue Pin vergeben werden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_NewPuk

Top Previous Next

TSE_NewPuk

Setzen einen neuen Puk der TSE.



Standardwert

TSE_NewPuk = keiner



Info

TSE_Puk wird gefüllt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_NewTime

Top Previous Next

TSE_NewTime

Enthält einen aktuellen Zeitstempel in Form einer UTC-Time.



Standardwert

TSE_NewTime = keiner



Info

Für die Synchronisation der TSE ist es notwendig, die TSE-Zeit zu setzen. Dafür kann z.Bsp. der Stack_UpdateTime() verwendet werden.

Das Feld wird intern auch in der Funktion SetUTCTime() gesetzt.

Außerdem gibt das Kommando GetStorageInfo, Informationen wie hasValidTime zur Verfügung.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_NewTimeAdminPin

Top Previous Next

TSE_NewTimeAdminPin

Setzen einer neuen Pin für den TimeAdmin.



Standardwert

TSE_NewTimeAdminPin = keiner



Info

TSE_TimeAdminPin wird gefüllt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_Port

Top Previous Next

TSE_Port

Verwendeter Port der TSE.



Standardwert

TSE_Port = 8009



Info

TSE_Port = "8009"

TSE_Port = "8143" (SSL)





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_ProcessData

Top Previous Next

TSE_ProcessData

Enthält bei einer Transaktion die base64-codierten Daten.



Standardwert

TSE_ProcessData = keiner



Info

Beispiel für einen komplexen Wert: Beleg^75.33_7.99_0.00_0.00_0.00^10.00:Bar_5.00:Bar:CHF_5.00:Bar:USD_64.30:Unbar

Die zu übergebenden Daten sind in 3 Blöcke, getrennt durch ^ aufgeführt.



Block 1 = Vorgangstyp (Beleg)

Enthält einen definierten Vorgangstyp, siehe auch TSE_ProcessTypeData.



Block 2 = Brutto-Steuerumsätze (75.33_7.99_0.00_0.00_0.00)

Für alle numerische Werte folgende Darstellungsregeln:



1. Als Dezimaltrennzeichen ist �.� (Unicode U+002E) zu verwenden.



2. Tausendertrennzeichen sind nicht zulässig



3. Exponentialschreibweise ist nicht zulässig



4. Es muss mindestens eine Stelle vor dem Dezimaltrennzeichen angegeben werden



5. Negativen Werten wird ein �-� (Unicode U+002D) vorangestellt



6. Positiven Werten wird KEIN �+� vorangestellt



7. Führende Nullen werden abgeschnitten, sofern es nicht die erste Stelle vor dem Dezi-maltrennzeichen betrifft.





Es gilt folgende Reihenfolge der Steuersätze:



1. Allgemeiner Steuersatz (19%)



2. Ermäßigter Steuersatz (7%)



3. Durchschnittsatz (§24(1)Nr.3 UStG) (10.7%)



4. Durchschnittsatz (§24(1)Nr.1 UStG) (5.5%)



5. 0%





Block 3 = Zahlungen (10.00:Bar_5.00:Bar:CHF_5.00:Bar:USD_64.30:Unbar)

Bei den Zahlungen wird nur zwischen �Bar� und �Unbar� unterschieden. Bei mehreren Zahlungsarten sind diese entsprechend zu akkumulieren.

Die Zahlungen werden durch das Zeichen �_� (Unterstrich, Unicode U+005F) verkettet.



Jede Zahlung wird wie folgt dargestellt:

<Betrag>:<Zahlungsart>:<Währung>

Trennzeichen ist der Doppelpunkt (Unicode U+003A).

Der Zahlungsbetrag wird immer mit exakt zwei Nachkommastellen angegeben, alle anderen Nachkommastellen werden ggf. abgeschnitten.

Die Zahlungsart kann entweder �Bar� oder �Unbar� sein. Die Währung wird nur verwendet, wenn diese nicht EUR ist.

Die Währung muss dem dreistelligen Währungscode nach ISO 4217 entsprechen.

Zahlungen von 0.00 müssen entfallen.

Damit ergeben sich z. B. folgende Darstellungsmöglichkeiten:



Betrag

Zahlungsform

Währung

Darstellung



1

Bar

EUR

1.00:Bar



1.99

Unbar

EUR

1.99:Unbar



-2

Bar

EUR

-2.00:Bar



3.5

Bar

CHF

3.59:Bar:CHF



4

Unbar

USF

4.00:Unbar:USD





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_ProcessType

Top Previous Next

TSE_ProcessType

Beschreibt bei einer Transaktion die enthaltenen Daten in ProcessData.



Standardwert

TSE_ProcessType = 1



Info

Siehe auch TSE_ProcessData, TSE_VorgangsType sowie die Funktionen Stack_StartTransaction(), Stack_UpdateTransaction() und Stack_StartFinishTransaction().



Mögliche Zuweisungen lt. DSFinV-K:

TSE_ProcessType =

1 = Kassenbeleg-V1

2 = Bestellung-V1

3 = SonstigerVorgang





* * *



Nicht zu verwechseln mit TSE_VorgangsType, da diese Kombinationsmöglichkeiten zu TSE_ProcessType ermöglicht.

TSE_VorgangsType =

1 = Beleg

2 = AVTransfer

3 = AVBestellung

4 = AVTraining

5 = AVBelegstorno (ab DSFinV-K Version 2.1 gestrichen)

6 = AVBelegabbruch

7 = AVSachbezug

8 = AVSonstige

9 = AVRechnung





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_Puk

Top Previous Next

TSE_Puk

Personal Unlocking Key



Standardwert

TSE_Puk = keiner

(6-stellig)



Info

Muss beim Initialisierung z.Bsp. bei Stack_Setup() einmalig gesetzt werden.

Wird für die Kommandos UnblockUserForAdmin und UnblockUserForTimeAdmin benötigt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_SecretKey

Top Previous Next

TSE_SecretKey

Secret key für die Kalkulation des hash256-Werts für die User- und Client-Authentifikation.



Standardwert

TSE_SecretKey = EPSONKEY

(8-stellig)



Info

Der TSE_SecretKey kann nach der Initialisierung z.Bsp. mit der Funktion Stack_RegisterSecretkKey() erfolgen.

Außerdem wird der TSE_SecretKey beim Authentifizieren des Admins und TimeAdmins sowie bei allen Stack-Funktionen zur Berechnung des hash256-Wertes benötigt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_SocketID

Top Previous Next

TSE_SocketID

Enhält die Rückgabe-ID nach einer erfolgreichen TCP/IP-Socket-Verbindung.



Standardwert

TSE_SocketID = keiner



Info

Wird gefüllt bei erfolgreicher Verbindung via TSEConnect() oder TSEConnectOpenSend().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_StartDate

Top Previous Next

TSE_StartDate

Start-Datum für Zeitraum beim Exportieren der Daten von der TSE.



Standardwert

TSE_StartDate = keiner



Info

Datum wird beim Stack_ExportFilteredPeriodOfTime() verwendet.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_StartTransactionNumber

Top Previous Next

TSE_StartTransactionNumber

Start-Transaktionsnummer für Intervall beim Exportieren der Daten von der TSE.



Standardwert

TSE_StartTransactionNumber = keiner



Info

Wird beim Kommando ExportFilteredByTransactionNumberInterval verwendet.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_TimeAdminPin

Top Previous Next

TSE_TimeAdminPin

TimeAdminPin für alternativen User zum Adiministrator.



Standardwert

TSE_TimeAdminPin = keiner

(5-stellig)



Info

Bei einigen TSE-Kommandos wird der TimeAdmin anstelle des Admins verwendet.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_TLS

Top Previous Next

TSE_TLS

Aktiviert das TLS-Protokoll (Transport Layer Security) , früher SSL.



Standardwert

TSE_TLS = 0



Info

Wird bei verschlüsselten Socket-Verbindungen benötigt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_Type

Top Previous Next

TSE_Type

"Storage Type", wird von jedem Kommando benötigt.



Standardwert

TSE_Type = TSE



Info

keine





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_UserID

Top Previous Next

TSE_UserID

Administrator für Administrator-Aufgaben oder ClientID für TimeAdmin-Aufgaben.



Standardwert

TSE_UserID = Administrator



Info

keine





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_Vendor

Top Previous Next

TSE_Vendor

"Vendor Name", wird von jedem Kommando benötigt.



Standardwert

TSE_Vendor = TSE1



Info

keine





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE_VorgangsType

Top Previous Next

TSE_VorgangsType

Enthält bei einer Transaktion den Vorgangstyp.



Standardwert

TSE_VorgangsType = 8



Info

Nachfolgende aufgeführte Vorgangstypen sind erlaubt und können über die Funktion KindOfVorgangsTypes() im Klartext zurückgegeben werden.

1 = Beleg

2 = AVTransfer

3 = AVBestellung

4 = AVTraining

5 = AVBelegstorno (ab DSFinV-K Version 2.1 gestrichen)

6 = AVBelegabbruch

7 = AVSachbezug

8 = AVSonstige

9 = AVRechnung



Siehe auch TSE_ProcessType, TSE_ProcessData sowie die Funktionen Stack_StartTransaction(), Stack_UpdateTransaction() und Stack_StartFinishTransaction().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Intern

Top Previous Next

Interne Eigenschaften



Die einzelnen Properties entnehmen sie bitte der Liste.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cCertificate

Top Previous Next

cCertificate

Enthält das Zertifikat der TSE



Standardwert

cCertificate = leer



Info

Der Abruf des TSE-Zertifikats.

Der aufruf des Stack: Stack_GetLogMessageCertificate() füllt das Property.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nCheckDependencies

Top Previous Next

nCheckDependencies



Es empfiehlt sich nicht, diesen Standardwert zu verändern,

da EasyTSE sich um die Prüfung der benötigten Properties zur Ausführung eines Stacks kümmert und ggf. fehlende Eigenschaften in der Fehlerprotokollierung erfasst.

Durch das Setzten des Wertes auf 0, können Sie ggf. etwas Zeit einsparen, müssen aber selbst sicherstellen, dass alle benötigten Properties gesetzt sind.

Standardwert

nCheckDependencies = 1



Info:

Betrifft alle Stacks_xxxx





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cDataTrenner

Top Previous Next

cDataTrenner

Enthält den Trenner als String, (CHR(13), \n, \r, oder was auch immer) der einzelnen Signierungspositionen.



Beispiel für einen Zusammensetzung des tcDataString, der an den Stack Gastro_Stack_StartFinishTransaction übergeben wird:

EasyTSEInstanzName.cDataTrenner = "\n"

"1;Coloa;3.50" + EasyTSEInstanzName.cDataTrenner + "2.5;Schnitzel m. Pommes;12.50" + EasyTSEInstanzName.cDataTrenner ... usw.

Standardwert

cDataTrenner = CHR(13)



Info

Da nun im Stack Gastro_Stack_StartFinishTransaction mehrere Positionen übergeben werden können, müssen die einzelnen Positionen durch ein eindeutiges Trennzeichen verbunden werden. Hierbei ist zwinend darauf zu achten, dass das von ihnen definierte Trennzeichen NICHT in der Artikelbezeichnung vorkommt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cDeviceList

Top Previous Next

cDeviceList

Enthält eine, mit Semikolon getrennte Liste mit Device-IDs eines 3er oder 8er Servers.



Standardwert

cDeviceList = keiner



Info

Der Abruf der DeviceIDs eines Servers kann mit GetDeviceList() erfolgen.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cErrorList

Top Previous Next

cErrorList

Enthält eine Liste von aufgetretenen Fehlermeldungen.



Standardwert

cErrorList = keiner



Info

Ob Fehlermeldungen automatisch angezeigt oder unterdrückt werden um diese selbst anzuzeigen lässt sich mit nShowErrorList einstellen.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cExportDir

Top Previous Next

cExportDir

Variable zum Setzen des Ausgabeverzeichnisses bei einen Datenexport.



Standardwert

cExportDir = Installationsverzeichnis + "TSE_ExportedData"

Die Verzeichnistrenner sind als einfaches \ anzugeben.

Als Beispiel.: C:\Test\Export\Test



Info

Kann zur Laufzeit überschrieben werden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cLastProcessData

Top Previous Next

cLastProcessData

Enthält die letzte, erstellte ProcessData in Base64 codiert



Standardwert

cLastProcessData = leer



Info

Folgende Stack-Aufrufe füllen das Property (automatisch):



Stack_StartFinishTransaction()

Stack_StartFinishTransactionDouble()

Stack_FinishTransaction()

Stack_FinishTransactionDouble()

Stack_UpdateTransaction()

Gastro_Stack_StartFinishTransaction()



Dieses Property macht dann Sinn abzurufen, wenn sie nach einen oben aufgeführten StackCall die ProcessData in ihre Datenbank übernehmen/speichern wollen.

Um dieses Property wieder lesebar zu machen können sie die interne Funktion Base64ToString() verwenden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cLastQrProcessData

Top Previous Next

cLastQrProcessData

Enthält die letzte, erstellte ProcessData in Base64 codiert für den QrCode



Standardwert

cLastQrProcessData = leer



Info

Folgende Stack-Aufrufe füllen das Property (automatisch):



Stack_StartFinishTransaction()

Stack_StartFinishTransactionDouble()

Stack_FinishTransaction()

Stack_FinishTransactionDouble()

Stack_UpdateTransaction()

Gastro_Stack_StartFinishTransaction()



Dieses Property macht dann Sinn abzurufen, wenn sie nach einen oben aufgeführten StackCall die ProcessData zum Ausdruck des QrCodes in ihre Datenbank übernehmen/speichern wollen.

Um dieses Property wieder lesebar zu machen können sie die interne Funktion Base64ToString() verwenden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cQRCode

Top Previous Next

cQRCode

Enthält eine QR-Code als String.



Standardwert

cQRCode = keiner



Info

Der QR-Code wird automatisch beim Stack_StartFinishTransaction() erstellt und kann für den Ausdruck auf dem Kassenbeleg verwendet werden.

Erzeugt wird der Code über die interne Funktion BuildQRCode().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cQRCodePath

Top Previous Next

cQRCodePath

Enthält den Pfad und Dateinamen des zuletzt erstellen QR-Code-BMPs.



Standardwert

cQRCodePath = keiner



Info

cQRCodePath wirdd in BuildQRBMP() für die Erstellung des BMPs verwendet. Sie kann im Anschluss für den Ausdruck im eigenen Report verwendet werden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cQRName

Top Previous Next

cQRName

Mit dieser Eigenschaft können sie den zu erstellenenden und aktuellen QRCode einen eigenen Namen geben.



Standardwert

cQRName = "" (also leer)



Info

Dateiname für QRCode.bmp, sollte z.B. mit der aktuellen Bonnummer gefüllt sein.

Wenn leer, wird ein zufälliger Dateiname erzeugt (Standard)

Muss vor jeden Aufruf von Stack_StartFinishTransaction(), Gastro_Stack_StartFinishTransaction(), Stack_StartTransaction(), Stack_FinishTransaction() neu befüllt werden,

da nach Abschluss genannter Stacks das Property wieder resetten wird.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cRegisteredClientList

Top Previous Next

cRegisteredClientList

Enthält eine, mit Semikolon getrennte Liste der registrierten Kassen (Clients).



Standardwert

cRegisteredClientList = keiner



Info

Der Abruf der registrierten Kassen (Clients) erfolgt über TSESend() und das Kommando GetRegisterdClientList oder besser über den Stack_GetRegisteredClientList().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cSNfromPublicKey

Top Previous Next

cSNfromPublicKey

Enthält eine die Seriennummer der TSE errechnet aus dem PublicKey.



Standardwert

cSNfromPublicKey = keiner



Info

Die Seriennummer wird aus dem PublicKey mit der Methode BuildSNfromPublicKey() ermittelt und kann für den Ausdruck und die Anmeldung beim Finanzamt verwendet werden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cStartedTransactionList

Top Previous Next

cStartedTransactionList

Enthält eine, mit Semikolon getrennte Liste, der gestarteten und noch nicht abgeschlossenen Transaktionen.



Standardwert

cStartedTransactionList = keiner



Info

Der Abruf der gestarteten Transaktionen erfolgt über TSESend() und das Kommando GetStartedTransactionList oder besser über den Stack_GetStartedTransactionList().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cTempDir

Top Previous Next

cTempDir

Enthält den Pfadnamen für die Ablage von QR-Code-BMPs.



Standardwert

cTempDir = C:\Users\<USER>\AppData\Local\Temp\EasyTSE



Info

cTempDir wird automatisch beim INIT der Klasse gesetzt und kann vom Anwender im Anschluss überschrieben werden.

Die Eigenschaft wird in SetTempDir() gesetzt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cTimeServers

Top Previous Next

cTimeServers

Enthält eine, mit Semikolon getrennte Liste, von NTP-Synchronisationsserver zum Holen und Setzen der Atomzeit im UTC-Format.



Standardwert

cTimeServers = "time.nist.gov;time.windows.com;"



Info

Für das regelmäßige Setzen der UTC-Zeit werden die in cTimeServers hinterlegten NTP-Server abgefragt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





cWorkerTexteListe

Top Previous Next

cWorkerTexteListe

Enthält eine Liste, des Sendeprotokolls





Standardwert

leer (string)

Info

Speichert die Protokollierung von Property cWorkerTexteListe, wenn Property nWriteProtocol = 1 in den TempOrdner (cTempDir)





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nAutoSetup

Top Previous Next

nAutoSetup

Es besteht auch eine Abhänigkeit zum Stack_Setup().

Sollten sie das Property nAutoSetup auf 1 setzen, übernimmt EasyTSE automatisch bei einen UNINITIALIZED-State der TSE den Aufruf des Stack_Setup().

Die ist natürlich sehr komfortabel und praktisch.

Standardwert

nAutoSetup = 0



Info





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nConnected

Top Previous Next

nConnected

Diese Eigenschaft wird 1, wenn ein TSEConnect() erfolgreich ausgeführt wurde.



Standardwert

nConnected = 0



Info

nConnected wird bei TSEReset() auf den Standardwert gesetzt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nExportArchiveFinalize

Top Previous Next

nExportArchiveFinalize

Das Setzen von nExportArchiveFinalize = 1 bewirkt, dass nach einen kompletten, erfogreichen Daten-Komplettexport die Tar Dateien auf der TSE gelöscht werden!

Setzt aber auch gleichzeit voraus, dass der Benutzer die exportierten Daten aufbewahrt! (jederzeit verfügbar, doppelt gesichert, usw.)



Standardwert

nExportArchiveFinalize = 0



Info

In Verbindung mit Stack_ExportArchiveData(), Stack_ExportFilteredByPeriodOfTime() oder Stack_ExportFilteredByTransactionNumber().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nHoldConnection

Top Previous Next

nHoldConnection

Wenn diesem Property der Wert 1 zugewiesen wird, können sie nun die Stacks:

Stack_StartTransaction

Stack_UpdateTransaction �. beliebig oft nacheinander

Stack_FinishTransaction

aufrufen und dabei die initiale Erstverbindung von Stack_StartTransaction() nutzen, ohne dass sich diese ständig öffnet und wieder schließt.



Der Vorteil liegt hier ganz klar in der Performance.

Denn es wird nun nicht mehr bei jeden der genannten Stacks die Verbindung geöffnet und dann wieder geschlossen, sondern beim Stack_StartTransaction wird die Verbindung geöffnet und erst beim Beenden von Stack_FinishTransaction wird die Verbindung wieder geschlossen.

Bei Verwendung dieser Variante MUSS aber auch ein konsequent die Reihenfolge: Stack_StartTransaction, Stack_UpdateTransaction �. beliebig oft nacheinander und Stack_FinishTransaction angewandt werden.

Bedeutet: Eine eröffnete Transaktion (mit Stack_StartTransaction) muss auch unmittelbar danach beendet werden!



Sollten sie langanhaltende Transaktionen verwenden, verwenden sie bitte die Variante: Gastro_Stack_Connect(), Gastro_Stack_StartFinishTransaction(), Gastro_Stack_Disconnect(). In diesen Stacks hat das Property keinen Einfluss und keine Auswirkung.



Alternativ können sie aber auch die ursprüngliche Variante bei nHoldConnection = 0 verwenden.



Standardwert

nHoldConnection = 0



Info

In Verbindung mit Stack_StartTransaction(), Stack_UpdateTransaction(), Stack_FinishTransaction()





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nLastSelfTest

Top Previous Next

nLastSelfTest



Sollten sie das Property nLastSelftest auf 0 setzen, übernimmt EasyTSE keine automatische Prüfung, ob ein Selbsttest der TSE notwendig ist.

Ein fehlender Selbsttest kann zur Folge haben, dass z.B. eine Transaktion nicht ausgeführt werden kann.

Setzten sie also nLastSelftest auf 0, müssen sie sich selbst um einen gültigen und validen Selbsttest in ihrer Software kümmern.

Standardwert

nLastSelftest = 1



Info





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nMaxReadIdleMs

Top Previous Next

nMaxReadIdleMs

Die maximale Anzahl von Millisekunden die auf einen Socket-Lesevorgang gewartet werden kann, wenn keine zusätzlichen Daten anstehen.

Um unbegrenzt zu warten setzen Sie diese Eigenschaft auf 0.



Standardwert

nMaxReadIdleMs = 40000



Info

Wird von der Chilkat-Klassenbibliothek benutzt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nMaxSendIdleMs

Top Previous Next

nMaxSendIdleMs

Die maximale Anzahl von Millisekunden die bei einer Socket-Schreiboperation gewartet werden muss, bis der Socket beschreibbar wird.

Um unbegrenzt zu warten setzen Sie diese Eigenschaft auf 0.



Standardwert

nMaxSendIdleMs = 40000



Info

Wird von der Chilkat-Klassenbibliothek benutzt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nOpenDirAfterExport

Top Previous Next

nOpenDirAfterExport

Das Setzen von nOpenDirAfterExport = 1 bewirkt, dass nach einen erfolgreichen Datenexport das definierte Ausgabeverzeichnis im Windowsexplorer geöffnet wird.



Standardwert

nOpenDirAfterExport = 0



Info

In Verbindung mit Stack_ExportArchiveData(), Stack_ExportFilteredByPeriodOfTime() oder Stack_ExportFilteredByTransactionNumber().





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nOnlineTimeSync

Top Previous Next

nOnlineTimeSync

EasyTSE verfügt über einen komfortablem Zeitstempelabgleich über das Internet (SetUTCTime()).

Wenn jedoch ein PC permanent offline ist, mach die wichtige Prüfung auf einen validen Zeitstempel keinen Sinn und kostet ggf. etwas Überprüfungszeit.

dies können sie nun komplett umgehen und eliminieren, indem sie dieses Property nOnlineTimeSync auf 0 setzen.

Nun wird bei jeden Versuch, die Zeit abzugleichen, die lokale PC-Zeit verwendet.

Dies kann natürlich, wenn die Benutzer die PC-Zeit o. Datum verstellen zu abweichenden und falschen Zeitangaben führen!



Standardwert

nOnlineTimeSync = 1



Info





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nOpened

Top Previous Next

nOpened

Diese Eigenschaft wird 1, wenn ein TSEOpen() erfolgreich ausgeführt wurde.



Standardwert

nOpened = 0



Info

nOpened wird bei TSEReset() auf den Standardwert gesetzt.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nQRCodeMaker

Top Previous Next

nQRCodeMaker

Möglichkeit QrCodes für den Ausdruck über EasyTSE zu erstellen.

Bei nQRCodeMaker = 0 wird keine QRCode erstellt und auch kein QRCode Pfad gefüllt.

Dies kann dann zum Einsatz kommen, wenn sie selbst sich um die Erstellung des QRCodes kümmern wollen.



Standardwert

nQRCodeMaker = 1



Info





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nQRCodeResize

Top Previous Next

nQRCodeResize

Möglichkeit den erstellten QrCode über das OCX zu resizen.

Funktion wurde ab Version 2021.3.3 entfernt, da diese keiner nutzt



Standardwert

nQRCodeResize = 0



Info

Ermöglicht in Verbindung mit nQRCodeResizeSize die Größenänderung eines QR-BMPs.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nQRCodeResizeSize

Top Previous Next

nQRCodeResizeSize

Größe des QR-Codes nach dem resizen.

Funktion wurde ab Version 2021.3.3 entfernt, da diese keiner nutzt



Standardwert

nQRCodeResizeSize = 200



Info

Ermöglicht in Verbindung mit nQRCodeResize die Größenänderung eines QR-Codes





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nQRCodeTwips

Top Previous Next

nQRCodeTwips

Enthält die reservierten Puffer für die Erstellung der QR-Code-BMPs



Standardwert

nQRCodeTwips = 7000



Info

nQRCodeTwips wird bei der Erstellung des QR-Code-BMPs für die Größe der Ausgabe in BuildQRBMP() benötigt.

Bei schlechter Bildqualität kann der Wert nach dem INIT der Klasse verändert werden.

Da im QR-code eine große anzahl von Bytes dargestellt werden müssen (mind. 4500) sollte der Wert auf jeden Fall 7500 betragen.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nQRDeleteBMP

Top Previous Next

nQrDeleteBMP

Mit dieser Eigenschaft können sie das automatische Löschen von erstellten QrCode.BMP´s steuern.



Standardwert

nQRDeleteBMP = 0



Info

Nachdem der Destroy der Klasse ausgeführt wird, werden alle erstellten QrCodes (.BMP) aus dem Tempordner gelöscht, was eigentlich Sinn macht, wenn sie den Wert auf 1 gesetzt haben.

Wenn der Wert von nQRDeleteBMP = 0 ist, dann bleiben alle OR-BMP Dateien erhalten.

nQRDeleteBMP = 1, löscht alle Bitmaps der QRCodes und verschiebt diese in den Papierkorb

nQRDeleteBMP = 2, löscht alle Bitmaps der QRCodes endgültig





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nTSEError

Top Previous Next

nTSEError

Mit dieser Eigenschaft werden evtl. Fehler der TSE erfasst

0 = kein Fehler, alles ok (nConnected = 1, nOpend = 1)

1 = Keine TSE vorhanden

2 = TSE Daten hinterlegt jedoch IP (Socket) nicht erreichbar (nConnected = 0)

3 = TSE Daten hinterlegt jedoch nicht erreichbar evtl. ausgefallen (nConnected = 1, nOpend = 0)

4 = TSE Zertifikat abgelaufen

5 = TSE Device nicht gefunden

6 = TSE in Benutzung

Standardwert

nTSEError = 0 (keine TSE vorhanden)



Info

Der Status wird automatisch bei versch. Vorgängen der TSE gesetzt, die sie dann bequem abrufen können.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nUpdateTimeMin

Top Previous Next

nUpdateTimeMin

Mit dieser Eigenschaft können Sie selbst bestimmen, wann spätestens das Setzen eines neuen Zeitstempels erfolgen soll.

Die Angabe der Zeit muss in Minuten erfolgen.

Beachten Sie bitte, dass der maximal erlaubte Wert 55 Minuten sein darf!

Standardwert

nUpdateTimeMin = 0 (somit wird der Wert 30 Minuten verwendet)



Info

Um evtl. Signaturlücken zu vermeiden macht es Sinn einen Wert von 30 Min. zu verwenden.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





nWriteProtocol

Top Previous Next

nWriteProtocol

Speichert die Protokollierung von Property cWorkerTexteListe, wenn Property nWriteProtocol = 1 in den TempOrdner (cTempDir) durch den Aufruf von WriteProtocol()

Standardwert

nWrirteProtocol = 0 (keine Protokollierung)





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





oResult

Top Previous Next

oResult

Speichert die Rückgabe eines beliebigen Kommandos in Form eines Objekts.



Standardwert

oResult = NULL



Info

oResult wird bei TSEReset() auf den Standardwert gesetzt.

Die von der TSE zurückgelieferten Informationen werden zum Speichern in oResult automatisch durch Funktionen der nfJsonRead-Funktionen konvertiert.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Rückgabewerte als Properties

Top Previous Next

Es gibt Programmiersprachen, die anscheinend keine Rückgabeobjekte vollständig auswerten können.

Das ist zwar extrem selten, aber wie die Praxis gezeigt hat: aktuell.



Um hier eine ausreichende Unterstützung zu bieten können sie nach Aufruf der jeweiligen Stacks:

Stack_StartFinishTransaction, Stack_StartFinishTransactionDouble, Stack_FinishTransaction, Stack_FinishTransactionDouble, Stack_UpdateTransaction



Die Rückgabewerte der jeweiligen Stacks als Properties auslesen:



Response_StartLogTime (String/VarChar) / = Wertangabe als UTC Time!

Response_StartTransactionNumber (Integer)

Response_StartserialNumber (String/VarChar)

Response_Startsignature (String/VarChar)

Response_StartSignatureCounter (Integer)



Response_FinishLogTime (String/VarChar) / = Wertangabe als UTC Time!

Response_FinishSignatureCounter (Integer)

Response_Finishsignature (String/VarChar)



Response_UpdateLogTime (String/VarChar) / = Wertangabe als UTC Time!

Response_UpdateSignature (String/VarChar)

Response_UpdateSignatureCounter (Integer)





Info

Sollte ihre Programmiersprache (was eigentlich fast jede kann) Objekte auslesen können, verwenden sie bitte diese Variante NICHT.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Kommandos der TSE

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Konfiguration

Top Previous Next

Konfigurations-Kommandos





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





ChangePinForAdmin

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





ChangePinForTimeAdmin

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





ChangePuk

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





DeRegisterClient

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





DisableSecureElement

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





FactoryReset

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





GetTimeOutInterval

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





RegisterClient

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





RegisterSecretkey

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





SetTimeOutInterval

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





SetUp

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





SetUpForPrinter

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





UpdateTime

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Authentifikation

Top Previous Next

Authentifikations-Kommandos





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





AuthenticateHost

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





AuthenticateUserForAdmin

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





AuthenticateUserForTimeAdmin

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





DeauthenticateHost

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





GetChallenge

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





LogOutAdmin

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





LogOutTimeAdmin

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





UnblockUserForAdmin

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





UnblockUserForTimeAdmin

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Transaktion

Top Previous Next

Transaktions-Kommandos





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





FinishTransaction

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





GetLastTransactionResponse

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





StartTransaction

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





UpdateTransaction

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Export

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





ArchiveExport

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





CancelExport

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





ExportFilteredByPeriodOfTime

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





ExportFilteredByTransactionNumber

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





ExportFilteredByTransactionNumberInterval

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





FinalizeExport

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





GetExportData

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





GetLogCer

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Information

Top Previous Next

Informations-Kommandos





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





GetAuthenticatedUserList

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





GetRegisteredClientList

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





GetStartedTransactionList

Top Previous Next





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





GetStorageInfo

Top Previous Next

GetStorageInfo

Als Kommando für TSESend() oder TSEConnectOpenSend() und liefert detaillierte Informationen von der TSE.



Eigenschaften

TSE_Type

TSE_Vendor



Eigenschaften nach Ausführung

oResult

oGetStorageInfo



Info

Das Kommando GetStorageInfo liefert nachfolgende Informationen die über oResult oder oGetStorageInfo ausgelesen werden können.



Syntax: <<EpsonTSE>>.oGetStorageInfo.Output.TSEInformation.<<Eigenschaft>>





Quelle: JSON specification_GermanFiscal_draft.pdf





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Anhang

Top Previous Next

Anhang





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Beispiele für Visual Foxpro

Top Previous Next

Beispiele für Visual Foxpro





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Globale Anwendungsklasse instanziieren

Top Previous Next

Globale Anwendungsklasse instanziieren

Erstellt eine Anwendungsklasse zum globalen Zugriff innerhalb der Applikation.



Code

*-- Globale Variable definieren

*-- Eigene Klasse oder Klasse am globalen Anwendungsobjekt (goApp.oTSE)

PUBLIC goTSE



*-- Tabelle mit TSE-Verwaltung öffnen

USE TSE IN 0

SELECT TSE



*-- Aktuelle Kasse/Station in der TSE-Verwaltung suchen

LOCATE FOR ...

IF FOUND()

*-- Klasse mit den wichtigsten Properties befüllen

goTSE = CREATEOBJECT( "EpsonTSE" )

goTSE.TSE_IP = TSE.IP

goTSE.TSE_Port = TSE.PORT

goTSE.TSE_Puk = TSE.PUK

goTSE.TSE_AdminPin = TSE.ADMINPIN

goTSE.TSE_TimeAdminPin = TSE.TIMEADMINPIN

goTSE.TSE_SecretKey = TSE.SECRETKEY

ELSE

*-- TSE-Daten nicht vorhanden

goTSE = CREATEOBJECT( "Empty" )

MESSAGEBOX( "TSE konnte nicht zugeordnet werden!", 48, "Info" )

ENDIF

USE IN TSE





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





GetStorageInfo

Top Previous Next

GetStorageInfo

Holt sich Informationen von der TSE mit einem Funktionsaufruf.



Code

LOCAL loTSE, lnReturn



*-- Klasse initialisieren

loTSE = CREATEOBJECT( "EpsonTSE" )



*-- Verbinden, öffnen, senden

lnReturn = loTSE.TSEConnectOpenSend( "GetStorageInfo" )



*-- Message für Rückgabe oder Fehler

DO CASE

CASE lnReturn = 1

MESSAGEBOX( "TSE-Status : " + loTSE.oGetStorageInfo.Output.TSEInformation.tseInitializationState, 64, loTSE.cName )



CASE !EMPTY( loTSE.cErrorList )

MESSAGEBOX( loTSE.cErrorList, 64, loTSE.cName )

ENDCASE



RELEASE loTSE, lnReturn





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE-Verwaltung

Top Previous Next

TSE-Verwaltung

Beispielmasken zur eigenen Verwaltung der TSE´s.



Beispiel 1





Beispiel 2





Ausgabe für Finanzamt





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE Fehlermeldungen

Top Previous Next

TSE-Fehlermeldungen





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Drucker

Top Previous Next

Fehlermeldungen vom Drucker





Quelle: JSON_spc_en_revA.pdf





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TSE

Top Previous Next

Fehlermeldungen von der TSE





Quelle: JSON_spc_en_revA.pdf





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Einstellungen am Drucker

Top Previous Next

Einstellungen am Drucker



Zugangsdaten zum Drucker über Browser:

User: epson

PW: epson



Oder bei neuer Firmware:

User: epson

PW: <SN des Druckers>





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





TM-M30

Top Previous Next

Treiber-Konfiguration





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





Graphische Benutzeroberfläche

Top Previous Next

EasyTSE GUI



Optional verwendbare graphische Benutzeroberfläche für den Test der EasyTSE.DLL

Das Tool kann auch verwendet werden, um die Konfiguration einer TSE vorzunehmen.

Die Einstellungen werden in der Datei EasyTSEManager.INI gespeichert.





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





EasyTSE GUI

Top Previous

EasyTSE GUI





* * *



Copyright © by Reinhard Müller und Thomas Geißler. All Rights Reserved.





