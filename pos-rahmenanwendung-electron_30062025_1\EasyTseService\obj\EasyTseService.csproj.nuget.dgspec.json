{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\GitHub\\pos-rahmenanwendung-electron\\easytseservice\\EasyTseService.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\GitHub\\pos-rahmenanwendung-electron\\easytseservice\\EasyTseService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\pos-rahmenanwendung-electron\\easytseservice\\EasyTseService.csproj", "projectName": "EasyTseService", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\pos-rahmenanwendung-electron\\easytseservice\\EasyTseService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\pos-rahmenanwendung-electron\\easytseservice\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net472"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"targetAlias": "net472", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net472": {"targetAlias": "net472", "dependencies": {"System.Text.Json": {"target": "Package", "version": "[9.0.2, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.407\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win7-x86": {"#import": []}}}}}