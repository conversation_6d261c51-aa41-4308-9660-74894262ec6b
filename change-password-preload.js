const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('passwordApi', {
  // Passwortänderung an den Hauptprozess senden
  changePassword: (currentPassword, newPassword) => {
    ipcRenderer.send('change-password', { 
      currentPassword: currentPassword, 
      newPassword: newPassword 
    });
  },
  
  // Dialog abbrechen
  cancelDialog: () => {
    ipcRenderer.send('cancel-change-password');
  },
  
  // Ergebnis der Passwortänderung empfangen
  onChangeResult: (callback) => {
    ipcRenderer.on('change-password-result', (event, result) => callback(result));
  }
});