// Script zum Testen: Transaktion starten und sofort schließen
const axios = require('axios');

const CONFIG = {
    serviceUrl: 'http://localhost:8765',
    clientId: 'K001'
};

async function testStartFinish() {
    try {
        console.log("=".repeat(60));
        console.log("Test: Transaktion starten und sofort schließen");
        console.log("=".repeat(60));
        
        // 1. Transaktion starten
        console.log("1. Starte neue Transaktion...");
        const startResponse = await axios.post(`${CONFIG.serviceUrl}/startTransaction`, {
            clientId: CONFIG.clientId,
            processData: ""
        }, {
            timeout: 30000
        });

        let startData = startResponse.data;
        if (typeof startData === 'string') {
            startData = JSON.parse(startData);
        }

        if (!startData.success) {
            console.error("Fehler beim Starten der Transaktion:", startData.error);
            return;
        }

        const transactionNumber = startData.data.transactionNumber;
        console.log(`✓ Transaktion gestartet: ${transactionNumber}`);
        console.log(`  - LogTime: ${startData.data.logTime}`);
        console.log(`  - SignatureCounter: ${startData.data.signatureCounter}`);
        
        // 2. Kurz warten
        console.log("2. Warte 1 Sekunde...");
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 3. Transaktion sofort schließen
        console.log("3. Schließe Transaktion...");
        const finishPayload = {
            clientId: CONFIG.clientId,
            transactionNumber: String(transactionNumber),
            processData: "",
            processType: 1,
            vorgangsType: 4,
            betragMwSt1: 0,
            betragMwSt2: 0,
            betragMwSt3: 0,
            betragMwSt4: 0,
            betragMwSt0: 0,
            betragBar: 0,
            betragUnbar: 0
        };

        const finishResponse = await axios.post(`${CONFIG.serviceUrl}/finishTransaction`, finishPayload, {
            timeout: 30000
        });

        let finishData = finishResponse.data;
        if (typeof finishData === 'string') {
            finishData = JSON.parse(finishData);
        }

        if (finishData.success) {
            console.log("✓ Transaktion erfolgreich geschlossen!");
            console.log(`  - LogTime: ${finishData.data.logTime}`);
            console.log(`  - SignatureCounter: ${finishData.data.signatureCounter}`);
            console.log(`  - Signature: ${finishData.data.signatureFinish || 'nicht verfügbar'}`);
        } else {
            console.error("✗ Fehler beim Schließen der Transaktion:", finishData.error);
        }
        
    } catch (error) {
        console.error("Fehler:", error.message);
        
        if (error.response) {
            console.error(`HTTP Status: ${error.response.status}`);
            console.error(`HTTP Data: ${JSON.stringify(error.response.data)}`);
        }
    }
}

// Script ausführen
if (require.main === module) {
    testStartFinish().then(() => {
        console.log("");
        console.log("Script beendet.");
        process.exit(0);
    }).catch((error) => {
        console.error("Unerwarteter Fehler:", error.message);
        process.exit(1);
    });
}

module.exports = { testStartFinish };