const fs = require('fs');
const path = require('path');

// Pfad zum EasyTseService Release-Verzeichnis
const easytseDir = path.join(__dirname, '..', 'EasyTseService', 'bin', 'Release', 'net472');

// Überprüfen, ob das Verzeichnis existiert
if (!fs.existsSync(easytseDir)) {
  console.error(`FEHLER: Das Verzeichnis ${easytseDir} existiert nicht.`);
  console.error('Bitte stellen Sie sicher, dass EasyTseService kompiliert wurde.');
  process.exit(1);
}

// Überprüfen, ob die EasyTseService.exe existiert
const exePath = path.join(easytseDir, 'EasyTseService.exe');
if (!fs.existsSync(exePath)) {
  console.error(`FEHLER: Die Datei ${exePath} wurde nicht gefunden.`);
  console.error('Bitte stellen Sie sicher, dass EasyTseService kompiliert wurde.');
  process.exit(1);
}

console.log('EasyTseService ist vorhanden und kann eingebunden werden.');