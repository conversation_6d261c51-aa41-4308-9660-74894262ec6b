Diese Komponente dient als Brücke zwischen der Electron-basierten WPOS-Anwendung und der EasyTSE DLL. Sie stellt einen lokalen Webservice bereit, der über HTTP-Anfragen mit der EasyTSE DLL kommuniziert.
Voraussetzungen

Windows-Betriebssystem
.NET Framework 4.7.2 oder höher
EasyTSE DLL installiert (EasyTSE.dll)
Visual Studio 2019 oder höher zum Kompilieren (oder vorkompilierte Version verwenden)

Installation

EasyTSE installieren

Laden Sie die EasyTSE DLL von www.easytse.com/download/EasyTSE.dll herunter
Installieren Sie die DLL auf dem Zielsystem (kann auch im gleichen Verzeichnis wie der Service platziert werden)


System.Text.Json DLL

Erstellen Sie ein Verzeichnis lib im Projektordner
Laden Sie System.Text.Json.dll herunter und platzieren Sie sie im lib-Verzeichnis


Service kompilieren

Öffnen Sie eine Developer Command Prompt für Visual Studio
Navigieren Sie zum Verzeichnis mit der .csproj-Datei
Führen Sie den Befehl dotnet build -c Release aus


Service konfigurieren

Die kompilierte EXE befindet sich im Verzeichnis bin\Release\net472
Stellen Sie sicher, dass die EasyTSE.dll im gleichen Verzeichnis liegt oder im System registriert ist



Verwendung

Service starten

Führen Sie EasyTseService.exe aus
Der Service startet einen HTTP-Server auf http://localhost:8765
Lassen Sie das Konsolenfenster geöffnet, solange der Service laufen soll


Integration mit WPOS

Die WPOS-Anwendung ist bereits für die Kommunikation mit diesem Service konfiguriert
Der TseClient in WPOS sendet HTTP-Anfragen an den lokalen Service, um mit der TSE zu kommunizieren


Datenexporte

Der Service unterstützt verschiedene Exportmethoden gemäß den gesetzlichen Anforderungen
Die exportierten Daten werden im konfigurierten Exportverzeichnis gespeichert
Standardmäßig wird ein Unterverzeichnis "TSE_ExportedData" im Installationsverzeichnis verwendet



API-Endpunkte
Der Service stellt folgende HTTP-Endpunkte bereit:
Basis-Endpunkte

POST /connect - Verbindung zur TSE herstellen

Parameter: puk, adminPin, timeAdminPin, secretKey, ip, port, clientId, deviceId


POST /selfTest - TSE-Selbsttest durchführen
POST /updateTime - TSE-Zeit aktualisieren
POST /getPublicKey - Öffentlichen Schlüssel abrufen

Parameter: clientId


POST /info - TSE-Informationen abrufen

Parameter: clientId



Transaktions-Endpunkte

POST /startTransaction - Neue Transaktion starten

Parameter: clientId, processData (optional), holdConnection (optional)


POST /updateTransaction - Transaktion aktualisieren

Parameter: clientId, transactionNumber, processData, holdConnection (optional)


POST /finishTransaction - Transaktion abschließen

Parameter: clientId, transactionNumber, processData, processType (optional), vorgangsType (optional), totalAmount (optional), taxSet (optional), payments (optional)


POST /cancelTransaction - Transaktion abbrechen

Parameter: clientId, transactionNumber


POST /getSignature - Signatur für Transaktion abrufen

Parameter: clientId, transactionNumber


POST /getQRCode - QR-Code für Transaktion abrufen

Parameter: clientId, signature, signatureCounter, logTime, totalAmount



Export-Endpunkte

POST /exportArchive - Vollständigen TSE-Datenexport durchführen

Parameter:

clientId: Client-ID für den Export
deleteAfterExport (optional, Boolean): Gibt an, ob die Daten nach dem Export gelöscht werden sollen
exportDir (optional): Zielverzeichnis für den Export (falls nicht angegeben, wird das Standard-Exportverzeichnis verwendet)




POST /exportByDate - TSE-Datenexport nach Zeitraum durchführen

Parameter:

clientId: Client-ID für den Export
startDate: Anfangsdatum im Format "YYYY-MM-DDThh:mm:ss"
endDate (optional): Enddatum im Format "YYYY-MM-DDThh:mm:ss" (aktuelles Datum, wenn nicht angegeben)
exportDir (optional): Zielverzeichnis für den Export




POST /exportByTransaction - TSE-Datenexport nach Transaktionen durchführen

Parameter für einzelne Transaktion:

clientId: Client-ID für den Export
transactionNumber: Nummer der zu exportierenden Transaktion
exportDir (optional): Zielverzeichnis für den Export


Parameter für Transaktionsintervall:

clientId: Client-ID für den Export
startTransactionNumber: Beginn des Transaktionsintervalls
endTransactionNumber: Ende des Transaktionsintervalls
exportDir (optional): Zielverzeichnis für den Export





Exportfunktionen im Detail
Komplettexport (/exportArchive)
Dieser Endpunkt führt einen vollständigen Export aller auf der TSE gespeicherten Daten durch. Die Daten werden im TAR-Format gespeichert und können optional nach erfolgreichem Export gelöscht werden, um Speicherplatz auf der TSE freizugeben.
Beispiel-Request:
jsonKopieren{
  "clientId": "Kasse1",
  "deleteAfterExport": false,
  "exportDir": "C:\\TSE_Exports\\Vollexport"
}
Zeitraumexport (/exportByDate)
Mit diesem Endpunkt können TSE-Daten für einen bestimmten Zeitraum exportiert werden. Dies ist besonders nützlich für periodische Auswertungen oder bei Prüfungen durch das Finanzamt.
Beispiel-Request:
jsonKopieren{
  "clientId": "Kasse1",
  "startDate": "2023-01-01T00:00:00",
  "endDate": "2023-01-31T23:59:59",
  "exportDir": "C:\\TSE_Exports\\Januar2023"
}
Transaktionsexport (/exportByTransaction)
Dieser Endpunkt bietet zwei Möglichkeiten:

Export einer einzelnen Transaktion durch Angabe der Transaktionsnummer
Export eines Bereichs von Transaktionen durch Angabe von Start- und End-Transaktionsnummer

Beispiel-Request für einzelne Transaktion:
jsonKopieren{
  "clientId": "Kasse1",
  "transactionNumber": "12345",
  "exportDir": "C:\\TSE_Exports\\Transaktion12345"
}
Beispiel-Request für Transaktionsintervall:
jsonKopieren{
  "clientId": "Kasse1",
  "startTransactionNumber": "1000",
  "endTransactionNumber": "2000",
  "exportDir": "C:\\TSE_Exports\\Transaktionen1000-2000"
}
Fehlerbehebung

Service startet nicht: Prüfen Sie, ob die EasyTSE.dll korrekt installiert ist
Verbindungsfehler: Stellen Sie sicher, dass die TSE angeschlossen und die Zugangsdaten korrekt sind
HTTP-Fehler: Prüfen Sie, ob Port 8765 verfügbar ist und nicht von einer anderen Anwendung blockiert wird
Exportfehler: Stellen Sie sicher, dass das angegebene Exportverzeichnis existiert und Schreibrechte vorhanden sind
Bei großen Exporten sollte ausreichend freier Festplattenspeicher verfügbar sein
Die TSE muss über genügend freien Speicherplatz für temporäre Daten verfügen
Bei regelmäßigen Exporten empfiehlt sich die Verwendung von deleteAfterExport: true für den Komplettexport


Entwicklung und Anpassung

Der Service ist in C# geschrieben und verwendet die .NET-Framework-Klasse HttpListener
Die Kommunikation mit der EasyTSE DLL erfolgt über COM-Interop
Änderungen an der API können in der Datei EasyTseService.cs vorgenommen werden
Die Implementierung der Exportfunktionen basiert auf den in der EasyTSE-Dokumentation beschriebenen Stapelfunktionen (Stack_ExportArchiveData, Stack_ExportFilteredByPeriodOfTime, etc.)