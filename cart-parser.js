// Modul zum Extrahieren von Warenkorbdaten aus dem DOM
const loggerService = require('./logger-service');
const log = loggerService.getModuleLogger('CartParser');

// Log-Level Konstanten
const LOG_LEVELS = {
  NONE: 'false',     // Kein Logging der Warenkorbdaten
  MINIMAL: 'minimal', // Nur grundlegende Informationen
  FULL: 'full'       // Vollständiges Logging aller Warenkorbdaten
};

// Konfiguration - wird später vom Hauptprozess gesetzt
let config = {
  logLevel: LOG_LEVELS.MINIMAL  // Standard ist minimales Logging
};

/**
 * Setzt die Konfigurationsoptionen für den CartParser
 * @param {Object} options - Die Konfigurationsoptionen
 */
function setConfig(options) {
  config = { ...config, ...options };
  
  // Validierung und Normalisierung des Log-Levels
  if (config.logLevel && typeof config.logLevel === 'string') {
    config.logLevel = config.logLevel.toLowerCase();
    
    // Stelle sicher, dass logLevel einen gültigen Wert hat
    if (![LOG_LEVELS.NONE, LOG_LEVELS.MINIMAL, LOG_LEVELS.FULL].includes(config.logLevel)) {
      log.warn(`Ungültiger Log-Level "${config.logLevel}", verwende Standard "${LOG_LEVELS.MINIMAL}"`);
      config.logLevel = LOG_LEVELS.MINIMAL;
    }
  } else {
    log.warn(`Ungültiger oder fehlender Log-Level, verwende Standard "${LOG_LEVELS.MINIMAL}"`);
    config.logLevel = LOG_LEVELS.MINIMAL;
  }
  
  log.info(`CartParser Konfiguration gesetzt, Log-Level: ${config.logLevel}`);
}

/**
 * Bereinigt Text von problematischen Unicode-Zeichen
 * @param {string} text - Der zu bereinigende Text
 * @returns {string} - Der bereinigte Text
 */
function cleanText(text) {
  if (!text) return '';
  
  // Ersetze bekannte problematische Zeichen und normalisiere Unicode
  return text
    .replace(/┬á/g, ' ')        // Ersetze Leerzeichen
    .replace(/Ôé¼/g, '€')       // Korrigiere Euro-Symbol
    .replace(/Â/g, '')          // Entferne überflüssige Zeichen
    .normalize('NFC');          // Normalisiere Unicode-Zeichen
}

/**
 * Extrahiert Warenkorbdaten aus dem WebContents-Objekt
 * @param {Object} webContents - Das WebContents-Objekt des Webviews
 * @returns {Promise<Object>} - Ein Promise mit den extrahierten Warenkorbdaten
 */
async function extractCartFromWebContents(webContents) {
  try {
    // Führe JavaScript im WebContents aus, um den Warenkorb zu extrahieren
    const cartData = await webContents.executeJavaScript(`
      (function() {
        // Prüfen, ob wir auf der Checkout-Seite sind
        const isCheckoutPage = document.querySelector('app-checkout') !== null;
        
        // 1. Warenkorb-Einträge finden
        const cartItems = document.querySelectorAll('app-shopping-cart-item');
        
        // Wenn keine Elemente gefunden wurden, leeren Warenkorb zurückgeben
        if (!cartItems || cartItems.length === 0) {
          return { 
            items: [], 
            totalAmount: '0,00 €',
            isCheckout: isCheckoutPage
          };
        }
        
        // 2. Extrahiere Informationen aus den Warenkorbeinträgen
        const items = Array.from(cartItems).map((item, index) => {
          try {
            // Name des Produkts
            const nameElement = item.querySelector('.font-semibold.text-start.POS1\\\\:text-sm.POS3\\\\:text-xl') || 
                               item.querySelector('.font-semibold');
            const name = nameElement ? nameElement.textContent.trim() : 'Unbekanntes Produkt';
            
            // Preis des Produkts
            const priceElement = item.querySelector('.w-\\\\[30\\\\%\\\\].text-right.font-semibold.POS1\\\\:text-sm.POS3\\\\:text-xl.pr-1') ||
                               item.querySelector('.text-right.font-semibold') ||
                               item.querySelector('.text-lg.font-semibold');
            const price = priceElement ? priceElement.textContent.trim() : '0,00 €';
            
            // Menge - suchen wir im übergeordneten Container
            let quantity = 1;
            const quantityElement = document.querySelector('.flex.w-\\\\[10\\\\%\\\\].items-center.justify-center.bg-white.POS1\\\\:text-xl.POS3\\\\:text-2xl');
            if (quantityElement) {
              const quantityText = quantityElement.textContent.trim();
              const parsedQuantity = parseInt(quantityText);
              if (!isNaN(parsedQuantity)) {
                quantity = parsedQuantity;
              }
            }
            
            // Details (Datum, Uhrzeit etc.)
            let details = '';
            const detailsElement = item.querySelector('.flex.flex-col.POS1\\\\:text-xs.POS2\\\\:text-sm.POS3\\\\:text-lg');
            if (detailsElement) {
              const detailTexts = [];
              
              // Datum und Zeit extrahieren
              const dateTimeRows = detailsElement.querySelectorAll('.flex.flex-row');
              dateTimeRows.forEach(row => {
                if (row.textContent.trim()) {
                  detailTexts.push(row.textContent.trim().replace(/\\s+/g, ' '));
                }
              });
              
              details = detailTexts.join(' | ');
            }
            
            return {
              name,
              price,
              quantity,
              details
            };
          } catch (itemError) {
            console.error('Fehler beim Extrahieren von Warenkorbzeile ' + index + ':', itemError);
            return null;
          }
        }).filter(item => item !== null);
        
        // 3. Gesamtbetrag extrahieren - mit mehreren Möglichkeiten
        let totalAmount = '0,00 €';
        
        // Checkout-Seite hat die Gesamtsumme an anderer Stelle
        if (isCheckoutPage) {
          // Suche nach "Gesamtsumme" Element im Checkout
          const checkoutTotalLabels = Array.from(document.querySelectorAll('p.text-lg.font-semibold'));
          const gesamtsummeLabel = checkoutTotalLabels.find(el => 
            el.textContent.trim().includes('Gesamtsumme'));
            
          if (gesamtsummeLabel) {
            // Das nächste Element ist der Betrag
            const nextElement = gesamtsummeLabel.nextElementSibling;
            if (nextElement && nextElement.textContent) {
              totalAmount = nextElement.textContent.trim();
            }
          }
        } else {
          // Normaler Shop-Modus - Gesamtsumme im Checkout-Button
          const checkoutButton = document.querySelector('app-checkout-button button');
          if (checkoutButton) {
            totalAmount = checkoutButton.textContent.trim();
          }
        }
        
        return {
          items,
          totalAmount,
          isCheckout: isCheckoutPage
        };
      })();
    `);
    
    // Bereinige die extrahierten Daten vor der Rückgabe
    const cleanedCartData = {
      items: cartData.items.map(item => ({
        name: cleanText(item.name),
        price: cleanText(item.price),
        quantity: item.quantity,
        details: cleanText(item.details)
      })),
      totalAmount: cleanText(cartData.totalAmount),
      isCheckout: cartData.isCheckout || false
    };
    
    // Logging basierend auf dem konfigurierten Log-Level
    const itemCount = cleanedCartData.items.length;
    const mode = cleanedCartData.isCheckout ? "Checkout" : "Shop";
    
    switch(config.logLevel) {
      case LOG_LEVELS.NONE:
        // Kein Logging der Warenkorbdaten
        break;
        
      case LOG_LEVELS.MINIMAL:
        // Minimales Logging - nur Anzahl und Modus
        log.info(`Warenkorb mit ${itemCount} Positionen extrahiert (${mode}-Modus)`);
        break;
        
      case LOG_LEVELS.FULL:
        // Vollständiges Logging aller Daten
        log.info(`Warenkorb mit ${itemCount} Positionen extrahiert (${mode}-Modus)`);
        log.debug(`Vollständige Warenkorbdaten: ${JSON.stringify(cleanedCartData)}`);
        
        // Erste Position als Beispiel
        if (itemCount > 0) {
          log.debug(`Beispielposition: ${JSON.stringify(cleanedCartData.items[0])}`);
        }
        break;
        
      default:
        // Fallback zu minimalem Logging
        log.info(`Warenkorb mit ${itemCount} Positionen extrahiert (${mode}-Modus)`);
    }
    
    return cleanedCartData;
  } catch (error) {
    log.error('Fehler beim Extrahieren des Warenkorbs:', error);
    return { items: [], totalAmount: '0,00 €', isCheckout: false };
  }
}

module.exports = {
  extractCartFromWebContents,
  setConfig, // Exportiere die Konfigurationsfunktion
  LOG_LEVELS // Exportiere die Log-Level-Konstanten
};