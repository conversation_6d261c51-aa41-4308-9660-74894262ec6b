<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Shop Receiver Kassensystem</title>
  <style>
    body, html {
      margin: 0;
      padding: 0;
      height: 100%;
      overflow: hidden;
      font-family: Arial, sans-serif;
    }
    #webview {
      width: 100%;
      height: 100%;
      border: none;
      display: flex;
    } 
    #loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: white;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      z-index: 100;
    }
    #status {
      margin-top: 10px;
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
      background-color: #f8f8f8;
      max-width: 80%;
      word-break: break-all;
    }
    
    #ec-payment-modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.8);
      display: none; 
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }
    #payment-overlay {
    display: flex !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 9999 !important;
    background-color: rgba(0, 0, 0, 0.8) !important;
  }
  
  #payment-overlay.hidden {
    display: none !important;
  }
    .modal-content {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
      padding: 30px;
      width: 450px;
      max-width: 90%;
      text-align: center;
    }
    
    .modal-title {
      color: #333;
      margin-top: 0;
      font-size: 24px;
      margin-bottom: 20px;
    }
    
    .payment-spinner {
      display: inline-block;
      width: 50px;
      height: 50px;
      border: 5px solid rgba(0, 123, 255, 0.3);
      border-radius: 50%;
      border-top-color: #007bff;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 15px;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    
    .payment-status {
      padding: 15px;
      margin: 15px 0;
      border-radius: 4px;
      font-size: 16px;
      font-weight: bold;
      background-color: #fff3cd;
      color: #856404;
      border: 1px solid #ffeeba;
    }
    
    .payment-details {
      text-align: left;
      margin: 15px 0;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }
    
    .payment-details p {
      margin: 5px 0;
      font-size: 14px;
    }
    
    .modal-buttons {
      margin-top: 20px;
    }
    
    .btn {
      padding: 8px 16px;
      border-radius: 4px;
      font-weight: bold;
      cursor: pointer;
      border: none;
      outline: none;
      transition: background-color 0.2s;
    }
    
    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background-color: #5a6268;
    }
  </style>
</head>
<body>
  <div id="loading">
    <div>Laden...</div>
    <div id="status">Initialisiere Anwendung...</div>
  </div>
  
  <div id="ec-payment-modal">
    <div class="modal-content">
      <h2 class="modal-title">EC-Kartenzahlung</h2>
      
      <div class="payment-spinner"></div>
      
      <div id="ec-payment-status" class="payment-status">
        Bitte Karte an Terminal halten oder einstecken...
      </div>
      
      <div class="payment-details">
        <p><strong>Betrag:</strong> <span id="ec-payment-amount">0,00 €</span></p>
        <p><strong>Transaktion:</strong> <span id="ec-payment-transactionId">-</span></p>
        <p><strong>Status:</strong> <span id="ec-payment-statusText">Warte auf Kartenzahlung</span></p>
      </div>
      
      <div class="modal-buttons">
        <button id="ec-payment-close" class="btn btn-secondary">Schließen</button>
      </div>
    </div>
  </div>
  
  <webview id="webview" src="about:blank" nodeintegration></webview>
  
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const ecPaymentModal = document.getElementById('ec-payment-modal');
      const ecPaymentStatus = document.getElementById('ec-payment-status');
      const ecPaymentAmount = document.getElementById('ec-payment-amount');
      const ecPaymentTransactionId = document.getElementById('ec-payment-transactionId');
      const ecPaymentStatusText = document.getElementById('ec-payment-statusText');
      const ecPaymentClose = document.getElementById('ec-payment-close');
      
      function showEcPaymentModal(data) {
        console.log('Direct showEcPaymentModal called with:', data);
        
        if (data && data.amount) {
          const amount = parseFloat(data.amount) / 100;
          ecPaymentAmount.textContent = `${amount.toFixed(2)} €`;
        }
        
        if (data && data.transactionId) {
          ecPaymentTransactionId.textContent = data.transactionId;
        }
        
        ecPaymentModal.style.display = 'flex';
        
        console.log('EC Payment Modal should be visible now');
      }
      
      function hideEcPaymentModal() {
        ecPaymentModal.style.display = 'none';
      }
      
      ecPaymentClose.addEventListener('click', () => {
        hideEcPaymentModal();
      });
      
      setTimeout(() => {
        console.log('Auto-showing EC payment modal...');
        showEcPaymentModal({
          amount: 1490,
          transactionId: 'test-' + Date.now()
        });
      }, 3000); 

      if (window.api) {
        console.log('Setting up IPC listeners for payment events');
        
        window.api.onZVTPaymentStarted((data) => {
          console.log('ZVT payment started event received:', data);
          showEcPaymentModal(data);
        });
        
        window.api.onZVTPaymentUpdate((data) => {
          console.log('ZVT payment update event received:', data);
          
          ecPaymentModal.style.display = 'flex';
          
          if (data.message) {
            ecPaymentStatus.textContent = data.message;
          }
          
          if (data.statusText) {
            ecPaymentStatusText.textContent = data.statusText;
          }
          
          if (data.status === 'success') {
            ecPaymentStatus.style.backgroundColor = '#d4edda';
            ecPaymentStatus.style.color = '#155724';
            ecPaymentStatus.style.borderColor = '#c3e6cb';
          } else if (data.status === 'error') {
            ecPaymentStatus.style.backgroundColor = '#f8d7da';
            ecPaymentStatus.style.color = '#721c24';
            ecPaymentStatus.style.borderColor = '#f5c6cb';
          }
        });
        
        window.api.onZVTPaymentResult((data) => {
          console.log('ZVT payment result event received:', data);
          
          ecPaymentModal.style.display = 'flex';
          
          if (data.success) {
            ecPaymentStatus.textContent = 'Zahlung erfolgreich!';
            ecPaymentStatus.style.backgroundColor = '#d4edda';
            ecPaymentStatus.style.color = '#155724';
            ecPaymentStatus.style.borderColor = '#c3e6cb';
            ecPaymentStatusText.textContent = 'Erfolgreich';
          } else {
            ecPaymentStatus.textContent = `Fehler: ${data.error || 'Unbekannter Fehler'}`;
            ecPaymentStatus.style.backgroundColor = '#f8d7da';
            ecPaymentStatus.style.color = '#721c24';
            ecPaymentStatus.style.borderColor = '#f5c6cb';
            ecPaymentStatusText.textContent = 'Fehlgeschlagen';
          }
          
          if (data.amount) {
            const amount = parseFloat(data.amount) / 100;
            ecPaymentAmount.textContent = `${amount.toFixed(2)} €`;
          }
          
          if (data.transactionId) {
            ecPaymentTransactionId.textContent = data.transactionId;
          }
        });
      } else {
        console.warn('window.api not available, IPC events will not work');
      }
    });
  </script>
  <script src="renderer.js"></script>
</body>
</html>