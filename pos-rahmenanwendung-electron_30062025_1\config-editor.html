<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Konfigurationseditor</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }

    h1 {
      color: #333;
      border-bottom: 1px solid #ddd;
      padding-bottom: 10px;
      margin-bottom: 20px;
    }

    .config-container {
      display: flex;
      height: calc(100vh - 100px);
    }

    .config-tabs {
      width: 200px;
      background-color: #e0e0e0;
      padding: 10px 0;
      border-radius: 5px 0 0 5px;
      overflow-y: auto;
    }

    .config-tab {
      padding: 12px 15px;
      cursor: pointer;
      border-left: 4px solid transparent;
    }

    .config-tab:hover {
      background-color: #d0d0d0;
    }

    .config-tab.active {
      background-color: #d0d0d0;
      border-left-color: #4285f4;
      font-weight: bold;
    }

    .config-content {
      flex-grow: 1;
      background-color: white;
      padding: 20px;
      border-radius: 0 5px 5px 0;
      overflow-y: auto;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .config-section {
      display: none;
    }

    .config-section.active {
      display: block;
    }

    .form-group {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }

    input[type="text"],
    input[type="number"],
    input[type="password"],
    select,
    textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
      font-family: monospace;
    }

    textarea {
      min-height: 100px;
    }

    .checkbox-group {
      margin-top: 5px;
    }

    .checkbox-group label {
      font-weight: normal;
      display: inline;
      margin-left: 5px;
    }

    .buttons {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
    }

    button {
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }

    .save-btn {
      background-color: #4285f4;
      color: white;
    }

    .cancel-btn {
      background-color: #f1f1f1;
      color: #333;
    }

    .success-message {
      padding: 15px;
      margin: 15px 0;
      background-color: #d4edda;
      border-color: #c3e6cb;
      color: #155724;
      border-radius: 4px;
      display: none;
    }

    .error-message {
      padding: 15px;
      margin: 15px 0;
      background-color: #f8d7da;
      border-color: #f5c6cb;
      color: #721c24;
      border-radius: 4px;
      display: none;
    }

    .tooltip {
      display: inline-block;
      margin-left: 5px;
      position: relative;
      cursor: help;
    }

    .tooltip .tooltip-text {
      visibility: hidden;
      width: 250px;
      background-color: #555;
      color: #fff;
      text-align: left;
      border-radius: 6px;
      padding: 10px;
      position: absolute;
      z-index: 1;
      bottom: 125%;
      left: 50%;
      margin-left: -125px;
      opacity: 0;
      transition: opacity 0.3s;
      font-size: 12px;
      line-height: 1.4;
    }

    .tooltip:hover .tooltip-text {
      visibility: visible;
      opacity: 1;
    }
  </style>
</head>
<body>

  <div class="success-message" id="success-message">
    Konfiguration erfolgreich gespeichert. Einige Änderungen erfordern möglicherweise einen Neustart der Anwendung.
  </div>

  <div class="error-message" id="error-message"></div>

  <div class="config-container">
    <div class="config-tabs">
      <div class="config-tab active" data-tab="general">Allgemein</div>
      <div class="config-tab" data-tab="api">API</div>
      <div class="config-tab" data-tab="window">Fenstereinstellungen</div>
      <div class="config-tab" data-tab="printer">Drucker</div>
      <div class="config-tab" data-tab="mqtt">MQTT</div>
      <div class="config-tab" data-tab="fiskaly">Fiskaly TSE</div>
      <div class="config-tab" data-tab="zvt">ZVT-Terminal</div>
      <div class="config-tab" data-tab="logging">Protokollierung</div>
    </div>

    <div class="config-content">
      <!-- Allgemeine Einstellungen -->
      <div class="config-section active" id="general-section">
        <h2>Allgemeine Einstellungen</h2>

        <div class="form-group">
          <label for="fallback-url">Fallback-URL</label>
          <input type="text" id="fallback-url" placeholder="https://example.com">
          <div class="tooltip">
            <span class="tooltip-text">URL, die geladen wird, falls die API-Konfiguration nicht abgerufen werden kann.</span>
          </div>
        </div>

        <div class="form-group checkbox-group">
          <input type="checkbox" id="customer-display">
          <label for="customer-display">Kundenanzeige aktivieren</label>
          <div class="tooltip">
            <span class="tooltip-text">Ob ein zweites Fenster für die Kundenanzeige auf einem zweiten Monitor angezeigt werden soll.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="customer-polling">Abfrageintervall der Kundenanzeige (ms)</label>
          <input type="number" id="customer-polling" min="500" max="10000" step="100">
          <div class="tooltip">
            <span class="tooltip-text">Wie oft die Kundenanzeige aktualisiert werden soll (in Millisekunden).</span>
          </div>
        </div>

        <div class="form-group checkbox-group">
          <input type="checkbox" id="auto-update">
          <label for="auto-update">Automatische Updates aktivieren</label>
          <div class="tooltip">
            <span class="tooltip-text">Ob die Anwendung automatisch nach Updates suchen und diese installieren soll.</span>
          </div>
        </div>
      </div>

      <!-- API-Einstellungen -->
      <div class="config-section" id="api-section">
        <h2>API-Einstellungen</h2>

        <div class="form-group">
          <label for="config-url">Konfigurations-URL</label>
          <input type="text" id="config-url" placeholder="https://api.example.com/config">
          <div class="tooltip">
            <span class="tooltip-text">URL zum Abrufen der Anwendungskonfiguration.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="auth-key">Auth-Schlüssel</label>
          <input type="text" id="auth-key" placeholder="API-Autorisierungsschlüssel">
          <div class="tooltip">
            <span class="tooltip-text">Autorisierungsschlüssel für den API-Zugriff.</span>
          </div>
        </div>
      </div>

      <!-- Fenstereinstellungen -->
      <div class="config-section" id="window-section">
        <h2>Fenstereinstellungen</h2>

        <div class="form-group">
          <label for="window-width">Breite</label>
          <input type="number" id="window-width" min="800" max="3840">
          <div class="tooltip">
            <span class="tooltip-text">Fensterbreite in Pixeln.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="window-height">Höhe</label>
          <input type="number" id="window-height" min="600" max="2160">
          <div class="tooltip">
            <span class="tooltip-text">Fensterhöhe in Pixeln.</span>
          </div>
        </div>

        <div class="form-group checkbox-group">
          <input type="checkbox" id="window-fullscreen">
          <label for="window-fullscreen">Vollbildmodus</label>
          <div class="tooltip">
            <span class="tooltip-text">Ob die Anwendung im Vollbildmodus gestartet werden soll.</span>
          </div>
        </div>

        <div class="form-group checkbox-group">
          <input type="checkbox" id="window-kiosk">
          <label for="window-kiosk">Kiosk-Modus</label>
          <div class="tooltip">
            <span class="tooltip-text">Ob die Anwendung im Kiosk-Modus gestartet werden soll (verhindert, dass Benutzer die App verlassen).</span>
          </div>
        </div>
        <div class="form-group checkbox-group">
          <input type="checkbox" id="window-showmenubar">
          <label for="window-showmenubar">Menüleiste anzeigen</label>
          <div class="tooltip">
            <span class="tooltip-text">Ob die Menüleiste des Anwendungsfensters angezeigt werden soll.</span>
          </div>
        </div>
      </div>



      <!-- Druckereinstellungen -->
      <div class="config-section" id="printer-section">
        <h2>Druckereinstellungen</h2>

        <h3>Epson Drucker</h3>

        <div class="form-group">
          <label for="printer-ip">IP-Adresse</label>
          <input type="text" id="printer-ip" placeholder="*************">
          <div class="tooltip">
            <span class="tooltip-text">IP-Adresse des Druckers.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="printer-port">Port</label>
          <input type="text" id="printer-port" placeholder="9100">
          <div class="tooltip">
            <span class="tooltip-text">Druckerport (bei leerem Feld wird Standardport verwendet).</span>
          </div>
        </div>

        <div class="form-group">
          <label for="printer-model">Modell</label>
          <input type="text" id="printer-model" placeholder="TM-M30III">
          <div class="tooltip">
            <span class="tooltip-text">Name des Druckermodells.</span>
          </div>
        </div>

        <!-- Druckoptionen -->
        <h3>Druckoptionen</h3>
        <div class="form-group checkbox-group">
          <input type="checkbox" id="print-xml">
          <label for="print-xml">XML-Druck aktivieren</label>
          <div class="tooltip">
            <span class="tooltip-text">Falls aktiviert, wird der XML-Druckvorgang ausgeführt.</span>
          </div>
        </div>
        <div class="form-group checkbox-group">
          <input type="checkbox" id="print-fgl">
          <label for="print-fgl">FGL-Druck aktivieren</label>
          <div class="tooltip">
            <span class="tooltip-text">Falls aktiviert, wird der FGL-Druckvorgang ausgeführt.</span>
          </div>
        </div>
      </div>

      <!-- MQTT-Einstellungen -->
      <div class="config-section" id="mqtt-section">
        <h2>MQTT-Einstellungen</h2>

        <div class="form-group">
          <label for="mqtt-broker">Broker-URL</label>
          <input type="text" id="mqtt-broker" placeholder="wss://mqtt.example.com:443/mqtt">
          <div class="tooltip">
            <span class="tooltip-text">WebSocket-URL des MQTT-Brokers.</span>
          </div>
        </div>

        <h3>Verbindungsoptionen</h3>

        <div class="form-group">
          <label for="mqtt-client-id">Client-ID</label>
          <input type="text" id="mqtt-client-id">
          <div class="tooltip">
            <span class="tooltip-text">Eindeutige Client-Identifikation für die MQTT-Verbindung.</span>
          </div>
        </div>

        <div class="form-group checkbox-group">
          <input type="checkbox" id="mqtt-clean">
          <label for="mqtt-clean">Saubere Sitzung</label>
          <div class="tooltip">
            <span class="tooltip-text">Ob eine saubere Sitzung gestartet werden soll.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="mqtt-connect-timeout">Verbindungs-Timeout (ms)</label>
          <input type="number" id="mqtt-connect-timeout" min="1000" max="60000">
          <div class="tooltip">
            <span class="tooltip-text">Verbindungs-Timeout in Millisekunden.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="mqtt-reconnect-period">Wiederverbindungsintervall (ms)</label>
          <input type="number" id="mqtt-reconnect-period" min="500" max="60000">
          <div class="tooltip">
            <span class="tooltip-text">Zeitspanne zwischen Wiederverbindungsversuchen in Millisekunden.</span>
          </div>
        </div>
      </div>

      <div class="config-section" id="fiskaly-section">
        <h2>Fiskaly TSE Einstellungen</h2>

        <div class="form-group">
          <label for="tse-provider">TSE-Provider</label>
          <select id="tse-provider">
            <option value="fiskaly">Fiskaly</option>
            <option value="epson">Epson</option>
          </select>
          <div class="tooltip">
            <span class="tooltip-text">Wählen Sie den zu verwendenden TSE-Provider. Erfordert Neustart der Anwendung nach Änderung.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="fiskaly-api-url">API-URL</label>
          <input type="text" id="fiskaly-api-url" placeholder="https://kassensichv-middleware.fiskaly.com/api/v2">
          <div class="tooltip">
            <span class="tooltip-text">Fiskaly API-Endpunkt-URL.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="fiskaly-api-key">API-Schlüssel</label>
          <input type="text" id="fiskaly-api-key">
          <div class="tooltip">
            <span class="tooltip-text">Fiskaly API-Schlüssel. Aus Sicherheitsgründen werden nur die ersten 10 Zeichen angezeigt.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="fiskaly-api-secret">API-Geheimnis</label>
          <input type="text" id="fiskaly-api-secret">
          <div class="tooltip">
            <span class="tooltip-text">Fiskaly API-Geheimnis. Aus Sicherheitsgründen werden nur die ersten 10 Zeichen angezeigt.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="fiskaly-tss-id">TSS-ID</label>
          <input type="text" id="fiskaly-tss-id">
          <div class="tooltip">
            <span class="tooltip-text">Technische Sicherheits-System-ID.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="fiskaly-client-id">Client-ID</label>
          <input type="text" id="fiskaly-client-id" placeholder="K001">
          <div class="tooltip">
            <span class="tooltip-text">Client-Identifikation (z. B. Kassensystem-ID).</span>
          </div>
        </div>

        <div class="form-group">
          <label for="fiskaly-admin-pin">Admin-PIN</label>
          <input type="text" id="fiskaly-admin-pin">
          <div class="tooltip">
            <span class="tooltip-text">Admin-PIN für die TSE.</span>
          </div>
        </div>
      </div>

      <!-- ZVT-Terminal Einstellungen -->
      <div class="config-section" id="zvt-section">
        <h2>ZVT-Terminal Einstellungen</h2>

        <div class="alert" style="background-color: #fff3cd; color: #856404; padding: 10px; border-radius: 4px; margin-bottom: 20px;">
          <strong>Hinweis:</strong> Die Verbindungsdaten für das Terminal werden ausschließlich über die API-Konfiguration gesteuert.
          Hier können Sie nur die Händlerdaten für die Belege konfigurieren.
        </div>

        <h3>Belegdaten</h3>

        <div class="form-group">
          <label for="zvt-merchant-name">Firmenname</label>
          <input type="text" id="zvt-merchant-name" placeholder="Meine Firma GmbH">
          <div class="tooltip">
            <span class="tooltip-text">Name des Unternehmens, der auf den Belegen erscheint.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="zvt-merchant-address">Adresse</label>
          <input type="text" id="zvt-merchant-address" placeholder="Hauptstraße 123">
          <div class="tooltip">
            <span class="tooltip-text">Straße und Hausnummer des Unternehmens.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="zvt-merchant-zip-city">PLZ Ort</label>
          <input type="text" id="zvt-merchant-zip-city" placeholder="12345 Musterstadt">
          <div class="tooltip">
            <span class="tooltip-text">Postleitzahl und Ort des Unternehmens.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="zvt-merchant-line1">Zusatzzeile 1</label>
          <input type="text" id="zvt-merchant-line1" placeholder="Tel: 01234 / 56789">
          <div class="tooltip">
            <span class="tooltip-text">Zusätzliche Informationen (z.B. Telefonnummer).</span>
          </div>
        </div>

        <div class="form-group">
          <label for="zvt-merchant-line2">Zusatzzeile 2</label>
          <input type="text" id="zvt-merchant-line2" placeholder="Vielen Dank für Ihren Einkauf!">
          <div class="tooltip">
            <span class="tooltip-text">Zusätzliche Informationen (z.B. Danksagung).</span>
          </div>
        </div>

        <h3>Automatisierung</h3>
        <div class="form-group checkbox-group">
          <input type="checkbox" id="zvt-auto-day-end">
          <label for="zvt-auto-day-end">Autom. EC-Abschluss</label>
          <div class="tooltip">
            <span class="tooltip-text">Automatisch einen EC-Abschluss durchführen, wenn ein Kassenabschluss empfangen wird.</span>
          </div>
        </div>
        <div class="form-group checkbox-group">
          <input type="checkbox" id="zvt-auto-merchant-receipt">
          <label for="zvt-auto-merchant-receipt">Autom. Händlerbeleg</label>
          <div class="tooltip">
            <span class="tooltip-text">Automatisch den Händlerbeleg drucken, wenn eine EC-Zahlung abgeschlossen wird.</span>
          </div>
        </div>
      </div>

      <!-- Protokollierungseinstellungen -->
      <div class="config-section" id="logging-section">
        <h2>Protokollierungseinstellungen</h2>

        <div class="form-group checkbox-group">
          <input type="checkbox" id="logging-enabled">
          <label for="logging-enabled">Protokollierung aktivieren</label>
          <div class="tooltip">
            <span class="tooltip-text">Ob die Protokollierung der Anwendung aktiviert werden soll.</span>
          </div>
        </div>

        <div class="form-group checkbox-group">
          <input type="checkbox" id="logging-console">
          <label for="logging-console">In Konsole protokollieren</label>
          <div class="tooltip">
            <span class="tooltip-text">Ob Protokolle in der Konsole ausgegeben werden sollen.</span>
          </div>
        </div>

        <div class="form-group checkbox-group">
          <input type="checkbox" id="logging-file">
          <label for="logging-file">In Datei protokollieren</label>
          <div class="tooltip">
            <span class="tooltip-text">Ob Protokolle in eine Datei gespeichert werden sollen.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="logging-level">Protokollstufe</label>
          <select id="logging-level">
            <option value="error">Error</option>
            <option value="warn">Warning</option>
            <option value="info">Info</option>
            <option value="debug">Debug</option>
          </select>
          <div class="tooltip">
            <span class="tooltip-text">Allgemeine Protokollstufe.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="logging-console-level">Konsolen-Protokollstufe</label>
          <select id="logging-console-level">
            <option value="error">Error</option>
            <option value="warn">Warning</option>
            <option value="info">Info</option>
            <option value="debug">Debug</option>
          </select>
          <div class="tooltip">
            <span class="tooltip-text">Protokollstufe für die Konsolenausgabe.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="logging-file-level">Datei-Protokollstufe</label>
          <select id="logging-file-level">
            <option value="error">Error</option>
            <option value="warn">Warning</option>
            <option value="info">Info</option>
            <option value="debug">Debug</option>
          </select>
          <div class="tooltip">
            <span class="tooltip-text">Protokollstufe für die Dateiausgabe.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="logging-max-size">Maximale Protokolldateigröße (Bytes)</label>
          <input type="number" id="logging-max-size" min="1048576" step="1048576">
          <div class="tooltip">
            <span class="tooltip-text">Maximale Größe einer einzelnen Protokolldatei in Bytes (Standard 10MB = 10485760).</span>
          </div>
        </div>

        <div class="form-group">
          <label for="logging-max-files">Maximale Anzahl Protokolldateien</label>
          <input type="number" id="logging-max-files" min="1" max="50">
          <div class="tooltip">
            <span class="tooltip-text">Maximale Anzahl an Protokolldateien, die vor der Rotation gespeichert werden sollen.</span>
          </div>
        </div>

        <div class="form-group checkbox-group">
          <input type="checkbox" id="logging-cleanup">
          <label for="logging-cleanup">Bereinigung beim Beenden</label>
          <div class="tooltip">
            <span class="tooltip-text">Ob beim Beenden der Anwendung eine Bereinigung der Protokolle durchgeführt werden soll.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="logging-max-age">Maximales Protokollalter (Tage)</label>
          <input type="number" id="logging-max-age" min="1" max="365">
          <div class="tooltip">
            <span class="tooltip-text">Maximales Alter in Tagen, bevor Protokolle automatisch gelöscht werden.</span>
          </div>
        </div>

        <div class="form-group checkbox-group">
          <input type="checkbox" id="logging-mqtt-detailed">
          <label for="logging-mqtt-detailed">Detaillierte MQTT-Protokollierung</label>
          <div class="tooltip">
            <span class="tooltip-text">Ob detaillierte MQTT-Kommunikation protokolliert werden soll.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="logging-cart">Warenkorb-Protokollstufe</label>
          <select id="logging-cart">
            <option value="false">Deaktiviert</option>
            <option value="minimal">Minimal</option>
            <option value="full">Voll</option>
          </select>
          <div class="tooltip">
            <span class="tooltip-text">Wie detailliert die Inhalte des Warenkorbs protokolliert werden sollen.</span>
          </div>
        </div>

        <div class="form-group checkbox-group">
          <input type="checkbox" id="logging-webhook-enabled">
          <label for="logging-webhook-enabled">Error Log senden an Webhook</label>
          <div class="tooltip">
            <span class="tooltip-text">Ob ERROR-Level Logs an einen Microsoft Teams Webhook gesendet werden sollen.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="logging-webhook-url">Webhook-URL</label>
          <input type="text" id="logging-webhook-url" placeholder="https://ticketpay.webhook.office.com/webhookb2/...">
          <div class="tooltip">
            <span class="tooltip-text">Die Microsoft Teams Webhook-URL, an die ERROR-Level Logs gesendet werden sollen.</span>
          </div>
        </div>

        <div class="form-group">
          <label for="logging-webhook-sender">Absendername</label>
          <input type="text" id="logging-webhook-sender" placeholder="POS Fehlerprotokoll">
          <div class="tooltip">
            <span class="tooltip-text">Der Name, der als Absender für die Fehlermeldungen in Microsoft Teams angezeigt werden soll.</span>
          </div>
        </div>
      </div>

    </div>
  </div>

  <div class="buttons">
    <button class="cancel-btn" id="reset-btn">Änderungen zurücksetzen</button>
    <button class="save-btn" id="save-btn">Konfiguration speichern</button>
  </div>

  <script>
    const { ipcRenderer } = require('electron');

    // DOM-Elemente
    const tabs = document.querySelectorAll('.config-tab');
    const sections = document.querySelectorAll('.config-section');
    const saveBtn = document.getElementById('save-btn');
    const resetBtn = document.getElementById('reset-btn');
    const successMessage = document.getElementById('success-message');
    const errorMessage = document.getElementById('error-message');
    const jsonEditor = document.getElementById('json-editor');

    // Formularelemente für jede Sektion
    const formElements = {
      general: [
        'fallback-url',
        'customer-display',
        'customer-polling',
        'auto-update'
      ],
      api: [
        'config-url',
        'auth-key'
      ],
      window: [
        'window-width',
        'window-height',
        'window-fullscreen',
        'window-kiosk',
        'window-showmenubar'
      ],
      printer: [
        'printer-ip',
        'printer-port',
        'printer-model',
        'print-xml',
        'print-fgl'
      ],
      mqtt: [
        'mqtt-broker',
        'mqtt-client-id',
        'mqtt-clean',
        'mqtt-connect-timeout',
        'mqtt-reconnect-period'
      ],
      fiskaly: [
        'tse-provider',
        'fiskaly-api-url',
        'fiskaly-api-key',
        'fiskaly-api-secret',
        'fiskaly-tss-id',
        'fiskaly-client-id',
        'fiskaly-admin-pin'
      ],
      zvt: [
        'zvt-ip',
        'zvt-port',
        'zvt-is-tlv',
        'zvt-password',
        'zvt-merchant-name',
        'zvt-merchant-address',
        'zvt-merchant-zip-city',
        'zvt-merchant-line1',
        'zvt-merchant-line2',
        'zvt-auto-day-end',
        'zvt-auto-merchant-receipt'
      ],
      logging: [
        'logging-enabled',
        'logging-console',
        'logging-file',
        'logging-level',
        'logging-console-level',
        'logging-file-level',
        'logging-max-size',
        'logging-max-files',
        'logging-cleanup',
        'logging-max-age',
        'logging-mqtt-detailed',
        'logging-cart',
        'logging-webhook-enabled',
        'logging-webhook-url',
        'logging-webhook-sender'
      ]
    };

    // Globale Variablen
    let currentConfig = {};
    let originalConfig = {};

    // Hilfsfunktion zum Maskieren sensibler Daten
    function maskSensitiveValue(value) {
      if (!value) return '';
      if (value.length <= 10) return value;
      return value.substring(0, 10) + '*'.repeat(Math.min(10, value.length - 10));
    }

    // Tab-Wechsel Funktionalität
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const activeTab = document.querySelector('.config-tab.active');
        if (activeTab && activeTab.dataset.tab === 'json') {
          try {
            const jsonContent = jsonEditor.value.trim();
            if (jsonContent) {
              currentConfig = JSON.parse(jsonContent);
              initializeFormValues();
            }
          } catch (error) {
            showError(`Ungültiges JSON: ${error.message}`);
            return;
          }
        }

        if (tab.dataset.tab === 'json') {
          try {
            updateConfigFromForm();
          } catch (error) {
            showError(`Fehler beim Aktualisieren der Konfiguration: ${error.message}`);
            return;
          }
        }

        tabs.forEach(t => t.classList.remove('active'));
        sections.forEach(s => s.classList.remove('active'));

        tab.classList.add('active');
        const sectionId = `${tab.dataset.tab}-section`;
        document.getElementById(sectionId).classList.add('active');

        if (tab.dataset.tab === 'json') {
          updateJsonEditor();
        }
      });
    });

    ipcRenderer.on('load-config', (event, config) => {
      currentConfig = JSON.parse(JSON.stringify(config));
      originalConfig = JSON.parse(JSON.stringify(config));
      initializeFormValues();
      updateJsonEditor();
    });

    ipcRenderer.on('save-config-result', (event, result) => {
      if (result.success) {
        showSuccess('Konfiguration erfolgreich gespeichert. Einige Änderungen erfordern möglicherweise einen Neustart der Anwendung.');
        originalConfig = JSON.parse(JSON.stringify(currentConfig));
      } else {
        showError(`Konfiguration konnte nicht gespeichert werden: ${result.error}`);
      }
    });

    saveBtn.addEventListener('click', () => {
      try {
        const activeTab = document.querySelector('.config-tab.active');
        if (activeTab && activeTab.dataset.tab === 'json') {
          try {
            const jsonContent = jsonEditor.value.trim();
            if (jsonContent) {
              currentConfig = JSON.parse(jsonContent);
            }
          } catch (error) {
            showError(`Ungültiges JSON: ${error.message}`);
            return;
          }
        } else {
          updateConfigFromForm();
        }
        ipcRenderer.send('save-config', currentConfig);
      } catch (error) {
        showError(`Fehler beim Speichern der Konfiguration: ${error.message}`);
      }
    });

    resetBtn.addEventListener('click', () => {
      currentConfig = JSON.parse(JSON.stringify(originalConfig));
      initializeFormValues();
      const activeTab = document.querySelector('.config-tab.active');
      if (activeTab && activeTab.dataset.tab === 'json') {
        updateJsonEditor();
      }
      showSuccess('Änderungen wurden auf die zuletzt gespeicherte Konfiguration zurückgesetzt.');
    });

    Object.values(formElements).flat().forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.addEventListener('change', () => {
          const activeTab = document.querySelector('.config-tab.active');
          if (!activeTab || activeTab.dataset.tab !== 'json') {
            try {
              updateConfigFromForm();
            } catch (error) {
              showError(`Fehler beim Aktualisieren der Konfiguration: ${error.message}`);
            }
          }
        });
      }
    });

    function showSuccess(message) {
      successMessage.textContent = message || 'Konfiguration erfolgreich gespeichert.';
      successMessage.style.display = 'block';
      setTimeout(() => {
        successMessage.style.display = 'none';
      }, 5000);
    }

    function showError(message) {
      errorMessage.textContent = message || 'Ein Fehler ist aufgetreten.';
      errorMessage.style.display = 'block';
      setTimeout(() => {
        errorMessage.style.display = 'none';
      }, 10000);
    }

    function updateJsonEditor() {
      jsonEditor.value = JSON.stringify(currentConfig, null, 2);
    }

    function updateConfigFromForm() {
      // Allgemeine Einstellungen
      currentConfig.fallbackUrl = document.getElementById('fallback-url').value;
      currentConfig.customer_display = document.getElementById('customer-display').checked;
      currentConfig.autoUpdate = document.getElementById('auto-update').checked;

      if (!currentConfig.customer_display_options) {
        currentConfig.customer_display_options = {};
      }
      currentConfig.customer_display_options.polling_interval = parseInt(document.getElementById('customer-polling').value, 10) || 2000;

      // API-Einstellungen
      if (!currentConfig.api) {
        currentConfig.api = {};
      }
      currentConfig.api.configUrl = document.getElementById('config-url').value;
      currentConfig.api.authKey = document.getElementById('auth-key').value;

      // Fenstereinstellungen
      if (!currentConfig.windowSettings) {
        currentConfig.windowSettings = {};
      }
      currentConfig.windowSettings.width = parseInt(document.getElementById('window-width').value, 10) || 1024;
      currentConfig.windowSettings.height = parseInt(document.getElementById('window-height').value, 10) || 768;
      currentConfig.windowSettings.fullscreen = document.getElementById('window-fullscreen').checked;
      currentConfig.windowSettings.kiosk = document.getElementById('window-kiosk').checked;
      currentConfig.windowSettings.showMenuBar = document.getElementById('window-showmenubar').checked;

      // Druckereinstellungen
      if (!currentConfig.printer) {
        currentConfig.printer = { epson: {} };
      } else if (!currentConfig.printer.epson) {
        currentConfig.printer.epson = {};
      }
      currentConfig.printer.epson.ip = document.getElementById('printer-ip').value;
      currentConfig.printer.epson.port = document.getElementById('printer-port').value;
      currentConfig.printer.epson.model = document.getElementById('printer-model').value;

      // Druckoptionen
      if (!currentConfig.printOptions) {
        currentConfig.printOptions = { printXML: true, printFGL: true };
      }
      currentConfig.printOptions.printXML = document.getElementById('print-xml').checked;
      currentConfig.printOptions.printFGL = document.getElementById('print-fgl').checked;

      // MQTT-Einstellungen
      if (!currentConfig.mqtt) {
        currentConfig.mqtt = { options: {} };
      } else if (!currentConfig.mqtt.options) {
        currentConfig.mqtt.options = {};
      }
      currentConfig.mqtt.broker = document.getElementById('mqtt-broker').value;
      currentConfig.mqtt.options.clientId = document.getElementById('mqtt-client-id').value;
      currentConfig.mqtt.options.clean = document.getElementById('mqtt-clean').checked;
      currentConfig.mqtt.options.connectTimeout = parseInt(document.getElementById('mqtt-connect-timeout').value, 10) || 4000;
      currentConfig.mqtt.options.reconnectPeriod = parseInt(document.getElementById('mqtt-reconnect-period').value, 10) || 1000;

      // Fiskaly Einstellungen
      currentConfig.tse_provider = document.getElementById('tse-provider').value;
      if (!currentConfig.fiskaly_config) {
        currentConfig.fiskaly_config = {};
      }
      currentConfig.fiskaly_config.api_url = document.getElementById('fiskaly-api-url').value;

      // API-Schlüssel und API-Geheimnis speziell behandeln
      const apiKeyField = document.getElementById('fiskaly-api-key');
      const apiSecretField = document.getElementById('fiskaly-api-secret');

      // Nur aktualisieren, wenn der Wert geändert wurde (nicht mehr maskiert ist)
      const originalApiKey = currentConfig.fiskaly_config.api_key || '';
      const originalApiSecret = currentConfig.fiskaly_config.api_secret || '';
      const maskedApiKey = maskSensitiveValue(originalApiKey);
      const maskedApiSecret = maskSensitiveValue(originalApiSecret);

      // Wenn der Wert anders ist als die maskierte Version, dann wurde er geändert
      if (apiKeyField.value !== maskedApiKey) {
        currentConfig.fiskaly_config.api_key = apiKeyField.value;
      }

      if (apiSecretField.value !== maskedApiSecret) {
        currentConfig.fiskaly_config.api_secret = apiSecretField.value;
      }

      currentConfig.fiskaly_config.tss_id = document.getElementById('fiskaly-tss-id').value;
      currentConfig.fiskaly_config.client_id = document.getElementById('fiskaly-client-id').value;
      currentConfig.fiskaly_config.admin_pin = document.getElementById('fiskaly-admin-pin').value;

      // ZVT-Einstellungen - nur Händlerdaten
      if (!currentConfig.zvt_config) {
        currentConfig.zvt_config = {};
      }

      // ZVT-Belegdaten
      currentConfig.zvt_config.merchant_name = document.getElementById('zvt-merchant-name').value;
      currentConfig.zvt_config.merchant_address = document.getElementById('zvt-merchant-address').value;
      currentConfig.zvt_config.merchant_zip_city = document.getElementById('zvt-merchant-zip-city').value;
      currentConfig.zvt_config.merchant_line1 = document.getElementById('zvt-merchant-line1').value;
      currentConfig.zvt_config.merchant_line2 = document.getElementById('zvt-merchant-line2').value;

      // ZVT-Automatisierung
      currentConfig.zvt_config.auto_day_end = document.getElementById('zvt-auto-day-end').checked;
      currentConfig.zvt_config.auto_merchant_receipt = document.getElementById('zvt-auto-merchant-receipt').checked;

      // Protokollierungseinstellungen
      if (!currentConfig.logging) {
        currentConfig.logging = {};
      }
      currentConfig.logging.enabled = document.getElementById('logging-enabled').checked;
      currentConfig.logging.logToConsole = document.getElementById('logging-console').checked;
      currentConfig.logging.logToFile = document.getElementById('logging-file').checked;
      currentConfig.logging.logLevel = document.getElementById('logging-level').value;
      currentConfig.logging.consoleLevel = document.getElementById('logging-console-level').value;
      currentConfig.logging.fileLevel = document.getElementById('logging-file-level').value;
      currentConfig.logging.maxLogFileSize = parseInt(document.getElementById('logging-max-size').value, 10) || 10485760;
      currentConfig.logging.maxLogFiles = parseInt(document.getElementById('logging-max-files').value, 10) || 5;
      currentConfig.logging.cleanupOnExit = document.getElementById('logging-cleanup').checked;
      currentConfig.logging.maxLogAgeDays = parseInt(document.getElementById('logging-max-age').value, 10) || 30;
      currentConfig.logging.detailedMqttLogging = document.getElementById('logging-mqtt-detailed').checked;
      currentConfig.logging.cartLogging = document.getElementById('logging-cart').value;
      currentConfig.logging.webhookEnabled = document.getElementById('logging-webhook-enabled').checked;
      currentConfig.logging.webhookUrl = document.getElementById('logging-webhook-url').value;
      currentConfig.logging.webhookSenderName = document.getElementById('logging-webhook-sender').value;
    }

    function initializeFormValues() {
      // Allgemeine Einstellungen
      document.getElementById('fallback-url').value = currentConfig.fallbackUrl || '';
      document.getElementById('customer-display').checked = !!currentConfig.customer_display;
      document.getElementById('auto-update').checked = currentConfig.autoUpdate !== false; // Standardmäßig aktiviert
      const customerPolling = currentConfig.customer_display_options?.polling_interval || 2000;
      document.getElementById('customer-polling').value = customerPolling;

      // API-Einstellungen
      document.getElementById('config-url').value = currentConfig.api?.configUrl || '';
      document.getElementById('auth-key').value = currentConfig.api?.authKey || '';

      // Fenstereinstellungen
      document.getElementById('window-width').value = currentConfig.windowSettings?.width || 1024;
      document.getElementById('window-height').value = currentConfig.windowSettings?.height || 768;
      document.getElementById('window-fullscreen').checked = !!currentConfig.windowSettings?.fullscreen;
      document.getElementById('window-kiosk').checked = !!currentConfig.windowSettings?.kiosk;
      document.getElementById('window-showmenubar').checked = !!currentConfig.windowSettings?.showMenuBar;

      // Druckereinstellungen
      document.getElementById('printer-ip').value = currentConfig.printer?.epson?.ip || '';
      document.getElementById('printer-port').value = currentConfig.printer?.epson?.port || '';
      document.getElementById('printer-model').value = currentConfig.printer?.epson?.model || '';

      // Druckoptionen
      document.getElementById('print-xml').checked = currentConfig.printOptions?.printXML ?? true;
      document.getElementById('print-fgl').checked = currentConfig.printOptions?.printFGL ?? true;

      // MQTT-Einstellungen
      document.getElementById('mqtt-broker').value = currentConfig.mqtt?.broker || '';
      document.getElementById('mqtt-client-id').value = currentConfig.mqtt?.options?.clientId || '';
      document.getElementById('mqtt-clean').checked = !!currentConfig.mqtt?.options?.clean;
      document.getElementById('mqtt-connect-timeout').value = currentConfig.mqtt?.options?.connectTimeout || 4000;
      document.getElementById('mqtt-reconnect-period').value = currentConfig.mqtt?.options?.reconnectPeriod || 1000;

      // Fiskaly Einstellungen
      document.getElementById('tse-provider').value = currentConfig.tse_provider || 'fiskaly';
      document.getElementById('fiskaly-api-url').value = currentConfig.fiskaly_config?.api_url || '';

      // API-Schlüssel und API-Geheimnis maskieren
      const apiKey = currentConfig.fiskaly_config?.api_key || '';
      const apiSecret = currentConfig.fiskaly_config?.api_secret || '';

      document.getElementById('fiskaly-api-key').value = maskSensitiveValue(apiKey);
      document.getElementById('fiskaly-api-secret').value = maskSensitiveValue(apiSecret);

      document.getElementById('fiskaly-tss-id').value = currentConfig.fiskaly_config?.tss_id || '';
      document.getElementById('fiskaly-client-id').value = currentConfig.fiskaly_config?.client_id || '';
      document.getElementById('fiskaly-admin-pin').value = currentConfig.fiskaly_config?.admin_pin || '';

      // ZVT-Einstellungen - nur Händlerdaten
      if (currentConfig.zvt_config) {
        // ZVT-Belegdaten
        document.getElementById('zvt-merchant-name').value = currentConfig.zvt_config.merchant_name || '';
        document.getElementById('zvt-merchant-address').value = currentConfig.zvt_config.merchant_address || '';
        document.getElementById('zvt-merchant-zip-city').value = currentConfig.zvt_config.merchant_zip_city || '';
        document.getElementById('zvt-merchant-line1').value = currentConfig.zvt_config.merchant_line1 || '';
        document.getElementById('zvt-merchant-line2').value = currentConfig.zvt_config.merchant_line2 || '';

        // ZVT-Automatisierung
        document.getElementById('zvt-auto-day-end').checked = !!currentConfig.zvt_config.auto_day_end;
        document.getElementById('zvt-auto-merchant-receipt').checked = !!currentConfig.zvt_config.auto_merchant_receipt;
      }

      // Protokollierungseinstellungen
      document.getElementById('logging-enabled').checked = currentConfig.logging?.enabled;
      document.getElementById('logging-console').checked = currentConfig.logging?.logToConsole;
      document.getElementById('logging-file').checked = currentConfig.logging?.logToFile;
      document.getElementById('logging-level').value = currentConfig.logging?.logLevel || 'info';
      document.getElementById('logging-console-level').value = currentConfig.logging?.consoleLevel || 'info';
      document.getElementById('logging-file-level').value = currentConfig.logging?.fileLevel || 'debug';
      document.getElementById('logging-max-size').value = currentConfig.logging?.maxLogFileSize || 10485760;
      document.getElementById('logging-max-files').value = currentConfig.logging?.maxLogFiles || 5;
      document.getElementById('logging-cleanup').checked = currentConfig.logging?.cleanupOnExit;
      document.getElementById('logging-max-age').value = currentConfig.logging?.maxLogAgeDays || 30;
      document.getElementById('logging-mqtt-detailed').checked = !!currentConfig.logging?.detailedMqttLogging;
      document.getElementById('logging-cart').value = currentConfig.logging?.cartLogging || 'minimal';
      document.getElementById('logging-webhook-enabled').checked = !!currentConfig.logging?.webhookEnabled;
      document.getElementById('logging-webhook-url').value = currentConfig.logging?.webhookUrl || '';
      document.getElementById('logging-webhook-sender').value = currentConfig.logging?.webhookSenderName || 'POS Fehlerprotokoll';
    }

    jsonEditor.addEventListener('input', () => {
      clearTimeout(jsonEditor.parseTimeout);
      jsonEditor.parseTimeout = setTimeout(() => {
        try {
          const jsonContent = jsonEditor.value.trim();
          if (jsonContent) {
            JSON.parse(jsonContent);
            errorMessage.style.display = 'none';
          }
        } catch (error) {
          showError(`Ungültiges JSON: ${error.message}`);
        }
      }, 1000);
    });

    ipcRenderer.on('load-config', (event, config) => {
      currentConfig = JSON.parse(JSON.stringify(config));
      originalConfig = JSON.parse(JSON.stringify(config));
      initializeFormValues();
      updateJsonEditor();
    });

    ipcRenderer.on('save-config-result', (event, result) => {
      if (result.success) {
        showSuccess('Konfiguration erfolgreich gespeichert. Einige Änderungen erfordern möglicherweise einen Neustart der Anwendung.');
        originalConfig = JSON.parse(JSON.stringify(currentConfig));
      } else {
        showError(`Konfiguration konnte nicht gespeichert werden: ${result.error}`);
      }
    });

    saveBtn.addEventListener('click', () => {
      try {
        const activeTab = document.querySelector('.config-tab.active');
        if (activeTab && activeTab.dataset.tab === 'json') {
          try {
            const jsonContent = jsonEditor.value.trim();
            if (jsonContent) {
              currentConfig = JSON.parse(jsonContent);
            }
          } catch (error) {
            showError(`Ungültiges JSON: ${error.message}`);
            return;
          }
        } else {
          updateConfigFromForm();
        }
        ipcRenderer.send('save-config', currentConfig);
      } catch (error) {
        showError(`Fehler beim Speichern der Konfiguration: ${error.message}`);
      }
    });

    resetBtn.addEventListener('click', () => {
      currentConfig = JSON.parse(JSON.stringify(originalConfig));
      initializeFormValues();
      const activeTab = document.querySelector('.config-tab.active');
      if (activeTab && activeTab.dataset.tab === 'json') {
        updateJsonEditor();
      }
      showSuccess('Änderungen wurden auf die zuletzt gespeicherte Konfiguration zurückgesetzt.');
    });

    Object.values(formElements).flat().forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.addEventListener('change', () => {
          const activeTab = document.querySelector('.config-tab.active');
          if (!activeTab || activeTab.dataset.tab !== 'json') {
            try {
              updateConfigFromForm();
            } catch (error) {
              showError(`Fehler beim Aktualisieren der Konfiguration: ${error.message}`);
            }
          }
        });
      }
    });

    // Keine Toggle-Buttons mehr für die Sichtbarkeit
  </script>
</body>
</html>
