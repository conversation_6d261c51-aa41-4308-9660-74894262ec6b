<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Kassensystem wird geladen...</title>
  <style>
    body, html {
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
      flex-direction: column;
    }
    .loader {
      border: 8px solid #f3f3f3;
      border-radius: 50%;
      border-top: 8px solid #3498db;
      width: 60px;
      height: 60px;
      animation: spin 2s linear infinite;
      margin-bottom: 20px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .message {
      font-size: 18px;
      color: #333;
      margin-bottom: 10px;
      font-weight: bold;
    }
    .submessage {
      font-size: 14px;
      color: #666;
      max-width: 80%;
      text-align: center;
      margin-bottom: 20px;
    }
    .status-container {
      width: 450px;
      max-width: 450px;
      margin-top: 20px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 15px;
      background-color: #fff;
    }
    .status-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      padding: 8px;
      border-radius: 4px;
      background-color: #f9f9f9;
    }
    .status-icon {
      width: 24px;
      height: 24px;
      margin-right: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      font-size: 14px;
    }
    .status-pending {
      background-color: #f0f0f0;
      color: #999;
    }
    .status-loading {
      background-color: #ffeaa7;
      color: #fdcb6e;
      animation: pulse 1.5s infinite;
    }
    .status-success {
      background-color: #d4edda;
      color: #28a745;
    }
    .status-error {
      background-color: #f8d7da;
      color: #dc3545;
    }
    .status-warning {
      background-color: #fff3cd;
      color: #ffc107;
    }
    .status-text {
      flex-grow: 1;
    }
    .status-time {
      font-size: 12px;
      color: #999;
      min-width: 70px;
      text-align: right;
    }
    @keyframes pulse {
      0% { opacity: 0.6; }
      50% { opacity: 1; }
      100% { opacity: 0.6; }
    }
  </style>
</head>
<body>
  <div class="message">Kassensystem wird initialisiert</div>
  <div class="submessage">Bitte warten Sie, während alle Komponenten geladen werden.</div>

  <div class="status-container">
    <div class="status-item" id="config-status">
      <span class="status-icon status-pending">⏳</span>
      <span class="status-text">Konfiguration wird geladen...</span>
      <span class="status-time"></span>
    </div>

    <div class="status-item" id="tse-status">
      <span class="status-icon status-pending">⏳</span>
      <span class="status-text">TSE-System wird gestartet...</span>
      <span class="status-time"></span>
    </div>

    <div class="status-item" id="mqtt-status">
      <span class="status-icon status-pending">⏳</span>
      <span class="status-text">MQTT-Verbindung wird hergestellt...</span>
      <span class="status-time"></span>
    </div>

    <div class="status-item" id="printer-status">
      <span class="status-icon status-pending">⏳</span>
      <span class="status-text">Drucker wird verbunden...</span>
      <span class="status-time"></span>
    </div>

    <div class="status-item" id="display-status">
      <span class="status-icon status-pending">⏳</span>
      <span class="status-text">Kundendisplay wird vorbereitet...</span>
      <span class="status-time"></span>
    </div>
  </div>

  <div id="error-actions" style="display: none; margin-top: 20px; text-align: center;">
    <button id="continue-anyway-button" style="padding: 10px 20px; background-color: #ffc107; color: #212529; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
      Trotz Fehler fortfahren?
    </button>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      console.log('Ladebildschirm geladen, warte auf Status-Updates');

      // Status-Container prüfen
      const statusContainer = document.querySelector('.status-container');
      if (!statusContainer) {
        console.error('Status-Container nicht gefunden!');
      } else {
        console.log('Status-Container gefunden');
      }

      // Status-Elemente erzeugen, falls nicht vorhanden
      const components = ['config', 'tse', 'mqtt', 'printer', 'display', 'cart'];
      components.forEach(component => {
        if (!document.getElementById(`${component}-status`)) {
          const statusItem = document.createElement('div');
          statusItem.id = `${component}-status`;
          statusItem.className = 'status-item';
          statusItem.innerHTML = `
            <span class="status-icon status-pending">⏳</span>
            <span class="status-text">${component} wird initialisiert...</span>
            <span class="status-time"></span>
          `;
          statusContainer.appendChild(statusItem);
          console.log(`Status-Element für ${component} erzeugt`);
        }
      });

      // Funktion zum Prüfen, ob Fehler vorhanden sind
      function checkForErrors() {
        const components = ['config', 'tse', 'mqtt', 'printer', 'display', 'cart'];
        let hasErrors = false;

        components.forEach(component => {
          const statusItem = document.getElementById(`${component}-status`);
          if (statusItem) {
            const statusIcon = statusItem.querySelector('.status-icon');
            if (statusIcon && statusIcon.classList.contains('status-error')) {
              hasErrors = true;
            }
          }
        });

        // Zeige den "Trotzdem fortfahren"-Button, wenn Fehler vorhanden sind
        const errorActions = document.getElementById('error-actions');
        if (errorActions) {
          errorActions.style.display = hasErrors ? 'block' : 'none';
        }

        return hasErrors;
      }

      // Status-Update-Funktion
      window.updateStatusItem = function(itemId, status, message, time) {
        console.log(`Status-Update für ${itemId}: ${status} - ${message}`);

        const statusItem = document.getElementById(`${itemId}-status`);
        if (!statusItem) {
          console.error(`Status-Element für ${itemId} nicht gefunden!`);
          return;
        }

        const statusIcon = statusItem.querySelector('.status-icon');
        const statusText = statusItem.querySelector('.status-text');
        const statusTime = statusItem.querySelector('.status-time');

        if (!statusIcon || !statusText || !statusTime) {
          console.error(`Status-Unterelemente für ${itemId} nicht gefunden!`);
          return;
        }

        // Status-Icon aktualisieren
        statusIcon.className = 'status-icon';
        let icon = '⏳';

        switch(status) {
          case 'pending':
            statusIcon.classList.add('status-pending');
            icon = '⏳';
            break;
          case 'loading':
            statusIcon.classList.add('status-loading');
            icon = '⏳';
            break;
          case 'success':
            statusIcon.classList.add('status-success');
            icon = '✓';
            break;
          case 'error':
            statusIcon.classList.add('status-error');
            icon = '✗';
            break;
          case 'warning':
            statusIcon.classList.add('status-warning');
            icon = '⚠';
            break;
        }

        statusIcon.innerHTML = icon;

        // Text aktualisieren, wenn eine Nachricht vorhanden ist
        if (message) {
          statusText.innerHTML = message;
        }

        // Zeit aktualisieren, wenn vorhanden
        if (time) {
          statusTime.innerHTML = time;
        }

        console.log(`Status für ${itemId} aktualisiert zu ${status}`);

        // Prüfe nach jedem Status-Update, ob Fehler vorhanden sind
        checkForErrors();
      };

      // Button-Event-Listener einrichten
      const continueButton = document.getElementById('continue-anyway-button');
      if (continueButton) {
        continueButton.addEventListener('click', () => {
          console.log('Benutzer hat "Trotzdem fortfahren" geklickt');
          if (window.electron && window.electron.continueAnyway) {
            window.electron.continueAnyway();
          }
        });
      }

      // IPC Event Listener einrichten
      if (window.electron) {
        window.electron.onUpdateStatus((data) => {
          console.log('Status-Update von IPC empfangen:', data);
          window.updateStatusItem(
            data.component,
            data.status,
            data.message,
            data.time
          );
        });

        window.electron.onCriticalError((data) => {
          console.error('Kritischer Fehler:', data.message);
          alert('Kritischer Fehler: ' + data.message);
        });

        // Listener für das Anzeigen des "Trotzdem fortfahren"-Buttons
        window.electron.onShowContinueButton(() => {
          console.log('Zeige "Trotzdem fortfahren"-Button an');
          const errorActions = document.getElementById('error-actions');
          if (errorActions) {
            errorActions.style.display = 'block';
          }
        });

        console.log('IPC-Listener für Status-Updates eingerichtet');
      } else {
        console.error('Electron API nicht verfügbar!');
      }
    });
  </script>
</body>
</html>