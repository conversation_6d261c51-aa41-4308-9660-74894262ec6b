const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');

// Sichere Schnittstelle für die Kommunikation mit dem Hauptprozess
contextBridge.exposeInMainWorld('monitoringApi', {
  // Transaktionen laden
  getTransactions: async (options = {}) => {
    return await ipcRenderer.invoke('get-transactions', options);
  },

  // Transaktionsdetails laden
  getTransactionDetails: async (transactionId) => {
    return await ipc<PERSON>ender<PERSON>.invoke('get-transaction-details', transactionId);
  },

  // Empfangen von Transaktionsdaten beim Laden der Seite
  onTransactionsLoaded: (callback) => {
    ipcRenderer.on('transactions-loaded', (event, data) => callback(data));
  },

  // Empfangen von Fehlern
  onTransactionsError: (callback) => {
    ipcRenderer.on('transactions-error', (event, data) => callback(data));
  },

  // Fenster schließen
  closeWindow: () => {
    ipcRenderer.send('close-monitoring-window');
  },

  // Aktuelles Fenster drucken
  printCurrentWindow: () => {
    ipcRenderer.send('print-monitoring-window');
  },

  // Formatierungshilfen
  formatDate: (timestamp) => {
    if (!timestamp) return '-';
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  },

  formatAmount: (amount) => {
    if (amount === undefined || amount === null) return '-';

    // Wenn der Betrag bereits als String mit Komma formatiert ist
    if (typeof amount === 'string' && amount.includes(',')) {
      return amount + ' €';
    }

    // Wenn der Betrag als Zahl vorliegt
    try {
      const numAmount = parseFloat(amount);
      return numAmount.toLocaleString('de-DE', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }) + ' €';
    } catch (e) {
      return amount + ' €';
    }
  },

  formatStatus: (status) => {
    if (!status) return '-';

    const statusMap = {
      'PENDING': 'Ausstehend',
      'COMPLETED': 'Abgeschlossen',
      'ABORTED': 'Abgebrochen',
      'FAILED': 'Fehlgeschlagen',
      'ERROR': 'Fehler',
      'FINISHED': 'Abgeschlossen',
      'CANCELED': 'Storniert',
      'CREATED': 'Erstellt'
    };

    return statusMap[status.toUpperCase()] || status;
  },

  formatPaymentMethod: (method) => {
    if (!method) return '-';

    const methodMap = {
      'CASH': 'Bargeld',
      'CARD': 'Kartenzahlung',
      'BAR': 'Bargeld',
      'EC': 'EC-Karte',
      'CREDIT': 'Kreditkarte',
      'OTHER': 'Andere'
    };

    return methodMap[method.toUpperCase()] || method;
  },
  
  // Beleg nachdrucken
  printReceipt: async (transactionId, receiptType) => {
    return await ipcRenderer.invoke('print-card-receipt', { transactionId, receiptType });
  },

  // TSE-Transaktion mit 0€ abschließen
  finishTransaction: async (transactionNumber, transactionId) => {
    return await ipcRenderer.invoke('finish-tse-transaction', { transactionNumber, transactionId });
  }
});
