// Modul für die TSE-Revisionsdatenbank
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const loggerService = require('./logger-service');
const log = loggerService.getModuleLogger('TSEDatabase');
const fs = require('fs');
const { app } = require('electron');

class TseDatabase {
  constructor(config = {}) {
    // Haupt-Anwendungsdatenverzeichnis
    let appDataPath;

    try {
      // Anwendungsdatenverzeichnis abrufen (funktioniert nur im Hauptprozess)
      if (app && typeof app.getPath === 'function') {
        appDataPath = path.join(app.getPath('userData'), 'data');
      }
    } catch (error) {
      log.warn('Konnte app.getPath nicht verwenden, verwende Fallback:', error.message);
    }

    // Fallback, wenn app.getPath nicht verfügbar ist (im Renderer-Prozess) oder fehlschlägt
    if (!appDataPath) {
      if (process.type === 'renderer') {
        log.info('Renderer-Prozess erkannt, verwende remote.app');
        try {
          // Ab Electron 14 müssen wir contextBridge verwenden
          if (window && window.electron && window.electron.getAppPath) {
            appDataPath = window.electron.getAppPath();
          }
        } catch (remoteError) {
          log.warn('Remote-Zugriff fehlgeschlagen:', remoteError.message);
        }
      }

      // Letzter Fallback: Verwende standardmäßige Pfade
      if (!appDataPath) {
        const userDataFolder = 'wizidpos-data';
        appDataPath = path.join(
          process.env.APPDATA ||
          process.env.HOME && path.join(process.env.HOME, '.config') ||
          __dirname,
          userDataFolder,
          'data'
        );
      }
    }

    // Stelle sicher, dass das Datenverzeichnis existiert
    if (!fs.existsSync(appDataPath)) {
      fs.mkdirSync(appDataPath, { recursive: true });
      log.info('Anwendungsdatenverzeichnis erstellt:', appDataPath);
    }

    // Pfad zur TSE-Revisionsdatenbank
    this.dbPath = config.dbPath || path.join(appDataPath, 'tse-revisions.db');
    this.db = null;
    this.initialized = false;

    log.info('TSE-Revisionsdatenbank initialisiert mit Pfad:', this.dbPath);
  }

  /**
   * Initialisiert die Datenbank und erstellt die Tabellen falls nötig
   */
  async initialize() {
    log.info('Initialisiere die Datenbank...');
    return new Promise((resolve, reject) => {
      try {
        // Stelle sicher, dass das Verzeichnis existiert
        const dbDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dbDir)) {
          fs.mkdirSync(dbDir, { recursive: true });
          log.info('Datenbankverzeichnis erstellt:', dbDir);
        }

        this.db = new sqlite3.Database(this.dbPath, (err) => {
          if (err) {
            log.error('Fehler beim Öffnen der Datenbank:', err.message);
            reject(err);
            return;
          }

          // Aktiviere WAL-Modus für bessere Leistung
          this.db.exec('PRAGMA journal_mode = WAL;', (pragmaErr) => {
            if (pragmaErr) {
              log.warn('Konnte WAL-Modus nicht aktivieren:', pragmaErr.message);
            }

            // Transaktionen-Tabelle erstellen
            const createTableSql = `
            CREATE TABLE IF NOT EXISTS tse_transactions (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              fiskaly_client TEXT NOT NULL,
              tse_transaction_id TEXT NOT NULL,
              cash_register_id TEXT NOT NULL,
              transaction_id TEXT NOT NULL,
              transaction_number INTEGER,
              transaction_counter INTEGER,
              start INTEGER,
              end INTEGER,
              state TEXT,
              type TEXT,
              start_signature TEXT,
              end_signature TEXT,
              process_data TEXT,
              created_at INTEGER DEFAULT (strftime('%s', 'now')),
              UNIQUE(tse_transaction_id, transaction_id)
            )`;

            this.db.exec(createTableSql, (tableErr) => {
              if (tableErr) {
                log.error('Fehler beim Erstellen der Tabelle:', tableErr.message);
                reject(tableErr);
                return;
              }

              // Index für schnelle Suchen erstellen
              const indexSql = `
              CREATE INDEX IF NOT EXISTS idx_transaction ON tse_transactions(transaction_id);
              CREATE INDEX IF NOT EXISTS idx_cash_register ON tse_transactions(cash_register_id);
              CREATE INDEX IF NOT EXISTS idx_tse_transaction ON tse_transactions(tse_transaction_id);
              CREATE INDEX IF NOT EXISTS idx_time ON tse_transactions(start, end);
              `;

              this.db.exec(indexSql, (indexErr) => {
                if (indexErr) {
                  log.warn('Fehler beim Erstellen der Indizes:', indexErr.message);
                }

                // Tabelle für Barzahlungen erstellen
                const createCashTableSql = `
                CREATE TABLE IF NOT EXISTS cash_transactions (
                  id INTEGER PRIMARY KEY AUTOINCREMENT,
                  transaction_id TEXT NOT NULL UNIQUE,
                  amount REAL NOT NULL,
                  status TEXT NOT NULL,
                  created_at INTEGER DEFAULT (strftime('%s', 'now')),
                  completed_at INTEGER,
                  receipt_number TEXT,
                  checkout_id TEXT
                )`;

                this.db.exec(createCashTableSql, (cashTableErr) => {
                  if (cashTableErr) {
                    log.error('Fehler beim Erstellen der Barzahlungstabelle:', cashTableErr.message);
                  } else {
                    // Index für schnelle Suchen erstellen
                    const cashIndexSql = `
                    CREATE INDEX IF NOT EXISTS idx_cash_transaction ON cash_transactions(transaction_id);
                    CREATE INDEX IF NOT EXISTS idx_cash_status ON cash_transactions(status);
                    CREATE INDEX IF NOT EXISTS idx_cash_time ON cash_transactions(created_at, completed_at);
                    CREATE INDEX IF NOT EXISTS idx_cash_checkout ON cash_transactions(checkout_id);
                    `;

                    this.db.exec(cashIndexSql, (indexErr) => {
                      if (indexErr) {
                        log.warn('Fehler beim Erstellen der Barzahlungsindizes:', indexErr.message);
                      }
                    });
                  }

                  // Tabelle für Kartenzahlungen erstellen
                  const createCardTableSql = `
                  CREATE TABLE IF NOT EXISTS card_transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transaction_id TEXT NOT NULL UNIQUE,
                    amount REAL NOT NULL,
                    status TEXT NOT NULL,
                    created_at INTEGER DEFAULT (strftime('%s', 'now')),
                    completed_at INTEGER,
                    customer_receipt TEXT,
                    merchant_receipt TEXT,
                    card_type TEXT,
                    card_number TEXT,
                    auth_code TEXT,
                    receipt_number TEXT,
                    trace_number TEXT,
                    terminal_id TEXT,
                    checkout_id TEXT
                  )`;

                  this.db.exec(createCardTableSql, (cardTableErr) => {
                    if (cardTableErr) {
                      log.error('Fehler beim Erstellen der Kartenzahlungstabelle:', cardTableErr.message);
                    } else {
                      // Index für schnelle Suchen erstellen
                      const cardIndexSql = `
                      CREATE INDEX IF NOT EXISTS idx_card_transaction ON card_transactions(transaction_id);
                      CREATE INDEX IF NOT EXISTS idx_card_status ON card_transactions(status);
                      CREATE INDEX IF NOT EXISTS idx_card_time ON card_transactions(created_at, completed_at);
                      CREATE INDEX IF NOT EXISTS idx_card_checkout ON card_transactions(checkout_id);
                      `;

                      this.db.exec(cardIndexSql, (indexErr) => {
                        if (indexErr) {
                          log.warn('Fehler beim Erstellen der Kartenzahlungsindizes:', indexErr.message);
                        }
                      });
                    }

                    // Tabelle für Transaktionsreferenzen erstellen
                    const createRefTableSql = `
                    CREATE TABLE IF NOT EXISTS transaction_references (
                      id INTEGER PRIMARY KEY AUTOINCREMENT,
                      tse_transaction_id TEXT NOT NULL,
                      payment_transaction_id TEXT NOT NULL,
                      checkout_id TEXT,
                      reference_type TEXT NOT NULL,
                      final_payment_method TEXT,
                      created_at INTEGER DEFAULT (strftime('%s', 'now')),
                      UNIQUE(tse_transaction_id, payment_transaction_id)
                    )`;

                    this.db.exec(createRefTableSql, (refTableErr) => {
                      if (refTableErr) {
                        log.error('Fehler beim Erstellen der Referenztabelle:', refTableErr.message);
                      } else {
                        // Index für schnelle Suchen erstellen
                        const refIndexSql = `
                        CREATE INDEX IF NOT EXISTS idx_ref_tse ON transaction_references(tse_transaction_id);
                        CREATE INDEX IF NOT EXISTS idx_ref_payment ON transaction_references(payment_transaction_id);
                        CREATE INDEX IF NOT EXISTS idx_ref_checkout ON transaction_references(checkout_id);
                        CREATE INDEX IF NOT EXISTS idx_ref_type ON transaction_references(reference_type);
                        CREATE INDEX IF NOT EXISTS idx_ref_payment_method ON transaction_references(final_payment_method);
                        `;

                        this.db.exec(refIndexSql, (indexErr) => {
                          if (indexErr) {
                            log.warn('Fehler beim Erstellen der Referenzindizes:', indexErr.message);
                          }

                          // Erstelle die View für die Transaktionsübersicht
                          const createViewSql = `
                          CREATE VIEW IF NOT EXISTS transactions_view AS
                          SELECT
                              t.id AS tse_id,
                              t.transaction_id AS tse_transaction_id,
                              t.tse_transaction_id AS fiskaly_transaction_id,
                              t.cash_register_id,
                              t.transaction_number,
                              t.transaction_counter,
                              t.start AS tse_start,
                              t.end AS tse_end,
                              t.state AS tse_state,
                              t.type AS tse_type,
                              t.start_signature,
                              t.end_signature,
                              t.created_at AS tse_created_at,

                              c.id AS card_id,
                              c.transaction_id AS card_transaction_id,
                              c.amount AS card_amount,
                              c.status AS card_status,
                              c.created_at AS card_created_at,
                              c.completed_at AS card_completed_at,
                              c.card_type,
                              c.card_number,
                              c.auth_code,
                              c.customer_receipt,
                              c.merchant_receipt,

                              cs.id AS cash_id,
                              cs.transaction_id AS cash_transaction_id,
                              cs.amount AS cash_amount,
                              cs.status AS cash_status,
                              cs.created_at AS cash_created_at,
                              cs.completed_at AS cash_completed_at,

                              r.id AS reference_id,
                              r.checkout_id,
                              r.reference_type,
                              r.final_payment_method AS initial_payment_method,
                              r.created_at AS reference_created_at,

                              CASE
                                  WHEN c.id IS NOT NULL AND c.status = 'COMPLETED' THEN 'CARD'
                                  WHEN cs.id IS NOT NULL AND cs.status = 'REPLACED_BY_CARD' THEN 'CARD'
                                  WHEN cs.id IS NOT NULL AND cs.status = 'COMPLETED' THEN 'CASH'
                                  WHEN r.final_payment_method IS NOT NULL THEN r.final_payment_method
                                  WHEN c.id IS NOT NULL THEN 'CARD'
                                  WHEN cs.id IS NOT NULL THEN 'CASH'
                                  ELSE 'UNKNOWN'
                              END AS effective_payment_method,

                              CASE
                                  WHEN c.id IS NOT NULL THEN c.amount
                                  WHEN cs.id IS NOT NULL THEN cs.amount
                                  ELSE NULL
                              END AS payment_amount
                          FROM
                              tse_transactions t
                          LEFT JOIN
                              transaction_references r ON t.tse_transaction_id = r.tse_transaction_id
                          LEFT JOIN
                              card_transactions c ON (r.payment_transaction_id = c.transaction_id OR t.transaction_id = c.transaction_id OR r.checkout_id = c.checkout_id)
                          LEFT JOIN
                              cash_transactions cs ON (r.payment_transaction_id = cs.transaction_id OR t.transaction_id = cs.transaction_id OR r.checkout_id = cs.checkout_id)
                          ORDER BY
                              t.created_at DESC`;

                          this.db.exec(createViewSql, (viewErr) => {
                            if (viewErr) {
                              log.warn('Fehler beim Erstellen der Transaktionsübersicht-View:', viewErr.message);
                            } else {
                              log.info('Transaktionsübersicht-View erfolgreich erstellt');
                            }

                            log.info('TSE-Revisionsdatenbank erfolgreich initialisiert');
                            this.initialized = true;
                            resolve(true);
                          });
                        });
                      }
                    });
                  });
                });
              });
            });
          });
        });
      } catch (error) {
        log.error('Unbehandelte Ausnahme bei Datenbankinitialisierung:', error);
        reject(error);
      }
    });
  }

  /**
   * Speichert Transaktionsdaten in der Datenbank
   */
  async saveTransaction(transactionData) {
    if (!this.initialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const sql = `INSERT OR REPLACE INTO tse_transactions
        (fiskaly_client, tse_transaction_id, cash_register_id, transaction_id,
         transaction_number, transaction_counter, start, end, state, type,
         start_signature, end_signature, process_data)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

      const params = [
        transactionData.fiskaly_client || '',
        transactionData.tse_transaction_id || '',
        transactionData.cash_register_id || '',
        transactionData.transaction_id || '',
        transactionData.transaction_number || 0,
        transactionData.transaction_counter || 0,
        transactionData.start || 0,
        transactionData.end || 0,
        transactionData.state || '',
        transactionData.type || '',
        transactionData.start_signature || '',
        transactionData.end_signature || '',
        transactionData.process_data || ''
      ];

      this.db.run(sql, params, function(err) {
        if (err) {
          log.error('Fehler beim Speichern der Transaktion:', err.message);
          reject(err);
          return;
        }

        log.info(`Transaktion erfolgreich gespeichert: ${transactionData.transaction_id}, ID: ${this.lastID}`);
        resolve({ id: this.lastID, transactionId: transactionData.transaction_id });
      });
    });
  }

  /**
   * Findet Transaktionen anhand der Wizid-Transaktions-ID
   */
  async findTransactionByWizidId(transactionId) {
    if (!this.initialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM tse_transactions WHERE transaction_id = ? ORDER BY created_at DESC LIMIT 1`;

      this.db.get(sql, [transactionId], (err, row) => {
        if (err) {
          log.error('Fehler bei Transaktionssuche:', err.message);
          reject(err);
          return;
        }

        resolve(row || null);
      });
    });
  }

  /**
   * Liste aller Transaktionen abrufen (mit Pagination)
   */
  async getTransactions(page = 1, limit = 100) {
    if (!this.initialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const offset = (page - 1) * limit;
      const sql = `SELECT * FROM tse_transactions ORDER BY created_at DESC LIMIT ? OFFSET ?`;

      this.db.all(sql, [limit, offset], (err, rows) => {
        if (err) {
          log.error('Fehler beim Abrufen der Transaktionen:', err.message);
          reject(err);
          return;
        }

        resolve(rows || []);
      });
    });
  }

  /**
   * Speichert eine neue Barzahlung
   * Wenn bereits eine Kartenzahlung oder eine andere Barzahlung mit COMPLETED-Status existiert,
   * wird diese Barzahlung nicht gespeichert
   * @param {Object} cashData Die Barzahlungsdaten
   * @returns {Promise<Object>} Das Ergebnis mit ID
   */
  async saveCashTransaction(cashData) {
    if (!this.initialized) {
      await this.initialize();
    }

    // Debug-Logging der eingehenden Checkout-ID
    const transactionId = cashData.transaction_id || cashData.id || '';
    const checkoutId = cashData.checkout_id || '';

    // Analyse des Transaktions-Formats
    const isPaymentFormat = transactionId.includes('transaction_payment_');
    const baseTransactionId = isPaymentFormat ?
      transactionId.replace('transaction_payment_', 'transaction_') : transactionId;

    log.info(`saveCashTransaction aufgerufen mit checkout_id: "${checkoutId}" für Transaktion ${transactionId}`);
    log.info(`Transaktionsformat: ${isPaymentFormat ? 'Payment-Format' : 'Standard-Format'}, Basis-ID: ${baseTransactionId}`);

    // Falls keine checkout_id übergeben wurde, versuchen wir sie aus anderen Quellen zu bekommen
    let effectiveCheckoutId = checkoutId;

    if (!effectiveCheckoutId) {
      // Falls wir eine Referenz zur gleichen Transaktion in der transaction_references Tabelle haben,
      // versuchen wir diese checkout_id zu verwenden
      try {
        const refSql = `SELECT checkout_id FROM transaction_references WHERE payment_transaction_id = ? LIMIT 1`;
        const refRow = await new Promise((resolve) => {
          this.db.get(refSql, [transactionId], (err, row) => {
            if (err) {
              log.warn(`Fehler bei der Suche nach Referenz-checkout_id für ${transactionId}: ${err.message}`);
              resolve(null);
            } else {
              resolve(row);
            }
          });
        });

        if (refRow && refRow.checkout_id) {
          effectiveCheckoutId = refRow.checkout_id;
          log.info(`Gefundene checkout_id aus transaction_references: "${effectiveCheckoutId}" für Transaktion ${transactionId}`);
        }
      } catch (error) {
        log.warn(`Fehler beim Abrufen der checkout_id aus Referenzen: ${error.message}`);
      }
    }

    // Prüfe zunächst, ob bereits eine Kartenzahlung existiert
    const cardTransaction = await this.checkCardTransaction(transactionId);
    if (cardTransaction.exists) {
      log.info(`Kartenzahlung für ${transactionId} existiert bereits, ignoriere Barzahlung`);
      return { id: null, transactionId, exists: true, ignored: true };
    }

    // Prüfe, ob bereits eine abgeschlossene Barzahlung existiert
    const cashTransaction = await this.checkCashTransaction(transactionId);
    if (cashTransaction.exists && cashTransaction.data.status === 'COMPLETED') {
      log.info(`Barzahlung für ${transactionId} existiert bereits mit Status COMPLETED, ignoriere`);
      return { id: null, transactionId, exists: true };
    }

    // Aktualisiere die final_payment_method in der transaction_references Tabelle
    if (cashData.status === 'COMPLETED') {
      await this._updateFinalPaymentMethod(transactionId, baseTransactionId, 'CASH');
    }

    return new Promise((resolve, reject) => {
      log.info(`Speichere Barzahlung für ${transactionId} mit checkout_id: "${effectiveCheckoutId}"`);

      const sql = `INSERT OR REPLACE INTO cash_transactions
        (transaction_id, amount, status, created_at, completed_at, checkout_id)
        VALUES (?, ?, ?, ?, ?, ?)`;

      const currentTime = Math.floor(Date.now() / 1000);
      const params = [
        transactionId,
        cashData.amount || 0,
        cashData.status || 'COMPLETED',
        currentTime,
        currentTime, // Bei Barzahlungen ist completed_at gleich created_at
        effectiveCheckoutId  // Explizit checkout_id setzen
      ];

      this.db.run(sql, params, function(err) {
        if (err) {
          log.error(`Fehler beim Speichern der Barzahlung: ${err.message}`);
          reject(err);
          return;
        }

        log.info(`Barzahlung erfolgreich gespeichert: ${transactionId}, ID: ${this.lastID}, checkout_id: "${effectiveCheckoutId}"`);
        resolve({ id: this.lastID, transactionId, exists: false });
      });
    });
  }

  /**
   * Speichert eine neue Kartenzahlung
   * Wenn eine Barzahlung für die gleiche Transaktion existiert, wird diese auf REPLACED_BY_CARD gesetzt
   * @param {Object} cardData Die Kartenzahlungsdaten
   * @returns {Promise<Object>} Das Ergebnis mit ID
   */
  async saveCardTransaction(cardData) {
    if (!this.initialized) {
      await this.initialize();
    }

    // Debug-Logging der eingehenden Checkout-ID
    const transactionId = cardData.transaction_id || cardData.id || '';
    const checkoutId = cardData.checkout_id || '';

    // Analyse des Transaktions-Formats
    const isPaymentFormat = transactionId.includes('transaction_payment_');
    const baseTransactionId = isPaymentFormat ?
      transactionId.replace('transaction_payment_', 'transaction_') : transactionId;

    log.info(`saveCardTransaction aufgerufen mit checkout_id: "${checkoutId}" für Transaktion ${transactionId}`);
    log.info(`Transaktionsformat: ${isPaymentFormat ? 'Payment-Format' : 'Standard-Format'}, Basis-ID: ${baseTransactionId}`);

    // Falls keine checkout_id übergeben wurde, versuchen wir sie aus anderen Quellen zu bekommen
    let effectiveCheckoutId = checkoutId;

    if (!effectiveCheckoutId) {
      // 1. Versuche, die checkout_id aus einer vorhandenen Barzahlung zu holen (sowohl original als auch basis ID)
      try {
        // Prüfe sowohl original als auch Basis-ID
        const cashSql = `SELECT checkout_id FROM cash_transactions
                        WHERE transaction_id IN (?, ?) AND checkout_id IS NOT NULL AND checkout_id != ''
                        LIMIT 1`;

        const cashRow = await new Promise((resolve) => {
          this.db.get(cashSql, [transactionId, baseTransactionId], (err, row) => {
            if (err) {
              log.warn(`Fehler bei der Suche nach Cash-checkout_id für ${transactionId}: ${err.message}`);
              resolve(null);
            } else {
              resolve(row);
            }
          });
        });

        if (cashRow && cashRow.checkout_id) {
          effectiveCheckoutId = cashRow.checkout_id;
          log.info(`Gefundene checkout_id aus cash_transactions: "${effectiveCheckoutId}" für Transaktion ${transactionId}`);
        }
      } catch (error) {
        log.warn(`Fehler beim Abrufen der checkout_id aus Barzahlungen: ${error.message}`);
      }

      // 2. Falls immer noch nicht gefunden, versuche es in der Referenztabelle
      if (!effectiveCheckoutId) {
        try {
          // Prüfe sowohl original als auch Basis-ID
          const refSql = `SELECT checkout_id FROM transaction_references
                        WHERE payment_transaction_id IN (?, ?) AND checkout_id IS NOT NULL AND checkout_id != ''
                        LIMIT 1`;

          const refRow = await new Promise((resolve) => {
            this.db.get(refSql, [transactionId, baseTransactionId], (err, row) => {
              if (err) {
                log.warn(`Fehler bei der Suche nach Referenz-checkout_id für ${transactionId}: ${err.message}`);
                resolve(null);
              } else {
                resolve(row);
              }
            });
          });

          if (refRow && refRow.checkout_id) {
            effectiveCheckoutId = refRow.checkout_id;
            log.info(`Gefundene checkout_id aus transaction_references: "${effectiveCheckoutId}" für Transaktion ${transactionId}`);
          }
        } catch (error) {
          log.warn(`Fehler beim Abrufen der checkout_id aus Referenzen: ${error.message}`);
        }
      }
    }

    return new Promise((resolve, reject) => {
      log.info(`Speichere Kartenzahlung für ${transactionId} mit checkout_id: "${effectiveCheckoutId}"`);

      const sql = `INSERT OR REPLACE INTO card_transactions
        (transaction_id, amount, status, created_at, checkout_id,
         customer_receipt, merchant_receipt, card_type, card_number,
         auth_code, receipt_number, trace_number, terminal_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

      // Betrag normalisieren: Wenn der Betrag größer als 100 ist, könnte er in Cent sein
      let amount = cardData.amount || 0;
      if (amount > 100) {
        amount = amount / 100;
      }

      log.info(`Normalisiere Betrag für Kartenzahlung: ${cardData.amount} -> ${amount}`);

      const params = [
        transactionId,
        amount,
        cardData.status || 'PENDING',
        Math.floor(Date.now() / 1000),
        effectiveCheckoutId,  // Explizit checkout_id setzen
        cardData.customer_receipt || null,
        cardData.merchant_receipt || null,
        cardData.card_type || null,
        cardData.card_number || null,
        cardData.auth_code || null,
        cardData.receipt_number || null,
        cardData.trace_number || null,
        cardData.terminal_id || null
      ];

      const self = this;

      this.db.run(sql, params, function(err) {
        if (err) {
          log.error(`Fehler beim Speichern der Kartenzahlung: ${err.message}`);
          reject(err);
          return;
        }

        const lastID = this.lastID;

        log.info(`Kartenzahlung erfolgreich gespeichert: ${transactionId}, ID: ${lastID}, checkout_id: "${effectiveCheckoutId}"`);

        // Markiere jede existierende Barzahlung als ersetzt (sowohl original als auch basis ID)
        const updateSql = `UPDATE cash_transactions
                         SET status = 'REPLACED_BY_CARD'
                         WHERE transaction_id IN (?, ?)`;

        // Aktualisiere die Barzahlung und dann die final_payment_method
        self.db.run(updateSql, [transactionId, baseTransactionId], async function(updateErr) {
          if (updateErr) {
            log.warn(`Fehler beim Aktualisieren der Barzahlung für ${transactionId}: ${updateErr.message}`);
          } else if (this.changes > 0) {
            log.info(`Barzahlung für ${transactionId}/${baseTransactionId} wurde als 'REPLACED_BY_CARD' markiert`);
          }

          // Aktualisiere die final_payment_method auf CARD
          try {
            await self._updateFinalPaymentMethod(transactionId, baseTransactionId, 'CARD');
            log.info(`final_payment_method für ${transactionId}/${baseTransactionId} auf CARD gesetzt nach Kartenzahlungsspeicherung`);
          } catch (methodErr) {
            log.warn(`Fehler beim Aktualisieren der final_payment_method nach Kartenzahlungsspeicherung: ${methodErr.message}`);
          }

          resolve({ id: lastID, transactionId, exists: false });
        });
      });
    });
  }

  /**
   * Speichert eine Referenz zwischen TSE-Transaktion und Zahlungstransaktion
   * @param {Object} referenceData Die Referenzdaten
   * @returns {Promise<Object>} Das Ergebnis mit ID
   */
  async saveTransactionReference(referenceData) {
    if (!this.initialized) {
      await this.initialize();
    }

    // Stelle sicher, dass checkout_id gesetzt wird
    const checkoutId = referenceData.checkout_id || '';
    const tseTransactionId = referenceData.tse_transaction_id || '';
    const paymentTransactionId = referenceData.payment_transaction_id || '';

    log.info(`Speichere Transaktionsreferenz für ${tseTransactionId} -> ${paymentTransactionId} mit checkout_id: "${checkoutId}"`);

    // Wenn checkout_id nicht übergeben wurde, aber in der Referenz vorhanden ist,
    // aktualisiere auch die zugehörigen cash_transactions und card_transactions
    if (checkoutId && paymentTransactionId) {
      try {
        // Finde die transaction_id ohne das "transaction_payment_" Präfix für Kartenzahlungen
        const baseTransactionId = paymentTransactionId.replace('transaction_payment_', 'transaction_');
        log.info(`Basis-TransaktionsID für mögliche card/cash Suche: ${baseTransactionId}`);

        // Aktualisiere die cash_transactions Tabelle - sowohl mit Original- als auch mit Basis-ID
        const cashUpdateSql = `UPDATE cash_transactions
                             SET checkout_id = ?
                             WHERE transaction_id IN (?, ?) AND (checkout_id IS NULL OR checkout_id = '')`;

        await new Promise((resolve) => {
          this.db.run(cashUpdateSql, [checkoutId, paymentTransactionId, baseTransactionId], function(err) {
            if (err) {
              log.warn(`Fehler beim Aktualisieren der checkout_id in cash_transactions für ${paymentTransactionId}: ${err.message}`);
            } else if (this.changes > 0) {
              log.info(`checkout_id für Barzahlung ${paymentTransactionId}/${baseTransactionId} aktualisiert auf "${checkoutId}"`);
            }
            resolve();
          });
        });

        // Aktualisiere die card_transactions Tabelle - sowohl mit Original- als auch mit Basis-ID
        const cardUpdateSql = `UPDATE card_transactions
                             SET checkout_id = ?
                             WHERE transaction_id IN (?, ?) AND (checkout_id IS NULL OR checkout_id = '')`;

        await new Promise((resolve) => {
          this.db.run(cardUpdateSql, [checkoutId, paymentTransactionId, baseTransactionId], function(err) {
            if (err) {
              log.warn(`Fehler beim Aktualisieren der checkout_id in card_transactions für ${paymentTransactionId}: ${err.message}`);
            } else if (this.changes > 0) {
              log.info(`checkout_id für Kartenzahlung ${paymentTransactionId}/${baseTransactionId} aktualisiert auf "${checkoutId}"`);
            }
            resolve();
          });
        });
      } catch (error) {
        log.warn(`Fehler bei der Aktualisierung der checkout_id in Zahlungstabellen: ${error.message}`);
      }
    }

    // Bestimme die initial_payment_method basierend auf dem reference_type
    let initialPaymentMethod = null;

    if (referenceData.reference_type) {
      if (referenceData.reference_type.includes('CARD')) {
        initialPaymentMethod = 'CARD';
      } else if (referenceData.reference_type.includes('CASH')) {
        initialPaymentMethod = 'CASH';
      }
    }

    // Wenn keine Zahlungsmethode bestimmt werden konnte, prüfe auf Kartenzahlung
    if (!initialPaymentMethod) {
      try {
        // Prüfe, ob eine Kartenzahlung existiert
        const cardSql = `SELECT id FROM card_transactions WHERE transaction_id = ? LIMIT 1`;
        const cardRow = await new Promise((resolve) => {
          this.db.get(cardSql, [paymentTransactionId], (err, row) => {
            if (err) {
              log.warn(`Fehler bei der Suche nach Kartenzahlung für ${paymentTransactionId}: ${err.message}`);
              resolve(null);
            } else {
              resolve(row);
            }
          });
        });

        if (cardRow) {
          initialPaymentMethod = 'CARD';
        } else {
          // Prüfe, ob eine Barzahlung existiert
          const cashSql = `SELECT id FROM cash_transactions WHERE transaction_id = ? LIMIT 1`;
          const cashRow = await new Promise((resolve) => {
            this.db.get(cashSql, [paymentTransactionId], (err, row) => {
              if (err) {
                log.warn(`Fehler bei der Suche nach Barzahlung für ${paymentTransactionId}: ${err.message}`);
                resolve(null);
              } else {
                resolve(row);
              }
            });
          });

          if (cashRow) {
            initialPaymentMethod = 'CASH';
          }
        }
      } catch (error) {
        log.warn(`Fehler bei der Bestimmung der initial_payment_method: ${error.message}`);
      }
    }

    return new Promise((resolve, reject) => {
      const sql = `INSERT OR REPLACE INTO transaction_references
        (tse_transaction_id, payment_transaction_id, checkout_id, reference_type, final_payment_method)
        VALUES (?, ?, ?, ?, ?)`;

      const params = [
        tseTransactionId,
        paymentTransactionId,
        checkoutId,
        referenceData.reference_type || 'UNKNOWN',
        initialPaymentMethod
      ];

      const self = this;
      this.db.run(sql, params, function(err) {
        if (err) {
          log.error(`Fehler beim Speichern der Transaktionsreferenz: ${err.message}`);
          reject(err);
          return;
        }

        const lastID = this.lastID;
        log.info(`Transaktionsreferenz erfolgreich gespeichert: ${tseTransactionId} -> ${paymentTransactionId}, ID: ${lastID}, checkout_id: "${checkoutId}", initial_payment_method: "${initialPaymentMethod}"`);

        // Wenn eine Zahlungsmethode bestimmt wurde, aktualisiere die final_payment_method
        if (initialPaymentMethod) {
          const baseTransactionId = paymentTransactionId.replace('transaction_payment_', 'transaction_');
          self._updateFinalPaymentMethod(paymentTransactionId, baseTransactionId, initialPaymentMethod)
            .then(() => {
              resolve({ id: lastID, exists: false });
            })
            .catch((updateErr) => {
              log.warn(`Fehler beim Aktualisieren der final_payment_method: ${updateErr.message}`);
              resolve({ id: lastID, exists: false });
            });
        } else {
          resolve({ id: lastID, exists: false });
        }
      });
    });
  }

  /**
   * Aktualisiert den Status einer Kartenzahlung
   * @param {string} transactionId Die Transaktions-ID
   * @param {Object} updateData Die zu aktualisierenden Daten
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async updateCardTransaction(transactionId, updateData) {
    if (!this.initialized) {
      await this.initialize();
    }

    // Analyse des Transaktions-Formats
    const isPaymentFormat = transactionId.includes('transaction_payment_');
    const baseTransactionId = isPaymentFormat ?
      transactionId.replace('transaction_payment_', 'transaction_') : transactionId;

    log.info(`Transaktionsformat: ${isPaymentFormat ? 'Payment-Format' : 'Standard-Format'}, Basis-ID: ${baseTransactionId}`);

    // Prüfe, ob eine Zahlungsmethode im updateData vorhanden ist
    if (updateData.status === 'COMPLETED' || updateData.merchant_receipt) {
      log.info(`Zahlungsmethode in updateData gefunden: "CARD"`);
      await this._updateFinalPaymentMethod(transactionId, baseTransactionId, 'CARD');
    }

    return new Promise((resolve, reject) => {
      // Baue die SQL-Anweisung dynamisch basierend auf den zu aktualisierenden Feldern auf
      const updates = [];
      const params = [];

      for (const [key, value] of Object.entries(updateData)) {
        // Nur erlaubte Felder aktualisieren
        if (['status', 'completed_at', 'customer_receipt', 'merchant_receipt',
             'card_type', 'card_number', 'auth_code', 'receipt_number',
             'trace_number', 'terminal_id', 'checkout_id'].includes(key)) {
          updates.push(`${key} = ?`);
          params.push(value);
        }
      }

      // Wenn keine gültigen Felder zu aktualisieren sind, beenden
      if (updates.length === 0) {
        log.warn(`Keine gültigen Felder zum Aktualisieren für Kartenzahlung: ${transactionId}`);
        resolve(false);
        return;
      }

      // Transaktions-ID hinzufügen
      params.push(transactionId);

      const sql = `UPDATE card_transactions SET ${updates.join(', ')} WHERE transaction_id = ?`;

      this.db.run(sql, params, function(err) {
        if (err) {
          log.error(`Fehler beim Aktualisieren der Kartenzahlung ${transactionId}:`, err.message);
          reject(err);
          return;
        }

        log.info(`Kartenzahlung ${transactionId} aktualisiert, ${this.changes} Zeilen betroffen`);
        resolve(this.changes > 0);
      });
    });
  }

  /**
   * Aktualisiert die final_payment_method in der transaction_references Tabelle
   * @param {string} transactionId Die Transaktions-ID
   * @param {string} baseTransactionId Die Basis-Transaktions-ID (ohne Präfix)
   * @param {string} paymentMethod Die Zahlungsmethode (CARD oder CASH)
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   * @private
   */
  async _updateFinalPaymentMethod(transactionId, baseTransactionId, paymentMethod) {
    // Bestimme die tatsächliche Zahlungsmethode basierend auf verschiedenen Faktoren
    let finalPaymentMethod = paymentMethod;

    // Prüfe zunächst, ob eine Barzahlung mit Status REPLACED_BY_CARD existiert
    try {
      const cashSql = `SELECT status FROM cash_transactions WHERE transaction_id IN (?, ?) LIMIT 1`;
      const cashRow = await new Promise((resolve) => {
        this.db.get(cashSql, [transactionId, baseTransactionId], (err, row) => {
          if (err) {
            log.warn(`Fehler bei der Suche nach cash_transaction für ${transactionId}: ${err.message}`);
            resolve(null);
          } else {
            resolve(row);
          }
        });
      });

      if (cashRow && cashRow.status === 'REPLACED_BY_CARD') {
        finalPaymentMethod = 'CARD';
        log.info(`Bestimme final_payment_method für ${transactionId}/${baseTransactionId}: CARD (Barzahlung wurde durch Karte ersetzt)`);
        return await this._updateFinalPaymentMethodInDb(transactionId, baseTransactionId, finalPaymentMethod);
      }
    } catch (error) {
      log.warn(`Fehler bei der Prüfung des Barzahlungsstatus: ${error.message}`);
    }

    // Wenn die Zahlungsmethode CARD ist, prüfe auf merchant_receipt
    if (paymentMethod === 'CARD') {
      // Prüfe, ob ein merchant_receipt vorhanden ist oder eine Kartenzahlung existiert
      try {
        const cardSql = `SELECT merchant_receipt, status FROM card_transactions WHERE transaction_id IN (?, ?) LIMIT 1`;
        const cardRow = await new Promise((resolve) => {
          this.db.get(cardSql, [transactionId, baseTransactionId], (err, row) => {
            if (err) {
              log.warn(`Fehler bei der Suche nach card_transaction für ${transactionId}: ${err.message}`);
              resolve(null);
            } else {
              resolve(row);
            }
          });
        });

        if (cardRow) {
          // Wenn eine Kartenzahlung existiert, setze die Zahlungsmethode auf CARD
          finalPaymentMethod = 'CARD';
          log.info(`Bestimme final_payment_method für ${transactionId}/${baseTransactionId}: CARD (Kartenzahlung existiert)`);
        } else {
          finalPaymentMethod = 'CASH';
          log.info(`Bestimme final_payment_method für ${transactionId}/${baseTransactionId}: CASH (keine Kartenzahlung gefunden)`);
        }
      } catch (error) {
        log.warn(`Fehler bei der Bestimmung der final_payment_method: ${error.message}`);
      }
    }

    // Aktualisiere die transaction_references Tabelle
    return await this._updateFinalPaymentMethodInDb(transactionId, baseTransactionId, finalPaymentMethod);
  }

  /**
   * Hilfsmethode zum Aktualisieren der final_payment_method in der Datenbank
   * @param {string} transactionId Die Transaktions-ID
   * @param {string} baseTransactionId Die Basis-Transaktions-ID (ohne Präfix)
   * @param {string} finalPaymentMethod Die finale Zahlungsmethode (CARD oder CASH)
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   * @private
   */
  async _updateFinalPaymentMethodInDb(transactionId, baseTransactionId, finalPaymentMethod) {
    try {
      // Prüfe, ob bereits ein Eintrag mit dieser final_payment_method existiert
      const checkSql = `SELECT final_payment_method FROM transaction_references
                      WHERE payment_transaction_id IN (?, ?) LIMIT 1`;

      const existingRow = await new Promise((resolve) => {
        this.db.get(checkSql, [transactionId, baseTransactionId], (err, row) => {
          if (err) {
            log.warn(`Fehler bei der Suche nach final_payment_method für ${transactionId}: ${err.message}`);
            resolve(null);
          } else {
            resolve(row);
          }
        });
      });

      // Wenn bereits die gleiche Zahlungsmethode gesetzt ist, nichts tun
      if (existingRow && existingRow.final_payment_method === finalPaymentMethod) {
        log.info(`Keine Änderung der final_payment_method für Transaktion ${transactionId}/${baseTransactionId} notwendig`);
        return true;
      }

      // Aktualisiere die final_payment_method
      const updateSql = `UPDATE transaction_references
                       SET final_payment_method = ?
                       WHERE payment_transaction_id IN (?, ?)`;

      const result = await new Promise((resolve) => {
        this.db.run(updateSql, [finalPaymentMethod, transactionId, baseTransactionId], function(err) {
          if (err) {
            log.warn(`Fehler beim Aktualisieren der final_payment_method für ${transactionId}: ${err.message}`);
            resolve(false);
          } else {
            log.info(`final_payment_method für Transaktion ${transactionId}/${baseTransactionId} auf ${finalPaymentMethod} gesetzt, ${this.changes} Zeilen betroffen`);
            resolve(this.changes > 0);
          }
        });
      });

      return result;
    } catch (error) {
      log.warn(`Fehler bei der Aktualisierung der final_payment_method: ${error.message}`);
      return false;
    }
  }

  /**
   * Markiert eine Kartenzahlung als abgeschlossen
   * @param {string} transactionId Die Transaktions-ID
   * @param {Object} completionData Die Abschlussdaten
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async completeCardTransaction(transactionId, completionData) {
    if (!this.initialized) {
      await this.initialize();
    }

    // Stelle sicher, dass alle Daten korrekt formatiert sind
    const updateData = {
      status: 'COMPLETED',
      completed_at: Math.floor(Date.now() / 1000),
      ...completionData
    };

    // Betrag normalisieren, falls vorhanden
    if (updateData.amount && updateData.amount > 100) {
      updateData.amount = updateData.amount / 100;
      log.info(`Normalisiere Betrag für Kartenzahlung bei Abschluss: ${completionData.amount} -> ${updateData.amount}`);
    }

    const result = await this.updateCardTransaction(transactionId, updateData);

    // Wenn die Kartenzahlung erfolgreich abgeschlossen wurde, markiere jede Barzahlung als ersetzt
    if (result) {
      const updateCashSql = `UPDATE cash_transactions
                         SET status = 'REPLACED_BY_CARD'
                         WHERE transaction_id = ?`;

      return new Promise((resolve) => {
        const self = this;
        self.db.run(updateCashSql, [transactionId], async function(updateErr) {
          if (updateErr) {
            log.warn(`Fehler beim Aktualisieren der Barzahlung für ${transactionId}: ${updateErr.message}`);
          } else if (this.changes > 0) {
            log.info(`Barzahlung für ${transactionId} wurde als 'REPLACED_BY_CARD' markiert`);
          }

          // Aktualisiere die final_payment_method auf CARD
          try {
            // Bestimme die Basis-ID
            const baseTransactionId = transactionId.includes('transaction_payment_') ?
              transactionId.replace('transaction_payment_', 'transaction_') : transactionId;

            await self._updateFinalPaymentMethod(transactionId, baseTransactionId, 'CARD');
            log.info(`final_payment_method für ${transactionId} auf CARD gesetzt nach Kartenzahlungsabschluss`);
          } catch (methodErr) {
            log.warn(`Fehler beim Aktualisieren der final_payment_method nach Kartenzahlungsabschluss: ${methodErr.message}`);
          }

          // Wir geben trotzdem das ursprüngliche Ergebnis zurück
          resolve(result);
        });
      });
    }

    return result;
  }

  /**
   * Findet alle ausstehenden Kartenzahlungen für eine Transaktion
   * @param {string} parentTransactionId Die übergeordnete Transaktions-ID
   * @returns {Promise<Array>} Liste der ausstehenden Kartenzahlungen
   */
  async findPendingCardPayments(parentTransactionId) {
    if (!this.initialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM card_transactions
                  WHERE transaction_id LIKE ? AND status = 'PENDING'`;

      // Verwende LIKE mit Platzhalter, da die transaction_id
      // entweder transaction_X oder transaction_payment_X sein kann
      const searchPattern = `%${parentTransactionId}%`;

      this.db.all(sql, [searchPattern], (err, rows) => {
        if (err) {
          log.error(`Fehler bei der Suche nach ausstehenden Kartenzahlungen für ${parentTransactionId}:`, err.message);
          reject(err);
          return;
        }

        resolve(rows || []);
      });
    });
  }

  /**
   * Prüft, ob eine Kartenzahlung existiert und abgeschlossen ist
   * @param {string} transactionId Die Transaktions-ID
   * @returns {Promise<Object>} Status-Informationen { exists: boolean, completed: boolean, data?: Object }
   */
  async checkCardTransaction(transactionId) {
    if (!this.initialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM card_transactions WHERE transaction_id = ?`;

      this.db.get(sql, [transactionId], (err, row) => {
        if (err) {
          log.error(`Fehler bei der Suche nach Kartenzahlung ${transactionId}:`, err.message);
          reject(err);
          return;
        }

        if (!row) {
          // Transaktion existiert nicht
          resolve({ exists: false, completed: false });
          return;
        }

        // Transaktion existiert, prüfe Status
        const isCompleted = row.status === 'COMPLETED';

        resolve({
          exists: true,
          completed: isCompleted,
          data: row
        });
      });
    });
  }

  /**
   * Prüft, ob eine Barzahlung existiert
   * @param {string} transactionId Die Transaktions-ID
   * @returns {Promise<Object>} Status-Informationen { exists: boolean, data?: Object }
   */
  async checkCashTransaction(transactionId) {
    if (!this.initialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM cash_transactions WHERE transaction_id = ?`;

      this.db.get(sql, [transactionId], (err, row) => {
        if (err) {
          log.error(`Fehler bei der Suche nach Barzahlung ${transactionId}:`, err.message);
          reject(err);
          return;
        }

        if (!row) {
          // Transaktion existiert nicht
          resolve({ exists: false });
          return;
        }

        // Transaktion existiert
        resolve({
          exists: true,
          data: row
        });
      });
    });
  }

  /**
   * Speichert eine neue Zahlungstransaktion
   * @param {Object} paymentData Die Zahlungsdaten
   * @returns {Promise<Object>} Das Ergebnis mit ID
   */
  async savePaymentTransaction(paymentData) {
    if (!this.initialized) {
      await this.initialize();
    }

    // Debug-Logging der eingehenden Checkout-ID
    const checkoutId = paymentData.checkout_id || '';
    log.info(`savePaymentTransaction aufgerufen mit checkout_id: "${checkoutId}" für Transaktion ${paymentData.transaction_id || paymentData.id}`);

    // Durchsuche alle Felder nach einer möglichen checkout_id (der Name könnte variieren)
    let foundCheckoutId = checkoutId;
    for (const [key, value] of Object.entries(paymentData)) {
      if (key.toLowerCase().includes('checkout') && value) {
        log.info(`Mögliches checkout_id Feld gefunden: ${key} = ${value}`);
        foundCheckoutId = value;
        break;
      }
    }

    // Bestimme den Zahlungstyp und speichere in der entsprechenden Tabelle
    const paymentMethod = (paymentData.payment_method || '').toUpperCase();

    // Betrag übernehmen, da dieser bereits im korrekten Format (in Cent) vorliegt
    const amount = paymentData.amount || 0;
    log.info(`Betrag für Zahlung: ${amount}`);

    if (paymentMethod === 'CASH' || paymentMethod === 'BAR') {
      // Barzahlung
      log.info(`Speichere Barzahlung mit checkout_id: "${foundCheckoutId}"`);
      return this.saveCashTransaction({
        transaction_id: paymentData.transaction_id || paymentData.id,
        amount: amount,
        status: paymentData.status,
        checkout_id: foundCheckoutId
      });
    } else if (paymentMethod === 'CARD' || paymentMethod === 'KARTE') {
      // Kartenzahlung
      log.info(`Speichere Kartenzahlung mit checkout_id: "${foundCheckoutId}"`);
      return this.saveCardTransaction({
        transaction_id: paymentData.transaction_id || paymentData.id,
        amount: amount,
        status: paymentData.status,
        checkout_id: foundCheckoutId,
        customer_receipt: paymentData.customer_receipt,
        merchant_receipt: paymentData.merchant_receipt,
        card_type: paymentData.card_type,
        card_number: paymentData.card_number,
        auth_code: paymentData.auth_code,
        receipt_number: paymentData.receipt_number,
        trace_number: paymentData.trace_number,
        terminal_id: paymentData.terminal_id
      });
    } else {
      // Unbekannte Zahlungsmethode, als Barzahlung behandeln
      log.warn(`Unbekannte Zahlungsmethode: ${paymentMethod}, wird als Barzahlung behandelt mit checkout_id: "${foundCheckoutId}"`);
      return this.saveCashTransaction({
        transaction_id: paymentData.transaction_id || paymentData.id,
        amount: amount,
        status: paymentData.status,
        checkout_id: foundCheckoutId
      });
    }
  }

  /**
   * Überprüft, ob eine Transaktion ein payment_transaction_id Format hat
   * und gibt ggf. die Basis-ID zurück.
   * @param {string} transactionId Die zu prüfende Transaktions-ID
   * @returns {Object} { isPaymentFormat: boolean, baseId: string, originalId: string }
   */
   _analyzeTransactionId(transactionId) {
    const isPaymentFormat = transactionId && transactionId.includes('transaction_payment_');
    const baseId = isPaymentFormat ?
      transactionId.replace('transaction_payment_', 'transaction_') : transactionId;

    return {
      isPaymentFormat,
      baseId,
      originalId: transactionId
    };
  }

  /**
   * Aktualisiert den Status einer Zahlungstransaktion
   * @param {string} transactionId Die Transaktions-ID
   * @param {Object} updateData Die zu aktualisierenden Daten
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async updatePaymentTransaction(transactionId, updateData) {
    if (!this.initialized) {
      await this.initialize();
    }

    // Debug-Logging der eingehenden Checkout-ID
    const checkoutId = updateData.checkout_id || '';
    log.info(`updatePaymentTransaction aufgerufen mit checkout_id: "${checkoutId}" für Transaktion ${transactionId}`);

    // Analyse der Transaktions-ID
    const { isPaymentFormat, baseId, originalId } = this._analyzeTransactionId(transactionId);
    log.info(`Transaktionsformat: ${isPaymentFormat ? 'Payment-Format' : 'Standard-Format'}, Basis-ID: ${baseId}`);

    // Wenn die checkout_id im Update enthalten ist und nicht leer ist,
    // wird sie für die Aktualisierung aller Tabellen verwendet
    if (checkoutId) {
      try {
        // Aktualisiere die cash_transactions Tabelle
        const cashUpdateSql = `UPDATE cash_transactions
                             SET checkout_id = ?
                             WHERE transaction_id IN (?, ?) AND (checkout_id IS NULL OR checkout_id = '')`;

        await new Promise((resolve) => {
          this.db.run(cashUpdateSql, [checkoutId, originalId, baseId], function(err) {
            if (err) {
              log.warn(`Fehler beim Aktualisieren der checkout_id in cash_transactions: ${err.message}`);
            } else if (this.changes > 0) {
              log.info(`checkout_id für Barzahlung ${originalId}/${baseId} aktualisiert auf "${checkoutId}"`);
            }
            resolve();
          });
        });

        // Aktualisiere die card_transactions Tabelle
        const cardUpdateSql = `UPDATE card_transactions
                             SET checkout_id = ?
                             WHERE transaction_id IN (?, ?) AND (checkout_id IS NULL OR checkout_id = '')`;

        await new Promise((resolve) => {
          this.db.run(cardUpdateSql, [checkoutId, originalId, baseId], function(err) {
            if (err) {
              log.warn(`Fehler beim Aktualisieren der checkout_id in card_transactions: ${err.message}`);
            } else if (this.changes > 0) {
              log.info(`checkout_id für Kartenzahlung ${originalId}/${baseId} aktualisiert auf "${checkoutId}"`);
            }
            resolve();
          });
        });

        // Aktualisiere die transaction_references Tabelle
        const refUpdateSql = `UPDATE transaction_references
                            SET checkout_id = ?
                            WHERE payment_transaction_id IN (?, ?) AND (checkout_id IS NULL OR checkout_id = '')`;

        await new Promise((resolve) => {
          this.db.run(refUpdateSql, [checkoutId, originalId, baseId], function(err) {
            if (err) {
              log.warn(`Fehler beim Aktualisieren der checkout_id in transaction_references: ${err.message}`);
            } else if (this.changes > 0) {
              log.info(`checkout_id für Transaktionsreferenz mit payment_transaction_id ${originalId}/${baseId} aktualisiert auf "${checkoutId}"`);
            }
            resolve();
          });
        });
      } catch (error) {
        log.warn(`Fehler bei der Aktualisierung der checkout_id: ${error.message}`);
      }
    }

    // Prüfe, ob es sich um eine Kartenzahlung handelt (prüfe beide IDs)
    const cardTransaction = await this.checkCardTransaction(transactionId);
    if (cardTransaction.exists) {
      return this.updateCardTransaction(transactionId, updateData);
    }

    // Wenn mit originaler ID nicht gefunden, versuche mit Basis-ID
    if (isPaymentFormat) {
      const baseCardTransaction = await this.checkCardTransaction(baseId);
      if (baseCardTransaction.exists) {
        return this.updateCardTransaction(baseId, updateData);
      }
    }

    // Prüfe, ob es sich um eine Barzahlung handelt (prüfe beide IDs)
    const cashTransaction = await this.checkCashTransaction(transactionId);
    if (cashTransaction.exists) {
      // Für Barzahlungen nur den Status aktualisieren
      if (updateData.status) {
        return new Promise((resolve, reject) => {
          const sql = `UPDATE cash_transactions SET status = ? WHERE transaction_id = ?`;
          this.db.run(sql, [updateData.status, transactionId], function(err) {
            if (err) {
              log.error(`Fehler beim Aktualisieren der Barzahlung ${transactionId}: ${err.message}`);
              reject(err);
              return;
            }
            log.info(`Barzahlung ${transactionId} aktualisiert, ${this.changes} Zeilen betroffen`);
            resolve(this.changes > 0);
          });
        });
      }
      return true; // Barzahlungen sind immer abgeschlossen
    }

    // Wenn mit originaler ID nicht gefunden, versuche mit Basis-ID
    if (isPaymentFormat) {
      const baseCashTransaction = await this.checkCashTransaction(baseId);
      if (baseCashTransaction.exists) {
        // Für Barzahlungen nur den Status aktualisieren
        if (updateData.status) {
          return new Promise((resolve, reject) => {
            const sql = `UPDATE cash_transactions SET status = ? WHERE transaction_id = ?`;
            this.db.run(sql, [updateData.status, baseId], function(err) {
              if (err) {
                log.error(`Fehler beim Aktualisieren der Barzahlung ${baseId}: ${err.message}`);
                reject(err);
                return;
              }
              log.info(`Barzahlung ${baseId} aktualisiert, ${this.changes} Zeilen betroffen`);
              resolve(this.changes > 0);
            });
          });
        }
        return true; // Barzahlungen sind immer abgeschlossen
      }
    }

    // Keine Transaktion gefunden
    log.warn(`Keine Transaktion zum Aktualisieren gefunden für ${transactionId} oder ${baseId}`);
    return false;
  }

  /**
   * Markiert eine Zahlungstransaktion als abgeschlossen
   * @param {string} transactionId Die Transaktions-ID
   * @param {Object} completionData Die Abschlussdaten
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async completePaymentTransaction(transactionId, completionData) {
    if (!this.initialized) {
      await this.initialize();
    }

    // Stelle sicher, dass alle Daten korrekt formatiert sind
    const updateData = {
      status: 'COMPLETED',
      completed_at: Math.floor(Date.now() / 1000),
      ...completionData
    };

    // Für Kartenzahlungen
    const cardTransaction = await this.checkCardTransaction(transactionId);
    if (cardTransaction.exists) {
      return this.completeCardTransaction(transactionId, updateData);
    }

    // Für Barzahlungen (bereits als COMPLETED markiert)
    const cashTransaction = await this.checkCashTransaction(transactionId);
    if (cashTransaction.exists) {
      return true; // Barzahlungen sind immer abgeschlossen
    }

    return false;
  }

  /**
   * Findet alle ausstehenden Zahlungen für eine Transaktion
   * @param {string} parentTransactionId Die übergeordnete Transaktions-ID
   * @returns {Promise<Array>} Liste der ausstehenden Zahlungstransaktionen
   */
  async findPendingPayments(parentTransactionId) {
    if (!this.initialized) {
      await this.initialize();
    }

    // Suche nach ausstehenden Kartenzahlungen
    return this.findPendingCardPayments(parentTransactionId);
  }

  /**
   * Prüft, ob eine Zahlungstransaktion existiert und abgeschlossen ist
   * @param {string} transactionId Die Transaktions-ID
   * @returns {Promise<Object>} Status-Informationen { exists: boolean, completed: boolean, data?: Object }
   */
  async checkPaymentTransaction(transactionId) {
    if (!this.initialized) {
      await this.initialize();
    }

    // Analyse der Transaktions-ID
    const { isPaymentFormat, baseId, originalId } = this._analyzeTransactionId(transactionId);
    log.info(`checkPaymentTransaction für ${originalId}, Format: ${isPaymentFormat ? 'Payment' : 'Standard'}, Basis-ID: ${baseId}`);

    // Prüfe zuerst in der Kartenzahlungstabelle
    const cardTransaction = await this.checkCardTransaction(originalId);
    if (cardTransaction.exists) {
      log.info(`Kartenzahlung gefunden für ${originalId}`);
      return cardTransaction;
    }

    // Wenn nicht gefunden und es ist eine payment_transaction_id, versuche mit der Basis-ID
    if (isPaymentFormat) {
      const baseCardTransaction = await this.checkCardTransaction(baseId);
      if (baseCardTransaction.exists) {
        log.info(`Kartenzahlung gefunden für Basis-ID ${baseId}`);
        return baseCardTransaction;
      }
    }

    // Prüfe in der Barzahlungstabelle
    const cashTransaction = await this.checkCashTransaction(originalId);
    if (cashTransaction.exists) {
      // Barzahlungen sind nur COMPLETED wenn der Status COMPLETED ist
      // Wenn REPLACED_BY_CARD, dann ist sie nicht mehr gültig
      const isCompleted = cashTransaction.data.status === 'COMPLETED';
      log.info(`Barzahlung gefunden für ${originalId}, Status: ${cashTransaction.data.status}`);
      return {
        exists: true,
        completed: isCompleted,
        data: cashTransaction.data
      };
    }

    // Wenn nicht gefunden und es ist eine payment_transaction_id, versuche mit der Basis-ID
    if (isPaymentFormat) {
      const baseCashTransaction = await this.checkCashTransaction(baseId);
      if (baseCashTransaction.exists) {
        // Barzahlungen sind nur COMPLETED wenn der Status COMPLETED ist
        const isCompleted = baseCashTransaction.data.status === 'COMPLETED';
        log.info(`Barzahlung gefunden für Basis-ID ${baseId}, Status: ${baseCashTransaction.data.status}`);
        return {
          exists: true,
          completed: isCompleted,
          data: baseCashTransaction.data
        };
      }
    }

    // Keine Transaktion gefunden
    log.info(`Keine Zahlungstransaktion gefunden für ${originalId} oder ${baseId}`);
    return { exists: false, completed: false };
  }

  /**
   * Findet eine Kartenzahlung anhand der Transaktions-ID oder Checkout-ID
   * @param {string} transactionId Die Transaktions-ID (kann TSE-Transaktions-ID oder Zahlungs-ID sein)
   * @param {string} checkoutId Optionale Checkout-ID
   * @returns {Promise<Object|null>} Die Kartenzahlungsdaten oder null
   */
  async findCardPaymentByTransactionId(transactionId, checkoutId = null) {
    if (!this.initialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const conditions = [];
      const params = [];

      if (transactionId) {
        const { isPaymentFormat, baseId, originalId } = this._analyzeTransactionId(transactionId);
        const idPlaceholders = [];
        if (originalId) {
          idPlaceholders.push('?');
          params.push(originalId);
        }
        if (isPaymentFormat && baseId !== originalId) {
          idPlaceholders.push('?');
          params.push(baseId);
        }
        if (idPlaceholders.length > 0) {
          conditions.push(`transaction_id IN (${idPlaceholders.join(',')})`);
        }
      }

      if (checkoutId) {
        conditions.push(`checkout_id = ?`);
        params.push(checkoutId);
      }

      if (conditions.length === 0) {
        log.warn('findCardPaymentByTransactionId aufgerufen ohne gültige Suchkriterien.');
        resolve(null);
        return;
      }

      const sql = `SELECT * FROM card_transactions WHERE ${conditions.join(' OR ')} ORDER BY created_at DESC LIMIT 1`;

      this.db.get(sql, params, (err, row) => {
        if (err) {
          log.error(`Fehler bei der Suche nach Kartenzahlung (transactionId: ${transactionId}, checkoutId: ${checkoutId}):`, err.message);
          reject(err);
          return;
        }
        if (row) {
          log.info(`Kartenzahlung gefunden für (transactionId: ${transactionId}, checkoutId: ${checkoutId}): ${JSON.stringify(row)}`);
        } else {
          log.info(`Keine Kartenzahlung gefunden für (transactionId: ${transactionId}, checkoutId: ${checkoutId}) mit SQL: ${sql} und Params: ${JSON.stringify(params)}`);
        }
        resolve(row || null);
      });
    });
  }

  /**
   * Findet eine Barzahlung anhand der Transaktions-ID
   * @param {string} transactionId Die Transaktions-ID
   * @returns {Promise<Object|null>} Die Barzahlungsdaten oder null
   */
  async findCashPaymentByTransactionId(transactionId, checkoutId = null) {
    if (!this.initialized) {
      await this.initialize();
    }
    return new Promise((resolve, reject) => {
      const conditions = [];
      const params = [];

      if (transactionId) {
        const { isPaymentFormat, baseId, originalId } = this._analyzeTransactionId(transactionId);
        const idPlaceholders = [];
        if (originalId) {
          idPlaceholders.push('?');
          params.push(originalId);
        }
        if (isPaymentFormat && baseId !== originalId) {
          idPlaceholders.push('?');
          params.push(baseId);
        }
        if (idPlaceholders.length > 0) {
          conditions.push(`transaction_id IN (${idPlaceholders.join(',')})`);
        }
      }

      if (checkoutId) {
        conditions.push(`checkout_id = ?`);
        params.push(checkoutId);
      }

      if (conditions.length === 0) {
        log.warn('findCashPaymentByTransactionId aufgerufen ohne gültige Suchkriterien.');
        resolve(null);
        return;
      }
      const sql = `SELECT * FROM cash_transactions WHERE ${conditions.join(' OR ')} ORDER BY created_at DESC LIMIT 1`;
      this.db.get(sql, params, (err, row) => {
        if (err) {
          log.error(`Fehler bei der Suche nach Barzahlung (transactionId: ${transactionId}, checkoutId: ${checkoutId}):`, err.message);
          reject(err);
          return;
        }
        resolve(row || null);
      });
    });
  }

  /**
   * Ruft die zusammengeführten Transaktionsdaten aus der View ab
   * @param {number} page Die Seitennummer (beginnend bei 1)
   * @param {number} limit Die Anzahl der Einträge pro Seite
   * @param {Object} filter Optionale Filterkriterien
   * @returns {Promise<Array>} Die Transaktionsdaten
   */
  async getTransactionsView(page = 1, limit = 100, filter = {}) {
    if (!this.initialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const offset = (page - 1) * limit;

      // Basisabfrage
      let sql = `SELECT * FROM transactions_view`;
      const params = [];

      // Filter anwenden, wenn vorhanden
      const whereConditions = [];

      if (filter.checkout_id) {
        whereConditions.push('checkout_id = ?');
        params.push(filter.checkout_id);
      }

      if (filter.payment_method) {
        whereConditions.push('effective_payment_method = ?');
        params.push(filter.payment_method);
      }

      if (filter.initial_payment_method) {
        whereConditions.push('initial_payment_method = ?');
        params.push(filter.initial_payment_method);
      }

      if (filter.tse_state) {
        whereConditions.push('tse_state = ?');
        params.push(filter.tse_state);
      }

      // Filter für den Gesamtzahlungsstatus
      if (filter.filter_by_overall_payment_status) {
        const status = filter.filter_by_overall_payment_status.toUpperCase();

        // Komplexe Bedingung für den Gesamtzahlungsstatus
        if (['COMPLETED', 'FINISHED'].includes(status)) {
          // Abgeschlossene Zahlungen: Entweder Kartenzahlung oder Barzahlung ist abgeschlossen
          whereConditions.push('((card_status = "COMPLETED" OR card_status = "FINISHED") OR (cash_status = "COMPLETED" AND cash_status != "REPLACED_BY_CARD"))');
        } else if (['PENDING', 'CREATED'].includes(status)) {
          // Ausstehende Zahlungen: Mindestens eine Zahlung ist ausstehend und keine ist abgeschlossen
          whereConditions.push('((card_status = "PENDING" OR card_status = "CREATED") OR (cash_status = "PENDING" OR cash_status = "CREATED")) AND NOT ((card_status = "COMPLETED" OR card_status = "FINISHED") OR (cash_status = "COMPLETED" AND cash_status != "REPLACED_BY_CARD"))');
        } else if (['ABORTED', 'CANCELED', 'FAILED', 'ERROR'].includes(status)) {
          // Fehlgeschlagene Zahlungen: Mindestens eine Zahlung ist fehlgeschlagen und keine ist abgeschlossen
          whereConditions.push('(card_status IN ("ABORTED", "CANCELED", "FAILED", "ERROR") OR cash_status IN ("ABORTED", "CANCELED", "FAILED", "ERROR")) AND NOT ((card_status = "COMPLETED" OR card_status = "FINISHED") OR (cash_status = "COMPLETED" AND cash_status != "REPLACED_BY_CARD"))');
        }
      }

      if (filter.date_from) {
        // Stellt sicher, dass es ein numerischer Timestamp ist
        const timestampFrom = Number(filter.date_from);
        if (!isNaN(timestampFrom)) {
          whereConditions.push('tse_created_at >= ?'); // Oder die korrekte Datumsspalte Ihrer View
          params.push(timestampFrom);
        }
      }

      if (filter.date_to) {
        // Stellt sicher, dass es ein numerischer Timestamp ist
        const timestampTo = Number(filter.date_to);
        if (!isNaN(timestampTo)) {
          whereConditions.push('tse_created_at <= ?'); // Oder die korrekte Datumsspalte Ihrer View
          params.push(timestampTo);
        }
      }

      // WHERE-Klausel hinzufügen, wenn Filter vorhanden sind
      if (whereConditions.length > 0) {
        sql += ` WHERE ${whereConditions.join(' AND ')}`;
      }

      // Sortierung und Paginierung
      sql += ` ORDER BY tse_created_at DESC LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      this.db.all(sql, params, (err, rows) => {
        if (err) {
          log.error('Fehler beim Abrufen der Transaktionsübersicht:', err.message);
          reject(err);
          return;
        }

        resolve(rows || []);
      });
    });
  }

  /**
   * Zählt die Gesamtanzahl der Transaktionen in der View (für Paginierung)
   * @param {Object} filter Optionale Filterkriterien
   * @returns {Promise<number>} Die Anzahl der Transaktionen
   */
  async countTransactionsView(filter = {}) {
    if (!this.initialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      // Basisabfrage
      let sql = `SELECT COUNT(*) as count FROM transactions_view`;
      const params = [];

      // Filter anwenden, wenn vorhanden
      const whereConditions = [];

      if (filter.checkout_id) {
        whereConditions.push('checkout_id = ?');
        params.push(filter.checkout_id);
      }

      if (filter.payment_method) {
        whereConditions.push('effective_payment_method = ?');
        params.push(filter.payment_method);
      }

      if (filter.initial_payment_method) {
        whereConditions.push('initial_payment_method = ?');
        params.push(filter.initial_payment_method);
      }

      if (filter.tse_state) {
        whereConditions.push('tse_state = ?');
        params.push(filter.tse_state);
      }

      // Filter für den Gesamtzahlungsstatus
      if (filter.filter_by_overall_payment_status) {
        const status = filter.filter_by_overall_payment_status.toUpperCase();

        // Komplexe Bedingung für den Gesamtzahlungsstatus
        if (['COMPLETED', 'FINISHED'].includes(status)) {
          // Abgeschlossene Zahlungen: Entweder Kartenzahlung oder Barzahlung ist abgeschlossen
          whereConditions.push('((card_status = "COMPLETED" OR card_status = "FINISHED") OR (cash_status = "COMPLETED" AND cash_status != "REPLACED_BY_CARD"))');
        } else if (['PENDING', 'CREATED'].includes(status)) {
          // Ausstehende Zahlungen: Mindestens eine Zahlung ist ausstehend und keine ist abgeschlossen
          whereConditions.push('((card_status = "PENDING" OR card_status = "CREATED") OR (cash_status = "PENDING" OR cash_status = "CREATED")) AND NOT ((card_status = "COMPLETED" OR card_status = "FINISHED") OR (cash_status = "COMPLETED" AND cash_status != "REPLACED_BY_CARD"))');
        } else if (['ABORTED', 'CANCELED', 'FAILED', 'ERROR'].includes(status)) {
          // Fehlgeschlagene Zahlungen: Mindestens eine Zahlung ist fehlgeschlagen und keine ist abgeschlossen
          whereConditions.push('(card_status IN ("ABORTED", "CANCELED", "FAILED", "ERROR") OR cash_status IN ("ABORTED", "CANCELED", "FAILED", "ERROR")) AND NOT ((card_status = "COMPLETED" OR card_status = "FINISHED") OR (cash_status = "COMPLETED" AND cash_status != "REPLACED_BY_CARD"))');
        }
      }

      if (filter.date_from) {
        const timestampFrom = Number(filter.date_from);
        if (!isNaN(timestampFrom)) {
          whereConditions.push('tse_created_at >= ?');
          params.push(timestampFrom);
        }
      }

      if (filter.date_to) {
        const timestampTo = Number(filter.date_to);
        if (!isNaN(timestampTo)) {
          whereConditions.push('tse_created_at <= ?');
          params.push(timestampTo);
        }
      }

      // WHERE-Klausel hinzufügen, wenn Filter vorhanden sind
      if (whereConditions.length > 0) {
        sql += ` WHERE ${whereConditions.join(' AND ')}`;
      }

      this.db.get(sql, params, (err, row) => {
        if (err) {
          log.error('Fehler beim Zählen der Transaktionen:', err.message);
          reject(err);
          return;
        }

        resolve(row ? row.count : 0);
      });
    });
  }

  /**
   * Schließt die Datenbankverbindung
   */
  close() {
    if (this.db) {
      this.db.close((err) => {
        if (err) {
          log.error('Fehler beim Schließen der Datenbank:', err.message);
        } else {
          log.info('TSE-Revisionsdatenbank erfolgreich geschlossen');
        }
      });
      this.db = null;
      this.initialized = false;
    }
  }
}

module.exports = TseDatabase;