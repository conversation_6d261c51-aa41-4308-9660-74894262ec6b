const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Sichere Schnittstelle für die Kommunikation mit dem Hauptprozess
contextBridge.exposeInMainWorld('api', {
  // Empfangen von Zahlungsdaten
  onPaymentData: (callback) => {
    ipcRenderer.on('payment-data', (event, data) => callback(data));
  },
  
  // Empfangen von Zahlungs-Updates
  onPaymentUpdate: (callback) => {
    ipcRenderer.on('payment-update', (event, data) => callback(data));
  },
 
  // Funktion für explizite Zahlungsergebnisse mit Fehlerbehandlung
  onPaymentResult: (callback) => {
    ipcRenderer.on('zvt-payment-result', (event, data) => {
      // Sicherstellen, dass bei Fehlern die Fehlermeldung verfügbar ist
      if (!data.success && !data.error && data.statusCode) {
        // Fallback-Fehlermeldungen für bekannte Statuscodes
        const errorMessages = {
          '0C': 'Transaktion wurde abgebrochen',
          'Z3': 'Zeitüberschreitung',
          '05': 'Karte abgelehnt',
          '1A': 'Terminal nicht bereit'
        };
        
        // Fehlermeldung aus Mapping oder generisch
        data.error = errorMessages[data.statusCode] || 
                     `Fehler bei der Zahlung (Code: ${data.statusCode})`;
      }
      
      callback(data);
    });
  },

  // Modal schließen und Zahlung abbrechen
  closeModal: () => {
    ipcRenderer.send('close-payment-modal', { abort: true });
  },
  
  // Modal nur schließen ohne Abbruch
  closeModalWithoutAbort: () => {
    ipcRenderer.send('close-payment-modal', { abort: false });
  },
  
  // Funktionen für den Belegdruck
  printCustomerReceipt: () => {
    ipcRenderer.send('print-customer-receipt');
  },
  
  printMerchantReceipt: () => {
    ipcRenderer.send('print-merchant-receipt');
  }
});