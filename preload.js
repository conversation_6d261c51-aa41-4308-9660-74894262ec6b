const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');
const path = require('path');

// Sichere Kommunikation zwischen Renderer und Main Prozess
contextBridge.exposeInMainWorld('electronAPI', {
  // Funktion zum Umschalten der Menüleiste
  toggleMenuBar: () => {
    console.log('toggleMenuBar in preload.js aufgerufen');
    ipcRenderer.send('toggle-menu-bar');
  }
});

// Sichere Kommunikation zwischen Renderer und Main Prozess
contextBridge.exposeInMainWorld('api', {
  // API zum Abrufen der POS-URL
  getPosUrl: async () => {
    return await ipcRenderer.invoke('get-pos-url');
  },

  // API zum Abrufen der gesamten API-Konfiguration
  getApiConfig: async () => {
    return await ipcRenderer.invoke('get-api-config');
  },

  // Weitere Methoden für die Kommunikation mit dem Shop/Kassensystem
  sendPaymentData: (data) => {
    ipcRenderer.send('payment-data', data);
  },

  onReceiptPrint: (callback) => {
    ipcRenderer.on('print-receipt', (event, data) => callback(data));
  },

  // ZVT-Kartenzahlungsfunktionen
  startCardPayment: async (amount) => {
    try {
      return await ipcRenderer.invoke('start-card-payment', amount);
    } catch (error) {
      console.error('Fehler bei Kartenzahlung:', error);
      return { success: false, error: error.toString() };
    }
  },

  checkTerminalStatus: async () => {
    try {
      return await ipcRenderer.invoke('check-terminal-status');
    } catch (error) {
      console.error('Fehler beim Terminal-Status-Check:', error);
      return { connected: false, error: error.toString() };
    }
  },

  performDayEnd: async () => {
    try {
      return await ipcRenderer.invoke('perform-day-end');
    } catch (error) {
      console.error('Fehler beim Tagesabschluss:', error);
      return { success: false, error: error.toString() };
    }
  },

  // Direkte Funktionen für Belegdruck
  printCustomerReceipt: () => {
    ipcRenderer.send('print-customer-receipt');
  },

  printMerchantReceipt: () => {
    ipcRenderer.send('print-merchant-receipt');
  },

  // Abbruchfunktion für Zahlungen
  abortPayment: () => {
    return ipcRenderer.invoke('abort-payment');
  },

  // Event-Listener für ZVT-Zahlungsstatus
  onZVTPaymentStarted: (callback) => {
    // Event-Listener bereinigen, falls bereits registriert
    ipcRenderer.removeAllListeners('zvt-payment-started');
    ipcRenderer.on('zvt-payment-started', (event, data) => callback(data));
  },

  onZVTPaymentUpdate: (callback) => {
    // Event-Listener bereinigen, falls bereits registriert
    ipcRenderer.removeAllListeners('zvt-payment-update');
    ipcRenderer.on('zvt-payment-update', (event, data) => callback(data));
  },

  onZVTPaymentResult: (callback) => {
    // Event-Listener bereinigen, falls bereits registriert
    ipcRenderer.removeAllListeners('zvt-payment-result');
    ipcRenderer.on('zvt-payment-result', (event, data) => callback(data));
  },

  onConfigReloaded: (callback) => {
    ipcRenderer.removeAllListeners('config-reloaded');
    ipcRenderer.on('config-reloaded', (event, data) => callback(data));
  },

  // Modal-Steuerung direkt aus dem Hauptfenster
  openPaymentModal: (data) => {
    ipcRenderer.send('open-payment-modal', data);
  },

  closePaymentModal: (options = {}) => {
    ipcRenderer.send('close-payment-modal', options);
  }
});

contextBridge.exposeInMainWorld('epson', {
  // Methode zum Laden und Initialisieren des ePOS SDK
  initializeSDK: () => {
    return new Promise((resolve, reject) => {
      try {
        // Prüfen, ob SDK bereits geladen ist
        if (window.epson && window.epson.ePOSDevice) {
          resolve(true);
          return;
        }

        // Script-Element erstellen und SDK laden
        const script = document.createElement('script');
        script.src = path.join(__dirname, 'resources', 'epos-sdk', 'epos-2.27.0.js');
        script.onload = () => resolve(true);
        script.onerror = (err) => reject(new Error('ePOS SDK konnte nicht geladen werden: ' + err));
        document.head.appendChild(script);
      } catch (error) {
        reject(error);
      }
    });
  },

  // Methode zum Verbinden mit dem Drucker
  connectToPrinter: (ip, port) => {
    return new Promise((resolve, reject) => {
      try {
        if (!window.epson || !window.epson.ePOSDevice) {
          reject(new Error('ePOS SDK nicht initialisiert. Bitte erst initializeSDK() aufrufen.'));
          return;
        }

        const ePosDev = new window.epson.ePOSDevice();
        ePosDev.connect(ip, port, (connectResult) => {
          if (connectResult === 'OK') {
            ePosDev.createDevice(
              'local_printer',
              ePosDev.DEVICE_TYPE_PRINTER,
              { crypto: true, buffer: false },
              (devobj, createResult) => {
                if (createResult === 'OK') {
                  resolve({ device: devobj, connection: ePosDev });
                } else {
                  reject(new Error('Drucker-Gerät konnte nicht erstellt werden: ' + createResult));
                }
              }
            );
          } else {
            reject(new Error('Verbindung zum Drucker fehlgeschlagen: ' + connectResult));
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  // Methode zum Drucken von XML-Daten
  printXML: (device, xmlData) => {
    return new Promise((resolve, reject) => {
      try {
        if (!device) {
          reject(new Error('Kein Drucker-Gerät verfügbar'));
          return;
        }

        // XML-Daten direkt an den Drucker senden
        device.addXML(xmlData);

        // Sende Druckauftrag
        device.send({
          onReceive: (response) => {
            if (response.success) {
              resolve({ success: true });
            } else {
              reject(new Error('Drucken fehlgeschlagen: ' + response.code));
            }
          },
          onError: (error) => {
            reject(new Error('Fehler beim Drucken: ' + error));
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  // Methode zum Drucken eines Testtextes
  printTestReceipt: (device) => {
    return new Promise((resolve, reject) => {
      try {
        if (!device) {
          reject(new Error('Kein Drucker-Gerät verfügbar'));
          return;
        }

        // Testnachricht erstellen
        device.addTextAlign(device.ALIGN_CENTER);
        device.addText('--- Testausdruck ---\n');
        device.addFeedLine(1);
        device.addText('Druckertest erfolgreich');
        device.addFeedLine(3);
        device.addCut(device.CUT_FEED);

        // Sende Druckauftrag
        device.send({
          onReceive: (response) => {
            if (response.success) {
              resolve({ success: true });
            } else {
              reject(new Error('Testdruck fehlgeschlagen: ' + response.code));
            }
          },
          onError: (error) => {
            reject(new Error('Fehler beim Testdruck: ' + error));
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }
});

// IPC-Kommunikation für Drucker-Operationen bleibt unverändert
contextBridge.exposeInMainWorld('printer', {
  // Methode zum Drucken über den Main-Prozess
  printViaMainProcess: async (printerOptions, xmlData) => {
    return await ipcRenderer.invoke('print-to-epson', { printerOptions, xmlData });
  },

  // Status des Druckauftrags
  onPrintResult: (callback) => {
    ipcRenderer.removeAllListeners('print-result');
    ipcRenderer.on('print-result', (event, data) => callback(data));
  },

  // Fehler bei Druckaufträgen
  onPrintError: (callback) => {
    ipcRenderer.removeAllListeners('print-error');
    ipcRenderer.on('print-error', (event, data) => callback(data));
  },

  // Erfolgreicher Druck
  onPrintCompleted: (callback) => {
    ipcRenderer.removeAllListeners('print-completed');
    ipcRenderer.on('print-completed', (event, data) => callback(data));
  }
});