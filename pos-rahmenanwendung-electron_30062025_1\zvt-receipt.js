/**
 * ZVT-Beleg-Verarbeitung und Druckfunktionen
 */
const loggerService = require("./logger-service");
const zvtUtils = require("./zvt-utils");

class ZVTReceipt {
  constructor(client, config) {
    this.client = client;
    this.config = config;
    this.log = loggerService.getModuleLogger("ZVTReceipt");
    this.epsonPrinter = null;
    this.mainWindow = null;

    // Detaillierte Logging der übergebenen Konfiguration
    this.log.info("ZVTReceipt Konstruktor aufgerufen mit Konfiguration:");

    if (config) {
      this.log.info("Konfiguration ist vorhanden");

      if (config.zvt_config) {
        this.log.info("ZVT-Konfiguration gefunden:");
        this.log.info(`- zvt_ip: ${config.zvt_config.zvt_ip}`);
        this.log.info(`- zvt_port: ${config.zvt_config.zvt_port}`);
        this.log.info(`- merchant_name: ${config.zvt_config.merchant_name}`);
        this.log.info(`- merchant_address: ${config.zvt_config.merchant_address}`);
        this.log.info(`- merchant_zip_city: ${config.zvt_config.merchant_zip_city}`);
        this.log.info(`- merchant_line1: ${config.zvt_config.merchant_line1}`);
        this.log.info(`- merchant_line2: ${config.zvt_config.merchant_line2}`);
      } else {
        this.log.warn("Keine ZVT-Konfiguration in der übergebenen Konfiguration gefunden");
      }
    } else {
      this.log.error("Keine Konfiguration übergeben");
    }

    // Speichern der letzten Belege für manuellen Druck
    this.lastReceipts = {
      customer: null,
      merchant: null,
    };

    // Buffer für aktuell empfangene Belege
    this.receiptBuffer = {
      customer: [],
      merchant: [],
    };
  }

  /**
   * Setzt den Epson-Drucker für die direkte Belegausgabe
   * @param {Object} printer - Epson-Drucker-Instanz
   */
  setEpsonPrinter(printer) {
    this.epsonPrinter = printer;
    this.log.info("Epson-Drucker für ZVT-Belege gesetzt");
  }

  /**
   * Speichert Belege zentral in allen relevanten Speicherorten
   * @param {Array} customerReceipt - Die Kundenbeleg-Daten
   * @param {Array} merchantReceipt - Die Händlerbeleg-Daten
   */
  _storeReceipts(customerReceipt, merchantReceipt) {
    if (customerReceipt && customerReceipt.length > 0) {
      this.lastReceipts.customer = [...customerReceipt];
      this.receiptBuffer.customer = [...customerReceipt];
      this.log.info(`Kundenbeleg mit ${customerReceipt.length} Zeilen gespeichert`);
    }

    if (merchantReceipt && merchantReceipt.length > 0) {
      this.lastReceipts.merchant = [...merchantReceipt];
      this.receiptBuffer.merchant = [...merchantReceipt];
      this.log.info(`Händlerbeleg mit ${merchantReceipt.length} Zeilen gespeichert`);
    }
    
    // Nach dem Speichern der Belege, sende sie über MQTT
    this._sendReceiptsViaMqtt(customerReceipt, merchantReceipt);
  }

  /**
   * Erstellt erweiterte Belege mit allen verfügbaren Informationen
   * @param {boolean} isErrorReceipt - Ob es sich um einen Fehlerbeleg handelt
   * @param {Object} statusInfo - Optional: Status-Informationen für Verifone-Terminals
   */
  _createEnhancedReceipts(isErrorReceipt = false, statusInfo = null) {
    this.log.info(isErrorReceipt ? "Erstelle erweiterten Fehlerbeleg" : "Erstelle erweiterten Erfolgsbeleg");

    // Suche nach Terminal-ID und TA-Nummer im TLV-Container
    let terminalId = null;
    let traceNumber = null;
    let receiptNumber = null;
    let vuNumber = null;

    // Prüfe, ob TLV-Daten im Belegtext vorhanden sind
    if (this.client.connection && this.client.connection.responseData) {
      const responseData = this.client.connection.responseData;

      // Suche nach Terminal-ID im TLV-Container (06 D3 FF)
      for (let i = 0; i < responseData.length - 10; i++) {
        if (responseData[i] === 0x06 && responseData[i+1] === 0xD3) {
          // TLV-Container gefunden, durchsuche den Inhalt
          const tlvData = this._bytesToAscii(responseData.slice(i+2));

          // Suche nach Terminal-ID im Text
          const terminalIdMatch = tlvData.match(/Terminal-ID\s*[:=]\s*\D*([0-9]{8})/i);
          if (terminalIdMatch && terminalIdMatch[1]) {
            terminalId = terminalIdMatch[1].trim();
            this.log.info(`Terminal-ID aus TLV-Container extrahiert: ${terminalId}`);
          }

          // Suche nach TA-Nr im Text
          const traceNumberMatch = tlvData.match(/TA-Nr\s*[:=]?\s*(\d+)/i);
          if (traceNumberMatch && traceNumberMatch[1]) {
            traceNumber = traceNumberMatch[1].trim();
            this.log.info(`TA-Nr aus TLV-Container extrahiert: ${traceNumber}`);
          }

          // Suche nach Beleg-Nr im Text
          const receiptNumberMatch = tlvData.match(/BNr\s*[:=]?\s*(\d+)/i);
          if (receiptNumberMatch && receiptNumberMatch[1]) {
            receiptNumber = receiptNumberMatch[1].trim();
            this.log.info(`Beleg-Nr aus TLV-Container extrahiert: ${receiptNumber}`);
          }

          // Suche nach VU-Nr im Text
          const vuNumberMatch = tlvData.match(/VU-Nr\s*[:=]?\s*\D*([a-zA-Z0-9]{8})/i);
          if (vuNumberMatch && vuNumberMatch[1]) {
            vuNumber = vuNumberMatch[1].trim();
            this.log.info(`VU-Nr aus TLV-Container extrahiert: ${vuNumber}`);
          }

          break;
        }
      }
    }

    // Verwende die extrahierten Daten oder Fallback auf die gespeicherten Werte
    terminalId = terminalId || this.client.lastTerminalId;
    traceNumber = traceNumber || this.client.lastTraceNumber;
    receiptNumber = receiptNumber || this.client.lastReceiptNumber;
    vuNumber = vuNumber || this.client.lastVUNumber;

    let cardType = this.client.lastCardType;
    let cardNumber = this.client.lastCardNumber;
    let authCode = this.client.lastAuthCode;
    let amount = this.client.lastAmount;

    // Überschreibe mit Informationen aus statusInfo, falls verfügbar
    if (statusInfo) {
      if (statusInfo.terminalId) terminalId = statusInfo.terminalId;
      if (statusInfo.amount) amount = statusInfo.amount * 100; // Umrechnen in Cent
      if (statusInfo.cardType) cardType = statusInfo.cardType;
    }

    this.log.info(`Terminal-Daten vor Belegdruck: TerminalID=${terminalId || "nicht extrahiert"}, TraceNr=${traceNumber || "nicht extrahiert"}, CardType=${cardType || "nicht extrahiert"}, Betrag=${amount || "nicht extrahiert"}`);

    // Händlerdaten aus der Konfiguration
    let merchantName = "Kartenzahlung";
    let merchantAddress = "";
    let merchantZipCity = "";
    let merchantLine1 = "";
    let merchantLine2 = "";

    // Prüfe, ob die Konfiguration vorhanden ist
    if (this.config) {
      this.log.info("Konfiguration gefunden, prüfe auf Händlerdaten");

      // Prüfe, ob die Händlerdaten direkt in der Konfiguration vorhanden sind
      if (this.config.merchant_name) merchantName = this.config.merchant_name;
      if (this.config.merchant_address) merchantAddress = this.config.merchant_address;
      if (this.config.merchant_zip_city) merchantZipCity = this.config.merchant_zip_city;
      if (this.config.merchant_line1) merchantLine1 = this.config.merchant_line1;
      if (this.config.merchant_line2) merchantLine2 = this.config.merchant_line2;

      // Wenn die Händlerdaten in der ZVT-Konfiguration vorhanden sind, verwende diese
      if (this.config.zvt_config) {
        this.log.info("ZVT-Konfiguration gefunden, prüfe auf Händlerdaten");

        if (this.config.zvt_config.merchant_name) {
          merchantName = this.config.zvt_config.merchant_name;
          this.log.info(`Verwende Firmenname aus ZVT-Konfiguration: ${merchantName}`);
        }

        if (this.config.zvt_config.merchant_address) {
          merchantAddress = this.config.zvt_config.merchant_address;
          this.log.info(`Verwende Adresse aus ZVT-Konfiguration: ${merchantAddress}`);
        }

        if (this.config.zvt_config.merchant_zip_city) {
          merchantZipCity = this.config.zvt_config.merchant_zip_city;
          this.log.info(`Verwende PLZ/Ort aus ZVT-Konfiguration: ${merchantZipCity}`);
        }

        if (this.config.zvt_config.merchant_line1) {
          merchantLine1 = this.config.zvt_config.merchant_line1;
          this.log.info(`Verwende Zusatzzeile 1 aus ZVT-Konfiguration: ${merchantLine1}`);
        }

        if (this.config.zvt_config.merchant_line2) {
          merchantLine2 = this.config.zvt_config.merchant_line2;
          this.log.info(`Verwende Zusatzzeile 2 aus ZVT-Konfiguration: ${merchantLine2}`);
        }
      }
    } else {
      this.log.warn("Keine Konfiguration gefunden, verwende Standardwerte für Händlerdaten");
    }

    terminalId = terminalId || this.config.client_id || "K001";
    traceNumber = traceNumber || "1";
    cardNumber = cardNumber ? `xxxx xxxx xxxx ${cardNumber}` : "xxxx xxxx xxxx xxxx";
    cardType = cardType || "Karte";
    authCode = authCode || "k.A.";
    receiptNumber = receiptNumber || "k.A.";
    vuNumber = vuNumber || "k.A.";
    const emvData = this.client.lastEmvData || "none";

    // Betrag formatieren (mit Euro und Cent)
    const amountStr = amount ? `${(amount / 100).toFixed(2)} EUR` : "0,00 EUR";

    // Status je nach Art des Belegs
    const status = isErrorReceipt ? "Abgebrochen/Fehlgeschlagen" : "Erfolgreich";

    // Kundenbeleg erstellen
    const customerReceipt = [
      "===============================================",
      "            K U N D E N B E L E G",
      "===============================================",
      "",
      `${merchantName}`,
    ];

    // Händlerdaten hinzufügen, wenn vorhanden
    this.log.info(`Händlerdaten für Kundenbeleg: Name=${merchantName}, Adresse=${merchantAddress}, PLZ/Ort=${merchantZipCity}, Zeile1=${merchantLine1}, Zeile2=${merchantLine2}`);

    if (merchantAddress) {
      customerReceipt.push(`${merchantAddress}`);
      this.log.info(`Adresse zum Kundenbeleg hinzugefügt: ${merchantAddress}`);
    }

    if (merchantZipCity) {
      customerReceipt.push(`${merchantZipCity}`);
      this.log.info(`PLZ/Ort zum Kundenbeleg hinzugefügt: ${merchantZipCity}`);
    }

    if (merchantLine1) {
      customerReceipt.push(`${merchantLine1}`);
      this.log.info(`Zusatzzeile 1 zum Kundenbeleg hinzugefügt: ${merchantLine1}`);
    }

    if (merchantLine2) {
      customerReceipt.push(`${merchantLine2}`);
      this.log.info(`Zusatzzeile 2 zum Kundenbeleg hinzugefügt: ${merchantLine2}`);
    }

    // Leerzeile nach den Händlerdaten
    customerReceipt.push("");

    // Rest des Belegs hinzufügen
    customerReceipt.push(`Datum: ${new Date().toLocaleString()}`);
    customerReceipt.push(`Terminal-ID: ${terminalId}`);
    customerReceipt.push(`TA-Nr: ${traceNumber}`);
    customerReceipt.push(`Beleg-Nr: ${receiptNumber}`);
    customerReceipt.push(`Betrag: ${amountStr}`);
    customerReceipt.push(`Kartennummer: ${cardNumber}`);
    customerReceipt.push(`Kartentyp: ${cardType}`);
    customerReceipt.push(`Autorisierungscode: ${authCode}`);
    customerReceipt.push(`EMV-Daten: ${emvData}`);
    customerReceipt.push(`Transaktions-ID: ${this.client.lastTransactionId || "k.A."}`);
    customerReceipt.push(`Status: ${status}`);
    customerReceipt.push("");
    customerReceipt.push("");

    // Händlerbeleg erstellen (ohne Unterschriftsfeld bei Fehlern)
    const merchantReceipt = [
      "===============================================",
      "            H Ä N D L E R B E L E G",
      "===============================================",
      "",
      `${merchantName}`,
    ];

    // Händlerdaten hinzufügen, wenn vorhanden
    this.log.info(`Händlerdaten für Händlerbeleg: Name=${merchantName}, Adresse=${merchantAddress}, PLZ/Ort=${merchantZipCity}, Zeile1=${merchantLine1}, Zeile2=${merchantLine2}`);

    if (merchantAddress) {
      merchantReceipt.push(`${merchantAddress}`);
      this.log.info(`Adresse zum Händlerbeleg hinzugefügt: ${merchantAddress}`);
    }

    if (merchantZipCity) {
      merchantReceipt.push(`${merchantZipCity}`);
      this.log.info(`PLZ/Ort zum Händlerbeleg hinzugefügt: ${merchantZipCity}`);
    }

    if (merchantLine1) {
      merchantReceipt.push(`${merchantLine1}`);
      this.log.info(`Zusatzzeile 1 zum Händlerbeleg hinzugefügt: ${merchantLine1}`);
    }

    if (merchantLine2) {
      merchantReceipt.push(`${merchantLine2}`);
      this.log.info(`Zusatzzeile 2 zum Händlerbeleg hinzugefügt: ${merchantLine2}`);
    }

    // Leerzeile nach den Händlerdaten
    merchantReceipt.push("");

    // Rest des Belegs hinzufügen
    merchantReceipt.push(`Datum: ${new Date().toLocaleString()}`);
    merchantReceipt.push(`Terminal-ID: ${terminalId}`);
    merchantReceipt.push(`TA-Nr: ${traceNumber}`);
    merchantReceipt.push(`Beleg-Nr: ${receiptNumber}`);
    merchantReceipt.push(`Betrag: ${amountStr}`);
    merchantReceipt.push(`Kartennummer: ${cardNumber}`);
    merchantReceipt.push(`Kartentyp: ${cardType}`);
    merchantReceipt.push(`Autorisierungscode: ${authCode}`);
    merchantReceipt.push(`EMV-Daten: ${emvData}`);
    merchantReceipt.push(`Transaktions-ID: ${this.client.lastTransactionId || "k.A."}`);
    merchantReceipt.push(`Status: ${status}`);
    merchantReceipt.push("");

    // Unterschriftsfeld nur bei erfolgreichen Zahlungen hinzufügen
    if (!isErrorReceipt) {
      merchantReceipt.push("");
      merchantReceipt.push("");
      merchantReceipt.push("Unterschrift: ________________________________");
      merchantReceipt.push("");
    }

    merchantReceipt.push("");

    // Belege in beiden Speicherorten sichern
    this._storeReceipts(customerReceipt, merchantReceipt);
  }

  /**
   * Hilfsmethode zur Umwandlung von Bytes in lesbaren ASCII-Text
   * @param {Buffer} buffer Die zu konvertierenden Bytes
   * @returns {string} Der lesbare ASCII-Text
   */
  _bytesToAscii(buffer) {
    return zvtUtils.bytesToAscii(buffer);
  }

  /**
   * Sendet eine Fehlernachricht über MQTT für fehlgeschlagene oder abgebrochene Zahlungen
   * @param {string} transactionId - Die Transaktions-ID
   * @param {string} terminalId - Die Terminal-ID
   * @param {string} errorMessage - Die Fehlermeldung
   * @param {string} amount - Der Zahlungsbetrag
   */
  sendFailedPaymentMqttMessage(transactionId, terminalId, errorMessage, amount) {
    try {
      // Prüfe, ob der MQTT-Client global verfügbar ist
      if (!global.mqttClient) {
        this.log.warn('MQTT-Client ist nicht verfügbar, EC-Zahlung-Fehlermeldung kann nicht gesendet werden');
        return;
      }

      // Prüfe, ob die API-Konfiguration verfügbar ist
      if (!this.config || !this.config.mqtt_config || !this.config.mqtt_config.topics || !this.config.mqtt_config.topics.zvt_data) {
        this.log.warn('MQTT-Konfiguration fehlt, EC-Zahlung-Fehlermeldung kann nicht gesendet werden');
        return;
      }

      const topic = this.config.mqtt_config.topics.zvt_data;
      this.log.info(`Sende EC-Zahlung-Fehlermeldung an MQTT-Topic: ${topic}`);

      // Erstellen des vereinfachten JSON-Payloads für MQTT (neues Format)
      const mqttPayload = {
        data: {
          date: new Date().toLocaleString('de-DE'),
          terminalId: terminalId || 'unbekannt',
          traceNumber: '',
          receiptNumber: '',
          amount: amount ? `${amount} EUR` : '0.00 EUR',
          cardNumber: '',
          cardType: '',
          authCode: '',
          transactionId: transactionId || 'unbekannt',
          status: errorMessage || 'error'
        },
        raw_data: ''
      };

      // Senden des Payloads über MQTT
      global.mqttClient.publish(topic, mqttPayload)
        .then(() => {
          this.log.info(`EC-Zahlung-Fehlermeldung erfolgreich an Topic ${topic} gesendet`);
        })
        .catch(err => {
          this.log.error(`Fehler beim Senden der EC-Zahlung-Fehlermeldung an MQTT: ${err.message}`);
        });
    } catch (error) {
      this.log.error(`Fehler beim Versenden der EC-Zahlung-Fehlermeldung an MQTT: ${error.message}`);
    }
  }

  /**
   * Sendet die EC-Belege über MQTT an das konfigurierte Topic
   * @param {Array} customerReceipt - Die Kundenbeleg-Daten
   * @param {Array} merchantReceipt - Die Händlerbeleg-Daten
   */
  _sendReceiptsViaMqtt(customerReceipt, merchantReceipt) {
    try {
      // Prüfe, ob der MQTT-Client global verfügbar ist
      if (!global.mqttClient) {
        this.log.warn('MQTT-Client ist nicht verfügbar, EC-Beleg kann nicht gesendet werden');
        return;
      }

      // Prüfe, ob die API-Konfiguration verfügbar ist
      if (!this.config || !this.config.mqtt_config || !this.config.mqtt_config.topics || !this.config.mqtt_config.topics.zvt_data) {
        this.log.warn('MQTT-Konfiguration fehlt, EC-Beleg kann nicht gesendet werden');
        return;
      }

      const topic = this.config.mqtt_config.topics.zvt_data;
      this.log.info(`Sende EC-Beleg an MQTT-Topic: ${topic}`);

      // Extrahiere Informationen aus den Belegen
      const extractReceiptInfo = (receipt) => {
        if (!receipt || !Array.isArray(receipt) || receipt.length === 0) return null;
        
        const receiptData = {};
        receipt.forEach(line => {
          if (line.includes('Terminal-ID:')) {
            const match = line.match(/Terminal-ID:\s*(.+)/);
            if (match && match[1]) receiptData.terminalId = match[1].trim();
          } else if (line.includes('TA-Nr:')) {
            const match = line.match(/TA-Nr:\s*(.+)/);
            if (match && match[1]) receiptData.traceNumber = match[1].trim();
          } else if (line.includes('Beleg-Nr:')) {
            const match = line.match(/Beleg-Nr:\s*(.+)/);
            if (match && match[1]) receiptData.receiptNumber = match[1].trim();
          } else if (line.includes('Betrag:')) {
            const match = line.match(/Betrag:\s*(.+)/);
            if (match && match[1]) receiptData.amount = match[1].trim();
          } else if (line.includes('Kartennummer:')) {
            const match = line.match(/Kartennummer:\s*(.+)/);
            if (match && match[1]) receiptData.cardNumber = match[1].trim();
          } else if (line.includes('Kartentyp:')) {
            const match = line.match(/Kartentyp:\s*(.+)/);
            if (match && match[1]) receiptData.cardType = match[1].trim();
          } else if (line.includes('Autorisierungscode:')) {
            const match = line.match(/Autorisierungscode:\s*(.+)/);
            if (match && match[1]) receiptData.authCode = match[1].trim();
          } else if (line.includes('Transaktions-ID:')) {
            const match = line.match(/Transaktions-ID:\s*(.+)/);
            if (match && match[1]) receiptData.transactionId = match[1].trim();
          } else if (line.includes('Status:')) {
            const match = line.match(/Status:\s*(.+)/);
            if (match && match[1]) receiptData.status = match[1].trim();
          } else if (line.includes('Datum:')) {
            const match = line.match(/Datum:\s*(.+)/);
            if (match && match[1]) receiptData.date = match[1].trim();
          }
        });

        return receiptData;
      };

      const customerReceiptData = extractReceiptInfo(customerReceipt);
      const merchantReceiptData = extractReceiptInfo(merchantReceipt);

      // Erstellen des vereinfachten JSON-Payloads für MQTT (neues Format)
      let receiptData = customerReceiptData || {
        date: new Date().toLocaleString('de-DE'),
        terminalId: this.client.lastTerminalId || 'unbekannt',
        traceNumber: this.client.lastTraceNumber || '',
        receiptNumber: this.client.lastReceiptNumber || '',
        amount: this.client.lastAmount ? (this.client.lastAmount / 100).toFixed(2) + ' EUR' : '0.00 EUR',
        cardNumber: this.client.lastCardNumber ? `xxxx xxxx xxxx ${this.client.lastCardNumber}` : '',
        cardType: this.client.lastCardType || '',
        authCode: this.client.lastAuthCode || '',
        transactionId: this.client.lastTransactionId || 'unbekannt',
        status: 'Erfolgreich'
      };
      
      // Status auf 'success' überschreiben, unabhängig vom extrahierten Wert
      receiptData.status = 'success';
      
      const mqttPayload = {
        data: receiptData,
        raw_data: this.client.connection?.responseData ? Buffer.from(this.client.connection.responseData).toString('base64') : ''
      };

      // Senden des Payloads über MQTT
      global.mqttClient.publish(topic, mqttPayload)
        .then(() => {
          this.log.info(`EC-Beleg erfolgreich an Topic ${topic} gesendet`);
        })
        .catch(err => {
          this.log.error(`Fehler beim Senden des EC-Belegs an MQTT: ${err.message}`);
        });
    } catch (error) {
      this.log.error(`Fehler beim Versenden des EC-Belegs an MQTT: ${error.message}`);
    }
  }

  /**
   * Verarbeitet eine Zahlungsantwort und die zugehörigen Belege
   * @param {Object} paymentResult - Ergebnis der Zahlung mit Belegdaten
   */
  async processPaymentReceipts(paymentResult) {
    this.log.info("Verarbeite Zahlungsbelege");
    try {
      if (this.client.receiptOptions.customer_receipt && paymentResult.customerReceipt) {
        this.log.info("Verarbeite Kundenbeleg");
        await this._printReceipt(paymentResult.customerReceipt, "customer");
      }
      if (this.client.receiptOptions.merchant_receipt && paymentResult.merchantReceipt) {
        this.log.info("Verarbeite Händlerbeleg");
        await this._printReceipt(paymentResult.merchantReceipt, "merchant");
      }
      return true;
    } catch (error) {
      this.log.error("Fehler bei der Belegverarbeitung:", error.message);
      return false;
    }
  }

  /**
   * Druckt einen Beleg auf dem Epson-Drucker
   * @param {Array} receiptData - Die Belegdaten als Zeilen-Array
   * @param {string} type - Belegtyp ('customer' oder 'merchant')
   */
  async _printReceipt(receiptData, type) {
    if (!Array.isArray(receiptData) || receiptData.length === 0) {
      this.log.warn(`Keine Belegdaten für ${type} vorhanden`);
      return;
    }

    this.log.info(`${type === "customer" ? "Kunden" : "Händler"}-Beleg mit ${receiptData.length} Zeilen verarbeiten`);
    receiptData.forEach((line, index) => {
      this.log.info(`${type.toUpperCase()} BELEG [${index + 1}]: ${line}`);
    });

    // Detaillierte Diagnose des Epson-Druckers
    if (!this.epsonPrinter) {
      this.log.error("Epson-Drucker ist nicht gesetzt (null oder undefined)");
      return;
    }

    this.log.info("Epson-Drucker-Objekttyp: " + typeof this.epsonPrinter);
    this.log.info("Verfügbare Methoden: " + Object.keys(this.epsonPrinter).join(", "));

    if (typeof this.epsonPrinter.sendToPrinter !== "function") {
      this.log.error("sendToPrinter ist keine Funktion im Epson-Drucker-Objekt");

      // Alternative Druckmethoden versuchen
      if (typeof this.epsonPrinter.processPrintJobMessage === "function") {
        try {
          const epsonXml = this._createEpsonReceiptXml(this._fixCharacters(receiptData));
          this.log.info(`Versuche alternativen Druck mit processPrintJobMessage für ${type}-Beleg`);

          const printResult = await this.epsonPrinter.processPrintJobMessage({
            params: {
              print_data: epsonXml,
              print_job_id: "zvt-receipt-" + Date.now(),
            },
          });

          this.log.info(`Alternativer Druck Ergebnis: ${JSON.stringify(printResult)}`);
          return;
        } catch (altError) {
          this.log.error("Alternativer Druckversuch fehlgeschlagen:", altError.message);
        }
      }

      return;
    }

    try {
      // Text korrigieren und dann XML erstellen
      const fixedReceiptData = this._fixCharacters(receiptData);

      // Bei Tagesabschlussbeleg erkennen
      const isBatchReport = receiptData.some(line =>
        line.includes("TAGESABSCHLUSS") ||
        line.includes("Tagesabschluss") ||
        line.includes("Summen EUR") ||
        line.includes("GEBUCHT") ||
        line.includes("Gebucht")
      );

      if (isBatchReport) {
        // Einfaches XML für Tagesabschluss ohne Formatierung
        const simpleXml = this._createBatchReportXml(fixedReceiptData);

        this.log.info(`Sende ${type}-Beleg direkt an Epson-Drucker mit ${fixedReceiptData.length} Zeilen`);
        const printResult = await this.epsonPrinter.sendToPrinter(simpleXml);

        if (printResult && printResult.success) {
          this.log.info(`${type}-Beleg erfolgreich auf Epson gedruckt`);
        } else {
          this.log.error(`Fehler beim Drucken auf Epson: ${printResult ? printResult.error : "Unbekannter Fehler"}`);
        }
      } else {
        // Standard-Druck für normale Belege mit mehr Formatierung
        const epsonXml = this._createEpsonReceiptXml(fixedReceiptData);
        this.log.info(`Sende ${type}-Beleg an Epson-Drucker`);
        const printResult = await this.epsonPrinter.sendToPrinter(epsonXml);

        if (printResult && printResult.success) {
          this.log.info(`${type}-Beleg erfolgreich gedruckt`);
        } else {
          this.log.error(`Fehler beim Drucken des ${type}-Belegs:`, printResult ? printResult.error : "Unbekannter Fehler");
        }
      }
    } catch (error) {
      this.log.error(`Fehler beim Drucken des ${type}-Belegs:`, error.message);
    }
  }

  /**
   * Hilfsmethode zum Korrigieren von Zeichenkodierungsproblemen
   * @param {Array} lines - Die zu korrigierenden Zeilen
   * @returns {Array} Die korrigierten Zeilen
   * @private
   */
  _fixCharacters(lines) {
    if (!Array.isArray(lines)) return [];

    // Umlaute und Sonderzeichen korrigieren
    return lines.map(line => {
      if (typeof line !== 'string') return line;

      // Umlaute-Ersetzungstabelle für verschiedene Kodierungsprobleme
      const replacements = [
        // UTF-8 falsch interpretiert
        { pattern: /├ñ/g, replacement: 'ä' },
        { pattern: /├Ñ/g, replacement: 'Ä' },
        { pattern: /├╝/g, replacement: 'ü' },
        { pattern: /├£/g, replacement: 'Ü' },
        { pattern: /├Â/g, replacement: 'ö' },
        { pattern: /├û/g, replacement: 'Ö' },
        { pattern: /├ƒ/g, replacement: 'ß' },

        // Weitere häufige Fehlkodierungen
        { pattern: /Ã¤/g, replacement: 'ä' },
        { pattern: /Ã„/g, replacement: 'Ä' },
        { pattern: /Ã¼/g, replacement: 'ü' },
        { pattern: /Ãœ/g, replacement: 'Ü' },
        { pattern: /Ã¶/g, replacement: 'ö' },
        { pattern: /Ã–/g, replacement: 'Ö' },
        { pattern: /ÃŸ/g, replacement: 'ß' },

        // Latin-1 falsch interpretiert
        { pattern: /ä/g, replacement: 'ä' },
        { pattern: /Ä/g, replacement: 'Ä' },
        { pattern: /ü/g, replacement: 'ü' },
        { pattern: /Ü/g, replacement: 'Ü' },
        { pattern: /ö/g, replacement: 'ö' },
        { pattern: /Ö/g, replacement: 'Ö' },
        { pattern: /ß/g, replacement: 'ß' },

        // Fragezeichen-Ersetzungen für häufige Probleme
        { pattern: /f\?r/g, replacement: 'für' },
        { pattern: /H\?ndler/g, replacement: 'Händler' },
        { pattern: /K\?ufer/g, replacement: 'Käufer' },
        { pattern: /Danke f\?r/g, replacement: 'Danke für' },
        { pattern: /Best\?tigung/g, replacement: 'Bestätigung' },

        // Euro-Symbol
        { pattern: /Ôé¼/g, replacement: '€' },
        { pattern: /â‚¬/g, replacement: '€' },

        // Fehlerhafte Satzzeichen
        { pattern: /Ôêé/g, replacement: '"' },
        { pattern: /Ôêô/g, replacement: '"' }
      ];

      // Alle Ersetzungen anwenden
      let result = line;
      for (const { pattern, replacement } of replacements) {
        result = result.replace(pattern, replacement);
      }

      // Allgemeine Bereinigung für nicht druckbare Zeichen
      return result.replace(/[^\x20-\x7E\xC0-\xFF]/g, ' ');
    });
  }

  /**
   * Hilfsmethode zum Escapen von XML-Sonderzeichen
   * @param {string} text - Der zu escapende Text
   * @returns {string} Der escapte Text
   * @private
   */
  _escapeXml(text) {
    if (typeof text !== 'string') return '';

    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }

  /**
   * Erstellt XML für einen Tagesabschlussbeleg
   * @param {Array} receiptData - Die Belegdaten als Zeilen-Array
   * @returns {string} Epson-XML für den Tagesabschlussbeleg
   * @private
   */
  _createBatchReportXml(receiptData) {
    return '<epos-print xmlns="http://www.epson-pos.com/schemas/2011/03/epos-print">\n' +
      '<text font="font_a"/>\n' +
      '<text align="left"/>\n' +
      receiptData.map(line => `<text>${this._escapeXml(line)}&#10;</text>`).join('\n') +
      '\n<feed line="5"/><cut type="feed"/>\n' +
      '</epos-print>';
  }

  /**
   * Erstellt XML für den Epson-Drucker aus Belegdaten
   * @param {Array} receiptData - Die Belegdaten als Zeilen-Array
   * @returns {string} Epson-XML für den Beleg
   * @private
   */
  _createEpsonReceiptXml(receiptData) {
    let xml = '<epos-print xmlns="http://www.epson-pos.com/schemas/2011/03/epos-print">';
    xml += '<text reverse="false" ul="true" em="true" color="color_1"/>';
    xml += '<text font="font_a"/>';
    xml += '<text align="center"/>';
    xml += '<text reverse="false" ul="false" em="false" color="color_1"/>';
    xml += '<feed line="1"/>';
    xml += '<text align="left"/>';

    // Alle Zeilen aus den Belegdaten übernehmen
    for (const line of receiptData) {
      // Wichtige Beträge hervorheben
      if (
        line.includes("BETRAG") ||
        line.includes("SUMME") ||
        line.includes("AMOUNT") ||
        line.includes("TOTAL")
      ) {
        xml += '<text reverse="false" ul="false" em="true" color="color_1"/>';
        xml += `<text>${this._escapeXml(line)}&#10;</text>`;
        xml += '<text reverse="false" ul="false" em="false" color="color_1"/>';
      } else {
        xml += `<text>${this._escapeXml(line)}&#10;</text>`;
      }
    }

    xml += '<feed line="1"/>';
    xml += '<cut type="feed"/>';
    xml += "</epos-print>";
    return xml;
  }

  /**
   * Methode zum manuellen Drucken des Kundenbelegs
   * @returns {Promise<object>} Druckergebnis
   */
  async printCustomerReceipt() {
    this.log.info("Drucke Kundenbeleg auf Anforderung");
    this.log.info("Überprüfe Belegquellen:");

    // Umfangreiche Diagnose
    this.log.info(
      `- lastReceipts: ${this.lastReceipts ? "vorhanden" : "nicht vorhanden"}`
    );
    if (this.lastReceipts) {
      this.log.info(
        `- lastReceipts.customer: ${
          this.lastReceipts.customer
            ? this.lastReceipts.customer.length + " Zeilen"
            : "nicht vorhanden"
        }`
      );
    }
    this.log.info(
      `- receiptBuffer.customer: ${
        this.receiptBuffer && this.receiptBuffer.customer
          ? this.receiptBuffer.customer.length + " Zeilen"
          : "0 Zeilen"
      }`
    );

    // Versuche Beleg aus allen möglichen Quellen zu bekommen
    let receiptData = null;

    if (
      this.lastReceipts &&
      this.lastReceipts.customer &&
      this.lastReceipts.customer.length > 0
    ) {
      this.log.info("Verwende lastReceipts.customer");
      receiptData = this.lastReceipts.customer;
    } else if (
      this.receiptBuffer &&
      this.receiptBuffer.customer &&
      this.receiptBuffer.customer.length > 0
    ) {
      this.log.info("Verwende receiptBuffer.customer");
      receiptData = this.receiptBuffer.customer;
    } else {
      // Notfallbeleg erstellen
      this.log.warn("Keine Belegdaten gefunden, erstelle Notfallbeleg");
      receiptData = [
        "===========================================",
        "KARTENZAHLUNG - KUNDENBELEG",
        "===========================================",
        `Betrag: ${(this.client.lastAmount / 100).toFixed(2)} EUR`,
        `Datum: ${new Date().toLocaleString()}`,
        `Transaktions-ID: ${this.client.lastTransactionId || "k.A."}`,
        `Status: Erfolgreich`,
        "===========================================",
      ];
    }

    if (!receiptData || receiptData.length === 0) {
      this.log.warn("Kein Kundenbeleg zum Drucken vorhanden");
      return { success: false, error: "Kein Kundenbeleg verfügbar" };
    }

    try {
      // Jede Zeile des zu druckenden Belegs protokollieren
      receiptData.forEach((line, index) => {
        this.log.info(`CUSTOMER BELEG [${index + 1}]: ${line}`);
      });

      await this._printReceipt(receiptData, "customer");
      return { success: true };
    } catch (error) {
      this.log.error("Fehler beim Drucken des Kundenbelegs:", error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Methode zum manuellen Drucken des Händlerbelegs
   * @returns {Promise<object>} Druckergebnis
   */
  async printMerchantReceipt() {
    this.log.info("printMerchantReceipt wurde aufgerufen");
    if (!this.lastReceipts || !this.lastReceipts.merchant) {
      this.log.warn("lastReceipts.merchant ist nicht definiert oder null");
      return { success: false, error: "Kein Händlerbeleg verfügbar" };
    }
    if (
      !Array.isArray(this.lastReceipts.merchant) ||
      this.lastReceipts.merchant.length === 0
    ) {
      this.log.warn("Kein Händlerbeleg zum Drucken vorhanden (leeres Array)");
      return { success: false, error: "Kein Händlerbeleg verfügbar" };
    }
    try {
      this.log.info(
        `Drucke Händlerbeleg mit ${this.lastReceipts.merchant.length} Zeilen auf Anforderung`
      );
      await this._printReceipt(this.lastReceipts.merchant, "merchant");
      return { success: true };
    } catch (error) {
      this.log.error("Fehler beim Drucken des Händlerbelegs:", error.message);
      return { success: false, error: error.message };
    }
  }
}

module.exports = ZVTReceipt;