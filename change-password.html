<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Passwort ändern</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      overflow: hidden; 
    }
    body {
      font-family: Arial, sans-serif;
      padding: 0px;
      background-color: #f5f5f5;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
    }
    .container {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 20px;
      width: 350px;
      text-align: center;
    }
    h2 {
      margin-top: 0;
      color: #333;
    }
    .form-group {
      margin-bottom: 15px;
      text-align: left;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    .buttons {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
    }
    button {
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
      width: 48%;
    }
    #submit-btn {
      background-color: #4CAF50;
      color: white;
    }
    #cancel-btn {
      background-color: #f44336;
      color: white;
    }
    .message {
      margin-top: 15px;
      padding: 10px;
      border-radius: 4px;
      text-align: center;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>Administrator-Passwort ändern</h2>
    <form id="password-form">
      <div class="form-group">
        <label for="current-password">Aktuelles Passwort:</label>
        <input type="password" id="current-password" required>
      </div>
      <div class="form-group">
        <label for="new-password">Neues Passwort:</label>
        <input type="password" id="new-password" required>
      </div>
      <div class="form-group">
        <label for="confirm-password">Passwort bestätigen:</label>
        <input type="password" id="confirm-password" required>
      </div>
      
      <div id="message" class="message hidden"></div>
      
      <div class="buttons">
        <button type="button" id="cancel-btn">Abbrechen</button>
        <button type="submit" id="submit-btn">Passwort ändern</button>
      </div>
    </form>
  </div>

  <script>
    document.getElementById('password-form').addEventListener('submit', (event) => {
      event.preventDefault();
      
      const currentPassword = document.getElementById('current-password').value;
      const newPassword = document.getElementById('new-password').value;
      const confirmPassword = document.getElementById('confirm-password').value;
      const messageElement = document.getElementById('message');
      
      // Felder zurücksetzen
      messageElement.className = "message hidden";
      
      // Überprüfen, ob alle Felder ausgefüllt sind
      if (!currentPassword || !newPassword || !confirmPassword) {
        messageElement.textContent = "Bitte füllen Sie alle Felder aus.";
        messageElement.className = "message error";
        return;
      }
      
      // Überprüfen, ob das neue Passwort mit der Bestätigung übereinstimmt
      if (newPassword !== confirmPassword) {
        messageElement.textContent = "Die Passwörter stimmen nicht überein.";
        messageElement.className = "message error";
        return;
      }
      
      // Änderung an den Hauptprozess senden
      window.passwordApi.changePassword(currentPassword, newPassword);
    });

    document.getElementById('cancel-btn').addEventListener('click', () => {
      window.passwordApi.cancelDialog();
    });
    
    // Nachricht vom Hauptprozess anzeigen
    window.passwordApi.onChangeResult((result) => {
      const messageElement = document.getElementById('message');
      messageElement.textContent = result.message;
      
      if (result.success) {
        messageElement.className = "message success";
        
        // Alle Eingabefelder zurücksetzen
        document.getElementById('current-password').value = '';
        document.getElementById('new-password').value = '';
        document.getElementById('confirm-password').value = '';
        
        // Buttons deaktivieren
        document.getElementById('submit-btn').disabled = true;
        document.getElementById('cancel-btn').disabled = true;
      } else {
        messageElement.className = "message error";
      }
    });
  </script>
</body>
</html>
