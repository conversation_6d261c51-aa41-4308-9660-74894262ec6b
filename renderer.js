
document.addEventListener('DOMContentLoaded', async () => {
  const webview = document.getElementById('webview');
  const loading = document.getElementById('loading');
  const status = document.getElementById('status');
  
  // Modal-Elemente
  const paymentOverlay = document.getElementById('payment-overlay');
  const paymentStatus = document.getElementById('payment-status');
  const paymentAmount = document.getElementById('payment-amount');
  const paymentTransactionId = document.getElementById('payment-transaction-id');
  const paymentStatusText = document.getElementById('payment-status-text');
  const closeModalButton = document.getElementById('close-modal');
  const spinner = document.querySelector('.spinner');

  // Zahlungsstatus-Konstanten
  const PAYMENT_STATUS = {
    WAITING: 'waiting',
    PROCESSING: 'processing',
    SUCCESS: 'success',
    ERROR: 'error'
  };
  
  // Hilfsfunktion zum Aktualisieren des Status
  function updateStatus(message) {
    console.log(message);
    status.textContent = message;
  }
  
  // Hilfsfunktion zum Aktualisieren des Zahlungsmodals
  function updatePaymentModal(statusType, message, details = {}) {
    // Status-Klassen entfernen
    paymentStatus.classList.remove('status-waiting', 'status-processing', 'status-success', 'status-error');
    
    // Je nach Status-Typ anpassen
    switch (statusType) {
      case PAYMENT_STATUS.WAITING:
        paymentStatus.classList.add('status-waiting');
        spinner.style.display = 'inline-block';
        break;
      case PAYMENT_STATUS.PROCESSING:
        paymentStatus.classList.add('status-processing');
        spinner.style.display = 'inline-block';
        break;
      case PAYMENT_STATUS.SUCCESS:
        paymentStatus.classList.add('status-success');
        spinner.style.display = 'none';
        break;
      case PAYMENT_STATUS.ERROR:
        paymentStatus.classList.add('status-error');
        spinner.style.display = 'none';
        break;
    }
    
    // Text aktualisieren
    paymentStatus.textContent = message;
    
    // Details aktualisieren, falls vorhanden
    if (details.amount !== undefined) {
      const formattedAmount = (parseFloat(details.amount) / 100).toFixed(2);
      paymentAmount.textContent = `${formattedAmount} €`;
    }
    
    if (details.transactionId !== undefined) {
      paymentTransactionId.textContent = details.transactionId;
    }
    
    if (details.statusText !== undefined) {
      paymentStatusText.textContent = details.statusText;
    }
  }
  
  // Zahlungsmodal anzeigen
  function showPaymentModal(initialData = {}) {
    console.log('Zeige Zahlungs-Modal an:', initialData);
    
    const amount = initialData.amount || 0;
    const transactionId = initialData.transactionId || '-';
    
    // Modal vorbereiten
    updatePaymentModal(PAYMENT_STATUS.PROCESSING, 'Bitte Karte an Terminal halten oder einstecken...', {
      amount: amount,
      transactionId: transactionId,
      statusText: 'Warte auf Kartenzahlung'
    });
    
    // Modal anzeigen - WICHTIG: Immer sichtbar machen!
    paymentOverlay.classList.remove('hidden');
    
    // Sicherstellen, dass das Modal im Vordergrund ist
    paymentOverlay.style.zIndex = '2000';
    
    // Debug: Position und Sichtbarkeit prüfen
    console.log('Modal-Overlay CSS:', {
      display: window.getComputedStyle(paymentOverlay).display,
      visibility: window.getComputedStyle(paymentOverlay).visibility,
      zIndex: window.getComputedStyle(paymentOverlay).zIndex,
      opacity: window.getComputedStyle(paymentOverlay).opacity
    });
  }
  
  // Zahlungsmodal verbergen
  function hidePaymentModal() {
    console.log('Verberge Zahlungs-Modal');
    paymentOverlay.classList.add('hidden');
  }
  
  // Event-Listener für den Schließen-Button
  closeModalButton.addEventListener('click', () => {
    hidePaymentModal();
  });
  
  // VERBESSERT: IPC-Listener für Zahlungsereignisse mit Debug-Logging
  window.api.onZVTPaymentStarted((data) => {
    console.log('ZVT-Zahlung gestartet Event empfangen:', data);
    
    // Tagesabschluss spezielle Anzeige
    if (data.isEndOfDay) {
      updatePaymentModal(PAYMENT_STATUS.PROCESSING, 'Tagesabschluss wird durchgeführt...', {
        statusText: 'Tagesabschluss',
        transactionId: data.transactionId || 'Tagesabschluss'
      });
    } else {
      // Normale Zahlung
      showPaymentModal(data);
    }
  });
  
  window.api.onZVTPaymentUpdate((data) => {
    console.log('ZVT-Zahlungs-Update Event empfangen:', data);
    
    // Sicherstellen, dass das Modal sichtbar ist
    if (paymentOverlay.classList.contains('hidden')) {
      paymentOverlay.classList.remove('hidden');
    }
    
    // Status basierend auf dem Update aktualisieren
    if (data.status === 'processing') {
      updatePaymentModal(PAYMENT_STATUS.PROCESSING, data.message || 'Zahlung wird verarbeitet...', {
        statusText: data.statusText || 'In Bearbeitung'
      });
    } else if (data.status === 'success') {
      updatePaymentModal(PAYMENT_STATUS.SUCCESS, data.message || 'Zahlung erfolgreich!', {
        statusText: data.statusText || 'Erfolgreich'
      });
      
      // Optional: Modal nach Erfolg automatisch nach X Sekunden schließen
      // setTimeout(hidePaymentModal, 5000);
    } else if (data.status === 'error') {
      updatePaymentModal(PAYMENT_STATUS.ERROR, data.message || 'Fehler bei der Zahlung!', {
        statusText: data.statusText || 'Fehlgeschlagen'
      });
    }
  });
  
  window.api.onZVTPaymentResult((result) => {
    console.log('ZVT-Zahlungsergebnis Event empfangen:', result);
    
    // Sicherstellen, dass das Modal sichtbar ist
    if (paymentOverlay.classList.contains('hidden')) {
      paymentOverlay.classList.remove('hidden');
    }
    
    if (result.success) {
      updatePaymentModal(PAYMENT_STATUS.SUCCESS, 'Zahlung erfolgreich abgeschlossen!', {
        statusText: 'Erfolgreich',
        // Wenn Betrag und Transaktions-ID im Ergebnis enthalten sind
        amount: result.amount,
        transactionId: result.transactionId
      });
    } else {
      updatePaymentModal(PAYMENT_STATUS.ERROR, `Fehler: ${result.error || 'Unbekannter Fehler'}`, {
        statusText: 'Fehlgeschlagen',
        // Wenn Betrag und Transaktions-ID im Ergebnis enthalten sind
        amount: result.amount,
        transactionId: result.transactionId
      });
    }
  });
  
  try {
    updateStatus('Verbinde mit API...');
    
    // URL aus der API über IPC abrufen
    const posUrl = await window.api.getPosUrl();
    updateStatus(`API-URL erhalten: ${posUrl}`);
    
    if (!posUrl) {
      throw new Error('Keine URL von der API erhalten');
    }

    // Webview vorbereiten
    updateStatus('Webview wird vorbereitet...');
    
    // Explizit die URL setzen
    webview.src = posUrl;
    
    // Webview-Events für besseres Debugging
    webview.addEventListener('did-start-loading', () => {
      updateStatus(`Lade Seite: ${posUrl}`);
      loading.style.display = 'flex';
    });

    webview.addEventListener('did-finish-load', () => {
      updateStatus('Seite geladen!');
      // Kurze Verzögerung, um sicherzustellen, dass alles richtig geladen ist
      setTimeout(() => {
        loading.classList.add('hidden');
      }, 1000);
    });

    webview.addEventListener('did-fail-load', (event) => {
      const { errorCode, errorDescription, validatedURL } = event;
      if (errorCode !== -3) { // -3 ist Abbruch (oft normal)
        updateStatus(`Fehler beim Laden (${errorCode}): ${errorDescription}`);
        console.error('Ladefehler:', { errorCode, errorDescription, validatedURL });
      }
    });

    webview.addEventListener('dom-ready', () => {
      // Debug: DOM ist bereit, Inhalt überprüfen
      updateStatus('Webview DOM bereit, injiziere Kommunikation...');
      
      // Öffne DevTools für Webview zum Debuggen
      // webview.openDevTools();
      
      // Code für die Kommunikation injizieren
      webview.executeJavaScript(`
        console.log('Shop Integrationsscript geladen');
        
        // Event-Listener für Shop-Daten
        document.addEventListener('shop-transaction', (event) => {
          console.log('Shop-Transaktion erkannt:', event.detail);
          window.postMessage({type: 'shop-data', data: event.detail}, '*');
        });

        // Kommunikation über postMessage
        window.addEventListener('message', (event) => {
          console.log('PostMessage empfangen:', event.data);
          if (event.data && event.data.type === 'shop-data') {
            try {
              window.ipcRenderer.sendToHost('shop-data', event.data.data);
            } catch (err) {
              console.error('Kommunikationsfehler:', err);
            }
          }
        });

        // Signal, dass alles bereit ist
        document.body.classList.add('pos-ready');
        console.log('POS Integration abgeschlossen');
        
        // Signalisiere Erfolg
        true;
      `)
      .then(result => {
        updateStatus('Kommunikation eingerichtet, Seite wird angezeigt...');
        setTimeout(() => {
          loading.classList.add('hidden');
        }, 500);
      })
      .catch(err => {
        updateStatus(`Fehler bei JS-Injektion: ${err.message}`);
        console.error('JS-Injektionsfehler:', err);
      });
    });

    // Empfangen von Daten vom Shop
    webview.addEventListener('ipc-message', (event) => {
      if (event.channel === 'shop-data') {
        // Verarbeite Daten vom Shop
        const shopData = event.args[0];
        console.log('Daten vom Shop empfangen:', shopData);
        
        // Weiterleitung an das Hauptfenster
        window.api.sendPaymentData(shopData);
      }
    });

    // Fehler-Event für Webview
    webview.addEventListener('crashed', () => {
      updateStatus('Webview ist abgestürzt! Starte die App neu.');
    });
    
    webview.addEventListener('plugin-crashed', () => {
      updateStatus('Ein Plugin ist abgestürzt!');
    });
    
    webview.addEventListener('destroyed', () => {
      updateStatus('Webview wurde zerstört!');
    });

  } catch (error) {
    console.error('Fehler beim Laden der URL:', error);
    updateStatus(`Kritischer Fehler: ${error.message}`);
  }
});