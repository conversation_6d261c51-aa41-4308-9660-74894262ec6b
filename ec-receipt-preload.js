const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Sichere Schnittstelle für die Kommunikation mit dem Hauptprozess
contextBridge.exposeInMainWorld('ecReceiptApi', {
  // Konfiguration vom Hauptprozess empfangen
  onLoadConfig: (callback) => {
    ipcRenderer.on('load-config', (event, data) => {
      console.log('Konfiguration vom Hauptprozess empfangen:', data);
      console.log('ZVT-Konfiguration:', data.zvt_config);
      callback(data);
    });
  },

  // Bestätigung nach dem Speichern
  onConfigSaved: (callback) => {
    ipcRenderer.on('config-saved', () => {
      console.log('Bestätigung nach dem Speichern empfangen');
      callback();
    });
  },

  // Konfiguration speichern
  saveConfig: (config) => {
    console.log('Sende Konfiguration zum Speichern:', config);
    console.log('ZVT-Konfiguration:', config.zvt_config);
    ipcRenderer.send('save-ec-receipt-config', config);
  },

  // Editor schließen
  closeEditor: () => {
    ipcRenderer.send('close-ec-receipt-editor');
  }
});
