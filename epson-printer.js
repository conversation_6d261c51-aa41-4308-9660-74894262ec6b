/*
  Verarbeitet sowohl XML- als auch FGL-Daten (FGL-Code)
  Falls beide Datenfelder vorhanden sind, wird zuerst die XML-Drucklogik
  (über printXML) und anschließend die FGL-Drucklogik (über printFGL) ausgeführt.
*/

const net = require('net');
const https = require('https');
const path = require('path');
const fs = require('fs');
const loggerService = require('./logger-service');
const log = loggerService.getModuleLogger('EpsonClient');
const { exec, execFile } = require('child_process');

class EpsonPrinter {
  constructor(config) {
    this.config = config;
    this.apiKey = config.api.authKey;
    this.apiUrl = config.api.configUrl.replace(/\/config$/, '/print_jobs/');
    this.printerIp = "";
    this.printerPort = "8008"; // Standard-ePOS-Port
    this.connected = false;

    // Drucker-IP aus der Konfiguration extrahieren
    if (config.printer && config.printer.epson && config.printer.epson.ip) {
      this.printerIp = config.printer.epson.ip;
      if (config.printer.epson.port) {
        this.printerPort = config.printer.epson.port;
      }
    } else if (config.Print && config.Print.RecipePrinterIp) {
      this.printerIp = config.Print.RecipePrinterIp;
    } else if (config.print && config.print.recipe_printer_ip) {
      this.printerIp = config.print.recipe_printer_ip;
    } else if (config.zvt_config && config.zvt_config.zvt_ip) {
      log.warn('Keine spezifische Drucker-IP gefunden, verwende ZVT-IP als Basis');
      const ipParts = config.zvt_config.zvt_ip.split('.');
      if (ipParts.length === 4) {
        ipParts[3] = "42";
        this.printerIp = ipParts.join('.');
        log.warn(`Konstruierte Drucker-IP: ${this.printerIp} (basierend auf ZVT-IP)`);
      }
    }

    log.info('Epson-Drucker initialisiert mit IP:', this.printerIp, 'und Port:', this.printerPort);
  }

  // XML-Daten drucken
  async printXML(xmlData) {
    try {
      log.info('Verwende direkte HTTP-Kommunikation für den XML-Druck');
      return await this.printXMLFallback(xmlData);
    } catch (error) {
      log.error('Unerwarteter Fehler beim XML-Druck:', error);
      return { success: false, error: error.message };
    }
  }

  // Direkte HTTP-Methode zum Drucken von XML-Daten
  async printXMLFallback(xmlData) {
    if (!this.printerIp) {
      const error = 'Keine Drucker-IP konfiguriert';
      log.error(error);
      return { success: false, error };
    }

    // XML-Daten vorbereiten
    const preparedXml = this.prepareXml(xmlData);

    // SOAP-Envelope erstellen
    const soapEnvelope = `<?xml version="1.0" encoding="UTF-8"?><s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"><s:Body>${preparedXml}</s:Body></s:Envelope>`;
    const xmlBuffer = Buffer.from(soapEnvelope, 'utf8');

    // Mögliche Ports prüfen (80, 8008, 8043)
    const portsToTry = ['80', '8008', '8043'];

    for (const port of portsToTry) {
      try {
        const result = await this.sendToPort(port, xmlBuffer);
        if (result.success) {
          return result;
        }
      } catch (error) {
        log.error(`Fehler beim Senden an Port ${port}:`, error.message);
      }
    }

    log.error('Alle Ports-Versuche fehlgeschlagen - der Drucker ist möglicherweise nicht erreichbar');
    return { success: false, error: 'Drucker nicht erreichbar auf allen Ports' };
  }

  // Sendet Daten an einen bestimmten Port
  sendToPort(port, xmlBuffer) {
    return new Promise((resolve, reject) => {
      const portSegment = (port !== "80") ? `:${port}` : "";
      const url = `http://${this.printerIp}${portSegment}/cgi-bin/epos/service.cgi?devid=local_printer&timeout=200000`;

      log.info(`Versuche Druckauftrag über HTTP an: ${url}`);

      const options = {
        method: 'POST',
        headers: {
          'Content-Type': 'text/xml; charset=utf-8',
          'SOAPAction': '""',
          'If-Modified-Since': 'Thu, 01 Jan 1970 00:00:00 GMT',
          'Content-Length': xmlBuffer.length
        }
      };

      const req = require('http').request(url, options, (res) => {
        let responseData = '';
        res.on('data', (chunk) => { responseData += chunk; });
        res.on('end', () => {
          log.info(`Antwort vom Drucker (Port ${port}) erhalten:`, responseData);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve({ success: true, response: responseData });
          } else {
            const errorMsg = `Fehler vom Drucker: HTTP-Status ${res.statusCode}`;
            log.error(errorMsg, responseData);
            reject(new Error(errorMsg));
          }
        });
      });

      req.setTimeout(200000);

      req.on('timeout', () => {
        log.error(`Timeout beim Warten auf Antwort vom Drucker (Port ${port})`);
        req.abort();
        reject(new Error(`Timeout beim Warten auf Antwort vom Drucker (Port ${port})`));
      });

      req.on('error', (err) => {
        log.error(`Fehler beim Senden der Daten an den Drucker (Port ${port})`, err);
        reject(err);
      });

      req.write(xmlBuffer);
      req.end();
    });
  }

  // Ersetzt escaped Anführungszeichen durch normale Anführungszeichen
  unescapeXml(str) {
    return str.replace(/\\"/g, '"');
  }

  // Bereitet das XML vor
  prepareXml(xmlData) {
    let fixedXml = xmlData;
    fixedXml = fixedXml.replace(/\\"/g, '"');
    try {
      const buffer = Buffer.from(fixedXml, 'utf8');
      fixedXml = buffer.toString('utf8');
    } catch (error) {
      log.warn('Fehler bei der Zeichenkonvertierung:', error);
    }
    log.info("XML, das an den Drucker gesendet wird:", fixedXml);
    return fixedXml;
  }

  // Prüft, ob der Drucker erreichbar ist
  async checkPrinterConnection() {
    if (!this.printerIp) {
      log.error('Keine Drucker-IP konfiguriert');
      return false;
    }

    try {
      const isWindows = process.platform === 'win32';
      const pingCmd = isWindows ? `ping -n 1 -w 1000 ${this.printerIp}` : `ping -c 1 ${this.printerIp}`;
      log.info(`Führe Ping-Befehl aus: ${pingCmd}`);

      return new Promise((resolve) => {
        exec(pingCmd, (error, stdout, stderr) => {
          if (error) {
            log.error(`Ping-Befehl fehlgeschlagen: ${pingCmd}`, error.message);
            this.testHttpConnection()
              .then((httpSuccess) => {
                if (httpSuccess) {
                  log.info(`HTTP-Verbindung zum Drucker erfolgreich: ${this.printerIp}`);
                  resolve(true);
                } else {
                  log.error(`Keine HTTP-Verbindung zum Drucker möglich: ${this.printerIp}`);
                  resolve(false);
                }
              })
              .catch(httpError => {
                log.error(`Auch HTTP-Test fehlgeschlagen: ${httpError.message}`);
                resolve(false);
              });
          } else {
            log.info(`Drucker erreichbar via Ping: ${this.printerIp}`);
            log.info(`Ping-Antwort: ${stdout}`);
            resolve(true);
          }
        });
      });
    } catch (error) {
      log.error('Fehler bei der Drucker-Verbindungsprüfung:', error);
      return false;
    }
  }

  // Alternative Methode zur HTTP-Verbindungsprüfung
  async testHttpConnection() {
    if (!this.printerIp) {
      log.error('Keine Drucker-IP konfiguriert');
      return false;
    }

    const portsToTry = ['80', '8008', '8043'];

    for (const port of portsToTry) {
      try {
        await this.testPortConnection(port);
        log.info(`Drucker auf Port ${port} erreichbar`);
        return true; // Erfolgreich verbunden
      } catch (error) {
        log.warn(`HTTP-Verbindungstest fehlgeschlagen auf Port ${port}: ${error.message}`);
        // Weiter mit dem nächsten Port
      }
    }

    // Wenn wir hier ankommen, waren alle Verbindungsversuche erfolglos
    log.error('Keine HTTP-Verbindung zum Drucker möglich auf allen Ports');
    return false;
  }

  // Testet die Verbindung zu einem bestimmten Port
  testPortConnection(port) {
    return new Promise((resolve, reject) => {
      const portSegment = (port !== "80") ? `:${port}` : "";
      const url = `http://${this.printerIp}${portSegment}/cgi-bin/epos/service.cgi?devid=local_printer&timeout=20000`;
      log.info(`Teste HTTP-Verbindung zu: ${url}`);
      const req = require('http').get(url, (res) => {
        log.info(`HTTP-Verbindungstest auf Port ${port}: Status ${res.statusCode}`);
        res.on('data', () => {});
        res.on('end', () => {
          resolve();
        });
      });
      req.setTimeout(20000);
      req.on('timeout', () => {
        log.error(`HTTP-Verbindung Timeout auf Port ${port}`);
        req.abort();
        reject(new Error(`Timeout bei HTTP-Verbindung auf Port ${port}`));
      });
      req.on('error', (err) => {
        reject(err);
      });
    });
  }

  // Methode zum Drucken von FGL-Daten über das Python-Skript
  printFGL(rawData) {
    return new Promise((resolve, reject) => {
      // Pfad zum Python-Skript - berücksichtige app.asar.unpacked
      let scriptPath;

      // Wenn die App gepackt ist (in einer ASAR-Datei)
      if (process.resourcesPath) {
        // Für gepackte App: Suche in app.asar.unpacked
        const appPath = process.resourcesPath.replace('resources', 'resources\\app.asar.unpacked');
        scriptPath = path.join(appPath, "Direct_TicketPrinter.py");

        // Fallback, falls der Pfad nicht existiert
        if (!fs.existsSync(scriptPath)) {
          log.warn(`Python-Skript nicht gefunden unter: ${scriptPath}, versuche alternativen Pfad`);
          scriptPath = path.join(__dirname, "Direct_TicketPrinter.py");
        }
      } else {
        // Für Entwicklungsumgebung
        scriptPath = path.join(__dirname, "Direct_TicketPrinter.py");
      }

      // Druckername aus der API-Konfiguration oder Standardwert
      const printerName = (this.config.apiConfig &&
                           this.config.apiConfig.print &&
                           this.config.apiConfig.print.ticket_printer_name)
        ? this.config.apiConfig.print.ticket_printer_name
        : "Boca BIDI FGL 26/46 200 DPI";

      log.info(`Starte FGL-Druck mit Drucker: ${printerName}`);
      log.info(`Verwende Python-Skript: ${scriptPath}`);

      execFile('python', [scriptPath, '--printer_name', printerName, '--raw_data', rawData], (error, stdout, stderr) => {
        if (error) {
          log.warn("Fehler beim Drucken mit FGL:", error);
          return reject(error);
        }
        log.info("FGL-Druckergebnis:", stdout);
        resolve({ success: true, response: stdout });
      });
    });
  }

  // Verarbeitung der Druckjob-Nachricht von MQTT – beide Datenfelder werden genutzt
  async processPrintJobMessage(message) {
    try {
      log.info('Verarbeite PrintJob-Nachricht:', JSON.stringify(message, null, 2));
      if (message.params && message.params.status === 'finished' && message.params.print_job_id) {
        const printJobId = message.params.print_job_id;
        log.info('Fertiger Druckjob erkannt, ID:', printJobId);

        const connected = await this.checkPrinterConnection();
        if (!connected) {
          log.warn('Drucker scheint nicht erreichbar zu sein, versuche trotzdem zu drucken');
        }

        try {
          const printJobDetails = await this.fetchPrintJobDetails(printJobId);
          let xmlResult = { success: true, message: "Keine XML-Daten vorhanden" };
          let fglResult = { success: true, message: "Keine FGL-Daten vorhanden" };

          // XML-Daten drucken, falls vorhanden
          if (printJobDetails && printJobDetails.xml) {
            log.info(`Druckjob XML-Daten erhalten, Länge: ${printJobDetails.xml.length} Zeichen`);
            xmlResult = await this.printXML(printJobDetails.xml);
          }

          // FGL-Daten drucken, falls vorhanden
          if (printJobDetails && printJobDetails.fgl && printJobDetails.fgl.length > 0) {
            const fglData = printJobDetails.fgl.join("\n");
            log.info("FGL-Druckjob erkannt, beginne mit FGL-Druck");
            fglResult = await this.printFGL(fglData);
          }

          // Ergebnisse beider Druckaufträge zusammenfassen
          const result = {
            xmlResult,
            fglResult
          };
          return result;
        } catch (fetchError) {
          log.error('Fehler beim Abrufen der Druckjob-Details:', fetchError.message);
          return { success: false, error: fetchError.message };
        }
      } else {
        const warnMsg = 'Ignoriere Druckjob-Nachricht, da kein fertiger Status oder keine ID vorhanden ist';
        log.warn(warnMsg);
        return { success: false, error: warnMsg };
      }
    } catch (error) {
      log.error('Fehler bei der Verarbeitung des Druckjobs:', error);
      return { success: false, error: error.message };
    }
  }

  // Druckjob-Details von der API abrufen
  async fetchPrintJobDetails(printJobId) {
    try {
      const url = this.apiUrl + printJobId;
      log.info('Rufe Druckjob-Details ab von:', url);

      return new Promise((resolve, reject) => {
        const options = {
          method: 'GET',
          headers: {
            'Authorization': this.apiKey
          }
        };

        const req = https.request(url, options, (res) => {
          let data = '';
          res.on('data', (chunk) => { data += chunk; });
          res.on('end', () => {
            if (res.statusCode === 200) {
              try {
                const printJob = JSON.parse(data);
                log.info('Druckjob-Details erfolgreich abgerufen');
                resolve(printJob);
              } catch (error) {
                log.error('Fehler beim Parsen der Druckjob-Details:', error);
                reject(error);
              }
            } else {
              log.error('API-Anfrage für Druckjob fehlgeschlagen mit Status:', res.statusCode);
              reject(new Error(`API-Anfrage fehlgeschlagen mit Status: ${res.statusCode}`));
            }
          });
        });

        req.on('error', (error) => {
          log.error('Fehler bei der API-Anfrage für Druckjob:', error);
          reject(error);
        });

        req.setTimeout(30000);

        req.on('timeout', () => {
          log.error('Timeout bei der API-Anfrage für Druckjob');
          req.destroy();
          reject(new Error('Timeout bei der API-Anfrage für Druckjob'));
        });

        req.end();
      });
    } catch (error) {
      log.error('Unerwarteter Fehler beim Abrufen der Druckjob-Details:', error);
      throw error;
    }
  }

  // Ressourcen freigeben
  dispose() {
    try {
      this.connected = false;
      log.info('Drucker-Ressourcen freigegeben');
    } catch (error) {
      log.error('Fehler beim Freigeben der Drucker-Ressourcen:', error);
    }
  }
}

module.exports = EpsonPrinter;
