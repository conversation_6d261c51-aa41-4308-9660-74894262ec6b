/**
 * ZVT-Client über TCP für EC-Terminals
 */
const ZVTConnection = require('./zvt-connection');
const ZVTCommands = require('./zvt-commands');
const ZVTParser = require('./zvt-parser');
const ZVTReceipt = require('./zvt-receipt');
const ZVTPayment = require('./zvt-payment');
const ZVTConstants = require('./zvt-constants');
const loggerService = require('./logger-service');

class ZVTClient {
  constructor(config = {}) {
    this.config = config;
    this.zvtConfig = config.zvt_config || {};
    this.log = loggerService.getModuleLogger("ZVTClient");

    // Detaillierte Logging der ZVT-Konfiguration
    this.log.info("ZVT-Client wird initialisiert mit Konfiguration:");
    this.log.info(`- zvt_ip: ${this.zvtConfig.zvt_ip || 'nicht definiert'}`);
    this.log.info(`- zvt_port: ${this.zvtConfig.zvt_port || 'nicht definiert'}`);
    this.log.info(`- is_tlv: ${this.zvtConfig.is_tlv ? 'ja' : 'nein'}`);
    this.log.info(`- merchant_name: ${this.zvtConfig.merchant_name || 'nicht definiert'}`);
    this.log.info(`- merchant_address: ${this.zvtConfig.merchant_address || 'nicht definiert'}`);
    this.log.info(`- merchant_zip_city: ${this.zvtConfig.merchant_zip_city || 'nicht definiert'}`);
    this.log.info(`- merchant_line1: ${this.zvtConfig.merchant_line1 || 'nicht definiert'}`);
    this.log.info(`- merchant_line2: ${this.zvtConfig.merchant_line2 || 'nicht definiert'}`);


    this.receiptOptions = {
      customer_receipt: false,
      merchant_receipt: false,
      ...(config.zvt_terminal || {}),
    };

    // Automatischen Händlerbelegdruck aus der Konfiguration übernehmen
    this.autoMerchantReceipt = this.zvtConfig.auto_merchant_receipt === true;

    // Zustandsvariablen
    this.lastTerminalId = null;
    this.lastTraceNumber = null;
    this.lastCardType = null;
    this.lastCardNumber = null;
    this.lastAuthCode = null;
    this.lastReceiptNumber = null;
    this.lastError = null;
    this.lastTransactionId = null;
    this.lastAmount = null;
    this.lastEmvData = null;
    this.lastVUNumber = null;
    this.mainWindow = null;

    // TLV (Tag-Length-Value) Parsing aktivieren, wenn in Konfiguration angegeben
    this.useTLV = this.zvtConfig.is_tlv === true;

    // Module initialisieren
    this.constants = new ZVTConstants();
    this.connection = new ZVTConnection(this, this.zvtConfig);
    this.commands = new ZVTCommands(this, this.zvtConfig);
    this.parser = new ZVTParser(this, this.constants);

    // Für die ZVTReceipt-Klasse die gesamte Konfiguration übergeben, damit die Händlerdaten und MQTT-Konfiguration verfügbar sind
    this.receipt = new ZVTReceipt(this, this.config);
    this.payment = new ZVTPayment(this);

    // Prüfen, ob ZVT-Konfiguration vorhanden ist
    if (!this.zvtConfig.zvt_ip || !this.zvtConfig.zvt_port) {
      this.log.warn(
        "ZVT-Konfiguration unvollständig, Terminal wird nicht verwendet"
      );
      return;
    }

    // Starte mit sauberen responseData Buffer
    this.connection.resetResponseBuffer();

    this.log.info(
      "ZVT-Client initialisiert mit IP:",
      this.zvtConfig.zvt_ip,
      "Port:",
      this.zvtConfig.zvt_port
    );
    this.log.info("Beleg-Optionen:", JSON.stringify(this.receiptOptions));
    this.log.info("TLV-Verarbeitung:", this.useTLV ? "aktiviert" : "deaktiviert");

    // Versuche, die globale Referenz zum Hauptfenster zu erhalten
    if (global.mainWindow) {
      this.mainWindow = global.mainWindow;
      this.log.info("Hauptfenster-Referenz erfolgreich erhalten");
    } else {
      this.log.warn("Keine Hauptfenster-Referenz verfügbar");
    }
  }

  /**
   * Sendet eine Nachricht an das Hauptfenster
   * @param {string} channel - Der Event-Kanal
   * @param {Object} data - Die zu sendenden Daten
   * @private
   */
  _sendToWindow(channel, data) {
    try {
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send(channel, data);
        this.log.info(`Nachricht an Hauptfenster gesendet: ${channel}`);
        return true;
      }
      if (global.mainWindow && !global.mainWindow.isDestroyed()) {
        global.mainWindow.webContents.send(channel, data);
        this.log.info(`Nachricht über globale Referenz gesendet: ${channel}`);
        return true;
      }
      try {
        const { BrowserWindow } = require("electron");
        const windows = BrowserWindow.getAllWindows();
        if (windows.length > 0) {
          const mainWin = windows[0];
          mainWin.webContents.send(channel, data);
          this.log.info(`Nachricht über getAllWindows gesendet: ${channel}`);
          return true;
        }
      } catch (electronError) {
        this.log.warn(
          `Electron BrowserWindow nicht verfügbar: ${electronError.message}`
        );
      }
      this.log.warn(`Keine Möglichkeit, Nachricht zu senden: ${channel}`);
      return false;
    } catch (error) {
      this.log.error(
        `Fehler beim Senden der Nachricht (${channel}):`,
        error.message
      );
      return false;
    }
  }

  /**
   * Setzt das Hauptfenster für UI-Benachrichtigungen
   * @param {BrowserWindow} window - Das Electron-Hauptfenster
   */
  setMainWindow(window) {
    this.mainWindow = window;
    this.log.info("Hauptfenster für UI-Benachrichtigungen gesetzt");
  }

  /**
   * Setzt den Epson-Drucker für die direkte Belegausgabe
   * @param {Object} printer - Epson-Drucker-Instanz
   */
  setEpsonPrinter(printer) {
    this.receipt.setEpsonPrinter(printer);
  }

  /**
   * Verbindung zum Terminal herstellen
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async connect() {
    return this.connection.connect();
  }

  /**
   * Abmeldung vom Terminal
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async disconnect() {
    return this.connection.disconnect();
  }

  /**
   * Zahlung mit dem Terminal durchführen
   * @param {number} amount Betrag in Cent
   * @param {string} transactionId ID der Transaktion
   * @returns {Promise<object>} Ergebnis der Zahlung
   */
  async processPayment(amount, transactionId) {
    return this.payment.processPayment(amount, transactionId);
  }

  /**
   * Bricht eine laufende Zahlung ab
   * @returns {Promise<boolean>} True wenn erfolgreich, sonst False
   */
  async abortPayment() {
    return this.payment.abortPayment();
  }

  /**
 * Führt einen Tagesabschluss direkt am Terminal durch
 * @returns {Promise<object>} Ergebnis des Tagesabschlusses
 */
async performDirectEndOfDay() {
    return this.payment.performDirectEndOfDay();
  }

  /**
   * Methode zum manuellen Drucken des Kundenbelegs
   * @returns {Promise<object>} Druckergebnis
   */
  async printCustomerReceipt() {
    return this.receipt.printCustomerReceipt();
  }

  /**
   * Methode zum manuellen Drucken des Händlerbelegs
   * @returns {Promise<object>} Druckergebnis
   */
  async printMerchantReceipt() {
    return this.receipt.printMerchantReceipt();
  }

  /**
   * Verarbeitet MQTT-Nachrichten und startet ggf. eine Kartenzahlung
   * @param {object} mqttMessage Die MQTT-Nachricht
   * @returns {Promise<object|null>} Ergebnis der Zahlungsverarbeitung oder null
   */
  async handleMqttMessage(mqttMessage) {
    return this.payment.handleMqttMessage(mqttMessage);
  }

  /**
   * Prüft, ob das Terminal momentan beschäftigt ist
   * @returns {boolean} True wenn beschäftigt, sonst False
   */
  isBusy() {
    return this.connection.busy || this.payment.paymentInProgress;
  }

  /**
   * Gibt den letzten Fehler zurück
   * @returns {string|null} Fehlermeldung oder null
   */
  getLastError() {
    return this.lastError;
  }
}

module.exports = ZVTClient;